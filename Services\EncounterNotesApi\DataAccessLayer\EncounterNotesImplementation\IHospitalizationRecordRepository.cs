﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IHospitalizationRecordRepository : IShardGenericRepository<HospitalizationRecord>
    {
        Task<List<HospitalizationRecord>> GetHospitalizationRecordById(Guid recordId, Guid OrgId, bool Subscription);
    }
}