using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class DiagnosticImagingCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<DiagnosticImagingCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, DiagnosticImage, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IDiagnosticImagingRepository> _diagnosticImagingRepositoryMock;
        private DiagnosticImagingCommandHandler _diagnosticImagingCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<DiagnosticImagingCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, DiagnosticImage, EncounterDataAccessLayer>>();
            _diagnosticImagingRepositoryMock = new Mock<IDiagnosticImagingRepository>();

            _unitOfWorkMock.Setup(u => u.DiagnosticImagingRepository).Returns(_diagnosticImagingRepositoryMock.Object);

            _diagnosticImagingCommandHandler = new DiagnosticImagingCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task AddDiagnosticImaging_ValidDiagnosticImages_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var diagnosticImages = new List<DiagnosticImage>
            {
                new DiagnosticImage
                {
                    RecordID = Guid.NewGuid(),
                    OrganizationID = _orgId,
                    PatientId = Guid.NewGuid(),
                    DiCompany = "Test Company",
                    Type = "X-Ray",
                    Lookup = "Chest",
                    OrderName = "Chest X-Ray",
                    StartsWith = "C",
                    IsActive = true,
                    CreatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now,
                    Subscription = _subscription
                }
            };

            // Act
            await _diagnosticImagingCommandHandler.AddDiagnosticImaging(diagnosticImages, _orgId, _subscription);

            // Assert
            _diagnosticImagingRepositoryMock.Verify(r => r.AddAsync(diagnosticImages, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateDiagnosticImaging_ValidDiagnosticImage_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var diagnosticImage = new DiagnosticImage
            {
                RecordID = Guid.NewGuid(),
                OrganizationID = _orgId,
                PatientId = Guid.NewGuid(),
                DiCompany = "Updated Company",
                Type = "Updated Type",
                Lookup = "Updated Lookup",
                OrderName = "Updated Order Name",
                StartsWith = "U",
                IsActive = true,
                UpdatedBy = Guid.NewGuid(),
                UpdatedDate = DateTime.Now,
                Subscription = _subscription
            };

            // Act
            await _diagnosticImagingCommandHandler.UpdateDiagnosticImaging(diagnosticImage, _orgId, _subscription);

            // Assert
            _diagnosticImagingRepositoryMock.Verify(r => r.UpdateAsync(diagnosticImage, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteDiagnosticImagingById_ValidId_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var id = Guid.NewGuid();

            // Act
            await _diagnosticImagingCommandHandler.DeleteDiagnosticImagingById(id, _orgId, _subscription);

            // Assert
            _diagnosticImagingRepositoryMock.Verify(r => r.DeleteByIdAsync(id, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateDiagnosticImagingList_ValidDiagnosticImages_ShouldUpdateExistingRecordsAndSave()
        {
            // Arrange
            var existingRecords = new List<DiagnosticImage>
            {
                new DiagnosticImage
                {
                    RecordID = Guid.NewGuid(),
                    OrganizationID = _orgId,
                    PatientId = Guid.NewGuid(),
                    DiCompany = "Original Company",
                    Type = "Original Type",
                    Lookup = "Original Lookup",
                    OrderName = "Original Order Name",
                    StartsWith = "O",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            var updatedRecords = new List<DiagnosticImage>
            {
                new DiagnosticImage
                {
                    RecordID = existingRecords[0].RecordID,
                    OrganizationID = _orgId,
                    PatientId = existingRecords[0].PatientId,
                    DiCompany = "Updated Company",
                    Type = "Updated Type",
                    Lookup = "Updated Lookup",
                    OrderName = "Updated Order Name",
                    StartsWith = "U",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _diagnosticImagingRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(existingRecords);

            // Act
            var result = await _diagnosticImagingCommandHandler.UpdateDiagnosticImagingList(updatedRecords, _orgId, _subscription);

            // Assert
            result.Should().BeTrue();
            _diagnosticImagingRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<DiagnosticImage>(), _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}
