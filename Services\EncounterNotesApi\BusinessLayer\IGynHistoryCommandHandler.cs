﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using ShardModels;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public interface IGynHistoryCommandHandler<T> where T : class
    {
        Task AddAsync(IEnumerable<T> gynhistory, Guid OrgId, bool Subscription);
        Task UpdateAsync(T complaint, Guid OrgId, bool Subscription);
        Task UpdateGynHistoryListAsync(IEnumerable<GynHistory> obhistory, Guid OrgId, bool Subscription);
        Task DeleteByIdAsync(Guid ObId, Guid OrgId, bool Subscription);
        Task DeleteByEntity(T gynhistory, Guid OrgId, bool Subscription);
    }
}
