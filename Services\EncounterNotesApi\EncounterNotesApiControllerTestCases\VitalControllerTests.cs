using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class VitalControllerTests
    {
        private Mock<IVitalCommandHandler<Vitals>> _vitalCommandHandlerMock;
        private Mock<IVitalQueryHandler<Vitals>> _vitalQueryHandlerMock;
        private Mock<ILogger<VitalController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private VitalController _controller;
        private List<Vitals> _testVitals;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _vitalCommandHandlerMock = new Mock<IVitalCommandHandler<Vitals>>();
            _vitalQueryHandlerMock = new Mock<IVitalQueryHandler<Vitals>>();
            _loggerMock = new Mock<ILogger<VitalController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No license available."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error occurred."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error posting data."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting data."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Delete successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testVitals = new List<Vitals>
            {
                new Vitals {
                    VitalId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Temperature = "98.6 F",
                    BP = "120/80",
                    Weight = "70 kg",
                    Height = "175 cm",
                    Pulse = "72 bpm",
                    isActive = true,
                    Subscription = _isSubscription
                },
                new Vitals {
                    VitalId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-15),
                    Temperature = "99.1 F",
                    BP = "130/85",
                    Weight = "72 kg",
                    Height = "175 cm",
                    Pulse = "78 bpm",
                    isActive = true,
                    Subscription = _isSubscription
                }
            };

            _controller = new VitalController(
                _vitalCommandHandlerMock.Object,
                _vitalQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetAllById_ReturnsVitals_WhenVitalsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientVitals = _testVitals.Select(v => { v.PatientId = patientId; return v; }).ToList();
            _vitalQueryHandlerMock.Setup(x => x.GetAllVitalsById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientVitals);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedVitals = okResult.Value as IEnumerable<Vitals>;
            returnedVitals.Should().NotBeNull();
            returnedVitals.Should().BeEquivalentTo(patientVitals);
        }

        [Test]
        public async Task GetAllById_ReturnsNotFound_WhenNoVitalsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _vitalQueryHandlerMock.Setup(x => x.GetAllVitalsById(patientId, _orgId, _isSubscription))
                .ReturnsAsync((IEnumerable<Vitals>)null);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task Registration_ValidVitals_ReturnsOk()
        {
            // Arrange
            var vitals = _testVitals;

            // Act
            var result = await _controller.Registration(vitals, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _vitalCommandHandlerMock.Verify(x => x.AddVital(vitals, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task Registration_EmptyList_ReturnsBadRequest()
        {
            // Arrange
            var emptyVitals = new List<Vitals>();

            // Act
            var result = await _controller.Registration(emptyVitals, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);
        }

        [Test]
        public async Task UpdateVitalById_ValidVital_ReturnsOk()
        {
            // Arrange
            var vital = _testVitals[0];

            // Act
            var result = await _controller.UpdateVitalById(vital.PatientId, vital, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _vitalCommandHandlerMock.Verify(x => x.UpdateVital(vital, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task DeleteByEntity_ValidVital_ReturnsOk()
        {
            // Arrange
            var vital = _testVitals[0];

            // Act
            var result = await _controller.DeleteByEntity(vital, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _vitalCommandHandlerMock.Verify(x => x.DeleteVitalByEntity(vital, _orgId, _isSubscription), Times.Once);
        }
    }
}
