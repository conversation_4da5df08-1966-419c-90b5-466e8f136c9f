﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using ShardModels;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]

    public class PrescriptionMedicationController : ControllerBase
    {
        private readonly IPrescriptionMedicationCommandHandler<PrescriptionMedication> _medicDataHandler;
        private readonly IPrescriptionMedicationQueryHandler<PrescriptionMedication> _medicQueryHandler;
        private readonly ILogger<PrescriptionMedicationController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;


        public PrescriptionMedicationController(
            IPrescriptionMedicationCommandHandler<PrescriptionMedication> medicDataHandler,
            IPrescriptionMedicationQueryHandler<PrescriptionMedication> medicQueryHandler,
            ILogger<PrescriptionMedicationController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
            )
        {
            _medicDataHandler = medicDataHandler;
            _medicQueryHandler = medicQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        /// Get all by ID
        /// </summary>


        [HttpGet("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<CurrentMedication>>> GetAllById(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult result;
            try
            {
                var medication = await _medicQueryHandler.GetAllMedicationsbyId(id, orgId, isSubscription);
                result = medication != null ? Ok(medication) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get only Active Medication for an ID
        /// </summary>


        [HttpGet("{id:guid}/isActive/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<PrescriptionMedication>>> GetAllByIdAndIsActive(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult result;
            try
            {
                var medication = await _medicQueryHandler.GetMedicationByIdAndIsActive(id, orgId, isSubscription);
                result = medication != null ? Ok(medication) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Add List Of new Medication
        /// </summary>

        [HttpPost]
        [Route("AddMedication/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> Registration([FromBody] List<PrescriptionMedication> medications, Guid orgId, bool isSubscription)
        {
            if (medications == null || medications.Count == 0)
            {
                return BadRequest(_localizer["NoLicense"]);
            }

            try
            {
                await _medicDataHandler.AddMedication(medications, orgId, isSubscription);
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException ex)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Delete Medication
        /// </summary>

        [HttpDelete]
        [Route("{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> DeleteByEntity([FromBody] PrescriptionMedication medication, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            if (medication == null)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _medicDataHandler.DeleteMedicationByEntity(medication, orgId, isSubscription);
                    result = Ok(_localizer["DeleteSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["DeleteLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
                }
            }
            return result;
        }

        /// <summary>
        /// Update Single Medication By PatientID
        /// </summary>

        [HttpPut("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateMedicationById(Guid id, [FromBody] PrescriptionMedication CurrMedication, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            if (CurrMedication == null || CurrMedication.PatientId != id)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _medicDataHandler.UpdateMedication(CurrMedication, orgId, isSubscription);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                }
            }
            return result;
        }

        /// <summary>
        /// Update List of Medication
        /// </summary>

        [HttpPut]
        [Route("UpdateMedicationList/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateMedicationList([FromBody] List<PrescriptionMedication> currentMedications, Guid orgId, bool isSubscription)
        {
            IActionResult result;

            try
            {
                await _medicDataHandler.UpdateMedicationList(currentMedications, orgId, isSubscription);
                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);

                result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
            }
            return result;
        }

    }
}