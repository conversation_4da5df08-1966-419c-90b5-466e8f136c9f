﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Text;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class BillingRepository : GenericRepository<Billing>, IBillingRepository
    {
        private readonly RecordDatabaseContext _context;

        public BillingRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
        }

        public async Task AddAsync(Billing billing)
        {
            await _context.Billings.AddAsync(billing);
        }

        public async Task UpdateAsync(Billing billing)
        {
            _context.Billings.Update(billing);
            await Task.CompletedTask;
        }

        public async Task DeleteByIdAsync(Guid id)
        {
            var billing = await _context.Billings.FindAsync(id);
            if (billing != null)
            {
                _context.Billings.Remove(billing);
            }
        }

        public async Task<Billing> GetByIdAsync(Guid id)
        {
            return await _context.Billings.FindAsync(id);
        }

        public async Task<List<Billing>> GetAllAsync()
        {
            return await _context.Billings.ToListAsync();
        }

        public async Task<List<Billing>> GetByPatientIdAsync(Guid patientId)
        {
            return await _context.Billings
                .Where(b => b.PatientId == patientId)
                .ToListAsync();
        }
    }
}
