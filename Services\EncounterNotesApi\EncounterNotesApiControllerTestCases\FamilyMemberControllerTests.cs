﻿﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using NUnit.Framework;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class FamilyMemberControllerTests
    {
        private FamilyMemberController _controller;
        private Mock<IFamilyMemberCommandHandler<FamilyMember>> _familyMemberCommandHandlerMock;
        private Mock<IFamilyMemberQueryHandler<FamilyMember>> _familyMemberQueryHandlerMock;
        private Mock<ILogger<FamilyMemberController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private RecordDatabaseContext _contextMock;
        private List<FamilyMember> _testFamilyMembers;
        private readonly Guid _orgId = Guid.NewGuid();
        private readonly bool _isSubscription = false;

        [SetUp]
        public void Setup()
        {
            _familyMemberCommandHandlerMock = new Mock<IFamilyMemberCommandHandler<FamilyMember>>();
            _familyMemberQueryHandlerMock = new Mock<IFamilyMemberQueryHandler<FamilyMember>>();
            _loggerMock = new Mock<ILogger<FamilyMemberController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            // Since the controller doesn't actually use the context, we can pass null
            _contextMock = null;

            // Setup localizer mock
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving family member."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Family member updated successfully."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating family member."));
            _localizerMock.Setup(x => x["SuccessfulDeletion"]).Returns(new LocalizedString("SuccessfulDeletion", "Family member deleted successfully."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting family member."));
            _localizerMock.Setup(x => x["InvalidAddressData"]).Returns(new LocalizedString("InvalidAddressData", "Invalid address data."));
            _localizerMock.Setup(x => x["AddressInvalidMessage"]).Returns(new LocalizedString("AddressInvalidMessage", "Address data is invalid."));
            _localizerMock.Setup(x => x["AddingNewAddress"]).Returns(new LocalizedString("AddingNewAddress", "Adding new address."));
            _localizerMock.Setup(x => x["AddressAddedSuccessfully"]).Returns(new LocalizedString("AddressAddedSuccessfully", "Address added successfully."));
            _localizerMock.Setup(x => x["AddSuccessful"]).Returns(new LocalizedString("AddSuccessful", "Family member added successfully."));
            _localizerMock.Setup(x => x["ErrorAddingAddress"]).Returns(new LocalizedString("ErrorAddingAddress", "Error adding address."));
            _localizerMock.Setup(x => x["InternalServerErrorMessage"]).Returns(new LocalizedString("InternalServerErrorMessage", "Internal server error."));
            _localizerMock.Setup(x => x["AccessError"]).Returns(new LocalizedString("AccessError", "Access error."));
            _localizerMock.Setup(x => x["Record not found"]).Returns(new LocalizedString("Record not found", "Record not found."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));

            _testFamilyMembers = new List<FamilyMember>
            {
                new FamilyMember {
                    RecordID = Guid.NewGuid(),
                    OrganizationID = _orgId,
                    Relation = "Father",
                    Status = "Living",
                    DOB = DateTime.Now.AddYears(-60),
                    Age = 60,
                    Notes = "Healthy",
                    PatientId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Subscription = _isSubscription
                },
                new FamilyMember {
                    RecordID = Guid.NewGuid(),
                    OrganizationID = _orgId,
                    Relation = "Mother",
                    Status = "Living",
                    DOB = DateTime.Now.AddYears(-55),
                    Age = 55,
                    Notes = "Hypertension",
                    PatientId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Subscription = _isSubscription
                }
            };

            _controller = new FamilyMemberController(
                _familyMemberQueryHandlerMock.Object,
                _familyMemberCommandHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object,
                _contextMock
            );
        }

        [Test]
        public async Task GetById_ReturnsFamilyMembers_WhenMembersExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientFamilyMembers = _testFamilyMembers.Select(m => { m.PatientId = patientId; return m; }).ToList();
            _familyMemberQueryHandlerMock.Setup(x => x.GetFamilyMembersById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientFamilyMembers);

            // Act
            var result = await _controller.GetById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedMembers = okResult.Value as List<FamilyMember>;
            returnedMembers.Should().NotBeNull();
            returnedMembers.Should().BeEquivalentTo(patientFamilyMembers);
        }

        [Test]
        public async Task GetById_ReturnsNotFound_WhenNoMembersExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _familyMemberQueryHandlerMock.Setup(x => x.GetFamilyMembersById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(new List<FamilyMember>());

            // Act
            var result = await _controller.GetById(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsFamilyMembers_WhenActiveMembersExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activeMembers = _testFamilyMembers.Select(m => { m.PatientId = patientId; m.IsActive = true; return m; }).ToList();
            _familyMemberQueryHandlerMock.Setup(x => x.GetFamilyMemberByIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activeMembers);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedMembers = okResult.Value as IEnumerable<FamilyMember>;
            returnedMembers.Should().NotBeNull();
            returnedMembers.Should().BeEquivalentTo(activeMembers);
        }

        [Test]
        public async Task AddFamilyMember_ValidMembers_ReturnsOk()
        {
            // Arrange
            var membersToAdd = _testFamilyMembers;

            // Act
            var result = await _controller.AddFamilyMember(membersToAdd, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["AddSuccessful"]);

            // Verify the command handler was called
            _familyMemberCommandHandlerMock.Verify(x => x.AddFamilyMember(membersToAdd, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateById_ValidMember_ReturnsOk()
        {
            // Arrange
            var memberToUpdate = _testFamilyMembers[0];
            var memberId = memberToUpdate.RecordID;

            // Act
            var result = await _controller.UpdateById(memberId, memberToUpdate, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _familyMemberCommandHandlerMock.Verify(x => x.UpdateFamilyMember(memberToUpdate, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task DeleteById_ValidMember_ReturnsOk()
        {
            // Arrange
            var memberId = _testFamilyMembers[0].RecordID;

            // Act
            var result = await _controller.DeleteById(memberId, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulDeletion"]);

            // Verify the command handler was called
            _familyMemberCommandHandlerMock.Verify(x => x.DeleteFamilyMemberById(memberId, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateAccess_ValidMembers_ReturnsOk()
        {
            // Arrange
            var membersToUpdate = _testFamilyMembers;
            _familyMemberCommandHandlerMock.Setup(x => x.UpdateFamilyMemberList(membersToUpdate, _orgId, _isSubscription))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.UpdateAccess(membersToUpdate, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            // Verify the command handler was called
            _familyMemberCommandHandlerMock.Verify(x => x.UpdateFamilyMemberList(membersToUpdate, _orgId, _isSubscription), Times.Once);
        }


    }
}
