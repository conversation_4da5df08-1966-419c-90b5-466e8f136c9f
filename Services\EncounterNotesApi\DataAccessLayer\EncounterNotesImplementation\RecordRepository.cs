﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Interfaces.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class RecordRepository : ShardGenericRepository<Record>, IRecordRepository
    {
        private readonly RecordDatabaseContext _context;
        private readonly IStringLocalizer<EncounterDataAccessLayer> _localizer;
        private readonly ILogger<RecordDatabaseContext> _logger;
        private readonly ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> _shardMapManagerService;
        private readonly string _shardMapName;
        public RecordRepository(
            RecordDatabaseContext context,
            ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> shardMapManagerService,
            IStringLocalizer<EncounterDataAccessLayer> localizer,
            ILogger<RecordDatabaseContext> logger)
            : base(context, shardMapManagerService, localizer, logger)
        {
            _context = context;
            _shardMapManagerService = shardMapManagerService;
            _localizer = localizer;
            _logger = logger;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }

        public async Task AddRecordsAsync(List<Record> EncounterNotes, Guid OrgId, bool Subscription)
        {
            foreach (var record in EncounterNotes)
            {
                if (record?.WordTimings != null)
                {
                    foreach (var wordTiming in record.WordTimings)
                    {
                        wordTiming.RecordId = record.Id;
                    }
                }
            }


            await _context.Records.AddRangeAsync(EncounterNotes);

            using (var transaction = _context.Database.BeginTransaction())
            {
                try
                {
                    await _context.SaveChangesAsync();
                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    throw;
                }
            }
        }

        public async Task<List<WordTiming>> GetWordTimings(Guid recordId, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return new List<WordTiming>();

            return await context.WordTimings
                .Where(wt => wt.RecordId == recordId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Record>> GetByPCPIdAsync(Guid PCPId, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return Enumerable.Empty<Record>();

            var records = await context.Records
                .Where(r => r.PCPId == PCPId)
                .ToListAsync();

            foreach (var record in records)
            {
                record.WordTimings = await GetWordTimings(record.Id, OrgId, Subscription);
            }

            return records.OrderByDescending(r => r.DateTime);
        }

        public async Task<IEnumerable<Record>> GetByPatientIdAsync(Guid PatientId, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return Enumerable.Empty<Record>();

            var records = await context.Records
                .Where(r => r.PatientId == PatientId)
                .ToListAsync();

            foreach (var record in records)
            {
                record.WordTimings = await GetWordTimings(record.Id, OrgId, Subscription);
            }

            return records.OrderByDescending(r => r.DateTime);
        }
    }
}
