﻿using EncounterNotesContracts;
using ShardModels;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface ITherapeuticInterventionsCommandHandler<TText>
    {
        Task DeleteTherapeuticInterventionsByEntity(TherapeuticInterventionsData _TherapeuticInterventions, Guid OrgId, bool Subscription);
        Task DeleteTherapeuticInterventionsById(Guid id, Guid OrgId, bool Subscription);
        Task AddTherapeuticInterventions(List<TText> texts, Guid OrgId, bool Subscription);
        Task UpdateTherapeuticInterventions(TherapeuticInterventionsData _TherapeuticInterventions, Guid OrgId, bool Subscription);
        Task UpdateTherapeuticInterventionsList(List<TherapeuticInterventionsData> _TherapeuticInterventions, Guid OrgId, bool Subscription);
    }
}
