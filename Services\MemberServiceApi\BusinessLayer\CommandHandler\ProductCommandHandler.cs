﻿using Contracts;
using MemberServiceBusinessLayer;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class ProductCommandHandler : IProductCommandHandler<ProductDTO>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ProductCommandHandler> _logger;

        public ProductCommandHandler(IConfiguration configuration, IUnitOfWork unitOfWork, ILogger<ProductCommandHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task AddProduct(List<ProductDTO> products, Guid orgId, bool subscription)
        {
            await _unitOfWork.ProductRepository.AddAsync(products, orgId, subscription);
            await _unitOfWork.SaveAsync();

        }

        public async Task UpdateProduct(ProductDTO product, Guid orgId, bool Subscription)
        {
            await _unitOfWork.ProductRepository.UpdateAsync(product, orgId, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteProductById(Guid id, Guid orgId, bool Subscription)
        {
            await _unitOfWork.ProductRepository.DeleteByIdAsync(id, orgId, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteProductByEntity(ProductDTO product, Guid orgId, bool Subscription)
        {
            await _unitOfWork.ProductRepository.DeleteByEntityAsync(product, orgId, Subscription);
            await _unitOfWork.SaveAsync();
        }
    }
}


