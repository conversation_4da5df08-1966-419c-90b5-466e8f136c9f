using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class PhysicalTherapyQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IPhysicalTherapyRepository> _physicalTherapyRepositoryMock;
        private PhysicalTherapyQueryHandler _physicalTherapyQueryHandler;
        private Guid _orgId;
        private bool _subscription;
        private Guid _patientId;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _physicalTherapyRepositoryMock = new Mock<IPhysicalTherapyRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;
            _patientId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.PhysicalTherapyRepository).Returns(_physicalTherapyRepositoryMock.Object);
            _physicalTherapyQueryHandler = new PhysicalTherapyQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetAllPhysicalTherapyById_ShouldReturnAllTherapiesForPatient()
        {
            // Arrange
            var therapies = new List<PhysicalTherapyData>
            {
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    TherapyAssessment = "Lower back pain assessment",
                    ShortTermGoals = "Reduce pain level to 3/10 within 2 weeks",
                    LongTermGoals = "Return to normal activities within 2 months",
                    PhysicalTherapyDiagnosis = "Lumbar strain",
                    PhysicalTherapyProgram = "Core strengthening and flexibility",
                    Disabilities = "Limited mobility",
                    Functionals = "Difficulty with prolonged sitting",
                    Limitations = "Cannot lift more than 10 pounds",
                    IsActive = true,
                    Subscription = _subscription
                },
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdatedDate = DateTime.Now.AddDays(-55),
                    TherapyAssessment = "Knee pain assessment",
                    ShortTermGoals = "Improve range of motion by 20% within 3 weeks",
                    LongTermGoals = "Return to sports activities within 3 months",
                    PhysicalTherapyDiagnosis = "Patellofemoral pain syndrome",
                    PhysicalTherapyProgram = "Knee strengthening and stability exercises",
                    Disabilities = "Limited walking distance",
                    Functionals = "Difficulty with stairs",
                    Limitations = "Cannot run or jump",
                    IsActive = false,
                    Subscription = _subscription
                },
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-45),
                    UpdatedDate = DateTime.Now.AddDays(-40),
                    TherapyAssessment = "Shoulder mobility assessment",
                    ShortTermGoals = "Increase range of motion by 20% within 3 weeks",
                    LongTermGoals = "Full range of motion within 3 months",
                    PhysicalTherapyDiagnosis = "Rotator cuff tendinitis",
                    PhysicalTherapyProgram = "Shoulder strengthening and mobility exercises",
                    Disabilities = "Limited overhead reaching",
                    Functionals = "Difficulty with dressing",
                    Limitations = "Cannot lift arm above shoulder height",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _physicalTherapyRepositoryMock.Setup(r => r.GetAllPhysicalTherapyAsync(_orgId, _subscription))
                .ReturnsAsync(therapies);

            // Act
            var result = await _physicalTherapyQueryHandler.GetAllPhysicalTherapyById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(t => t.PatientId == _patientId).Should().BeTrue();
        }

        [Test]
        public async Task GetAllPhysicalTherapyById_WithNoTherapies_ShouldReturnEmptyCollection()
        {
            // Arrange
            var therapies = new List<PhysicalTherapyData>();

            _physicalTherapyRepositoryMock.Setup(r => r.GetAllPhysicalTherapyAsync(_orgId, _subscription))
                .ReturnsAsync(therapies);

            // Act
            var result = await _physicalTherapyQueryHandler.GetAllPhysicalTherapyById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetPhysicalTherapyByIdAndIsActive_ShouldReturnActiveTherapiesForPatient()
        {
            // Arrange
            var therapies = new List<PhysicalTherapyData>
            {
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    TherapyAssessment = "Lower back pain assessment",
                    IsActive = true,
                    Subscription = _subscription
                },
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    TherapyAssessment = "Knee pain assessment",
                    IsActive = false, // Inactive
                    Subscription = _subscription
                },
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    TherapyAssessment = "Shoulder mobility assessment",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _physicalTherapyRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(therapies);

            // Act
            var result = await _physicalTherapyQueryHandler.GetPhysicalTherapyByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.All(t => t.PatientId == _patientId && t.IsActive).Should().BeTrue();
        }

        [Test]
        public async Task GetPhysicalTherapyByIdAndIsActive_WithNoActiveTherapies_ShouldReturnEmptyCollection()
        {
            // Arrange
            var therapies = new List<PhysicalTherapyData>
            {
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    TherapyAssessment = "Knee pain assessment",
                    IsActive = false, // Inactive
                    Subscription = _subscription
                }
            };

            _physicalTherapyRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(therapies);

            // Act
            var result = await _physicalTherapyQueryHandler.GetPhysicalTherapyByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
