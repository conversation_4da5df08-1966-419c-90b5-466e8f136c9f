﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesContracts
{
    public class TherapeuticInterventionsData : IContract
    {
        public Guid TherapeuticInterventionsID { get; set; }
        public Guid? PatientId { get; set; }
        public Guid? OrganizationId { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public Guid? PCPId { get; set; }
        public string? TherapyType { get; set; }
        public string? Notes { get; set; }
        public bool? IsActive { get; set; }
        public bool Subscription { get; set; }
    }
}
