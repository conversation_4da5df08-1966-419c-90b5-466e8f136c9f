using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class VaccinesQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IVaccinesRepository> _vaccinesRepositoryMock;
        private VaccinesQueryHandler _vaccinesQueryHandler;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _vaccinesRepositoryMock = new Mock<IVaccinesRepository>();

            _unitOfWorkMock.Setup(u => u.VaccinesRepository).Returns(_vaccinesRepositoryMock.Object);
            _vaccinesQueryHandler = new VaccinesQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetData_ShouldReturnAllVaccines()
        {
            // Arrange
            var vaccines = new List<Vaccines>
            {
                new Vaccines
                {
                    Id = Guid.NewGuid(),
                    VaccineName = "Influenza",
                    CVXCode = "88",
                    CPTCode = "90658",
                    CPTDescription = "Influenza virus vaccine, trivalent"
                },
                new Vaccines
                {
                    Id = Guid.NewGuid(),
                    VaccineName = "COVID-19",
                    CVXCode = "213",
                    CPTCode = "91300",
                    CPTDescription = "SARS-CoV-2 (COVID-19) vaccine"
                },
                new Vaccines
                {
                    Id = Guid.NewGuid(),
                    VaccineName = "Tetanus",
                    CVXCode = "115",
                    CPTCode = "90715",
                    CPTDescription = "Tetanus, diphtheria toxoids and acellular pertussis vaccine"
                }
            };

            _vaccinesRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(vaccines);

            // Act
            var result = await _vaccinesQueryHandler.GetData();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(3);
            result.Should().BeEquivalentTo(vaccines);
        }

        [Test]
        public async Task GetData_WithNoVaccines_ShouldReturnEmptyCollection()
        {
            // Arrange
            var vaccines = new List<Vaccines>();

            _vaccinesRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(vaccines);

            // Act
            var result = await _vaccinesQueryHandler.GetData();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetData_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            _vaccinesRepositoryMock.Setup(r => r.GetAsync())
                .ThrowsAsync(new Exception("Database connection error"));

            // Act & Assert
            await FluentActions.Invoking(async () => await _vaccinesQueryHandler.GetData())
                .Should().ThrowAsync<Exception>()
                .WithMessage("Database connection error");
        }
    }
}
