using System;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IPageRoleMappingCommandHandler<T>
    {
        Task AddPageRoleMappingAsync(T pageRoleMapping, Guid OrganizationId, bool Subscription);
        Task UpdatePageRoleMappingAsync(T pageRoleMapping, Guid OrganizationId, bool Subscription);
        Task DeletePageRoleMappingAsync(Guid id, Guid OrganizationId, bool Subscription);
    }
}
