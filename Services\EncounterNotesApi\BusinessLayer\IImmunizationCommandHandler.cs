﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface IImmunizationCommandHandler<TText>
    {
        Task DeleteImmunizationByEntity(Immunization immunization, Guid OrgId, bool Subscription);
        Task DeleteImmunizationById(Guid id, Guid OrgId, bool Subscription);
        Task AddImmunization(List<TText> texts, Guid OrgId, bool Subscription);
        Task UpdateImmunization(Immunization IMmunization, Guid OrgId, bool Subscription);
        Task UpdateImmunizationList(List<Immunization> immuNization, Guid OrgId, bool Subscription);
    }
}
