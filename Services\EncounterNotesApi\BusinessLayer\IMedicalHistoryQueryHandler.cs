﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IMedicalHistoryQueryHandler<TText>
    {
        Task<IEnumerable<MedicalHistory>> GetMedicalHistoryByIdAndIsActive(Guid id, Guid OrgId, bool Subscription);
        Task<IEnumerable<MedicalHistory>> GetAllMedicalHistoriesById(Guid id, Guid OrgId, bool Subscription);
    }
}