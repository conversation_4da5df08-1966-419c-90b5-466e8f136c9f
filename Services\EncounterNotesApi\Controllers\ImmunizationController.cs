﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using ShardModels;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]

    public class ImmunizationController : ControllerBase
    {
        private readonly IImmunizationCommandHandler<Immunization> _immunizationDataHandler;
        private readonly IImmunizationQueryHandler<Immunization> _immunizationQueryHandler;
        private readonly ILogger<SurgicalHistoryController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;


        public ImmunizationController(
            IImmunizationCommandHandler<Immunization> immunizationDataHandler,
            IImmunizationQueryHandler<Immunization> immunizationQueryHandler,
            ILogger<SurgicalHistoryController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
            )
        {
            _immunizationDataHandler = immunizationDataHandler;
            _immunizationQueryHandler = immunizationQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        /// Get All Details By Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<Immunization>>> GetAllById(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult result;
            try
            {
                var data = await _immunizationQueryHandler.GetAllImmunizationsById(id, orgId, isSubscription);
                result = data != null ? Ok(data) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get The Details which are currently Active
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id:guid}/IsActive/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<Immunization>>> GetAllByIdAndIsActive(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult result;
            try
            {
                var surgery = await _immunizationQueryHandler.GetImmunizationByIdAndIsActive(id, orgId, isSubscription);
                result = surgery != null ? Ok(surgery) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Add New Surgery
        /// </summary>
        /// <param name="surgeries"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddImmunization/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> Registration([FromBody] List<Immunization> addimmunization, Guid orgId, bool isSubscription)
        {
            if (addimmunization == null || addimmunization.Count == 0)
            {
                return BadRequest(_localizer["NoLicense"]);
            }

            try
            {
                await _immunizationDataHandler.AddImmunization(addimmunization, orgId, isSubscription);
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException ex)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Delete Surgery
        /// </summary>
        /// <param name="surgery"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> DeleteByEntity([FromBody] Immunization immunization, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            {
                if (immunization == null)
                {
                    result = BadRequest(_localizer["InvalidRecord"]);
                }
                else
                {
                    try
                    {
                        await _immunizationDataHandler.DeleteImmunizationByEntity(immunization, orgId, isSubscription);
                        result = Ok(_localizer["DeleteSuccessful"]);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, _localizer["DeleteLogError"]);
                        result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
                    }
                }
                return result;
            }
        }

        /// <summary>
        /// Update Surgery By Id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="SurHistory"></param>
        /// <returns></returns>
        [HttpPut("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateImmunizationById(Guid id, [FromBody] Immunization IMmunization, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            {
                if (IMmunization == null || IMmunization.PatientId != id)
                {
                    result = BadRequest(_localizer["InvalidRecord"]);
                }
                else
                {
                    try
                    {
                        await _immunizationDataHandler.UpdateImmunization(IMmunization, orgId, isSubscription);
                        result = Ok(_localizer["UpdateSuccessful"]);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, _localizer["UpdateLogError"]);
                        result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                    }
                }
                return result;
            }
        }

        /// <summary>
        /// Update Surgery(List)
        /// </summary>
        /// <param name="surgicalHistory"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("UpdateImmunizationList/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateImmunizationList([FromBody] List<Immunization> immuNization, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            try
            {
                await _immunizationDataHandler.UpdateImmunizationList(immuNization, orgId, isSubscription);
                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);

                result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
            }
            return result;
        }

    }
}