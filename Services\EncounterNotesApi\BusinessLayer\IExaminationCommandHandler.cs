﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface IExaminationCommandHandler
    {
        Task AddExaminationList(List<Examination> examinations, Guid OrgId, bool Subscription);

        Task UpdateExaminationList(List<Examination> examinations, Guid OrgId, bool Subscription);

    }
}
