﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IProductOrganizationMappingsQueryHandler<T>
    {
        Task<T> GetProductOrganizationMappingByIdAsync(Guid id);
        Task<List<ProductOrganizationMapping>> GetAllProductOrganizationMappingsAsync();
    }
}
