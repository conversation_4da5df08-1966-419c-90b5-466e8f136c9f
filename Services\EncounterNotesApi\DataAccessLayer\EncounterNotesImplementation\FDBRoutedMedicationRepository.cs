﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class FDBRoutedMedicationRepository : GenericRepository<FDBRoutedMedication>, IFDBRoutedMedicationRepository
    {
        private readonly RecordDatabaseContext _context;

        public FDBRoutedMedicationRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
        }
        public async Task<List<FDBRoutedMedication>> GetRoutedMedicationByMED_NAME_ID(string MED_NAME_ID)
        {
            var medication = await _context.FDBRoutedMedications
            .Where(t => t.MED_NAME_ID == MED_NAME_ID)
            .ToListAsync();
            return medication;
        }
    }
}
