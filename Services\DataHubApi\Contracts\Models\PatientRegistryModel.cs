using System.ComponentModel.DataAnnotations;

namespace DataHubContracts.Models
{
    public class PatientRegistryModel
    {
        public Guid Id { get; set; }
        public string? PatientName { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Sex { get; set; }
        public int? Age { get; set; }
        public string? TelephoneNumber { get; set; }
        public string? AccountNumber { get; set; }
        public string? PCPName { get; set; }
        public string? Insurance { get; set; }
        public string? InsuranceName { get; set; }
        public string? Language { get; set; }
        public string? Ethnicity { get; set; }
        public string? Race { get; set; }
        public string? ZipCode { get; set; }
        public string? Facility { get; set; }
        public Guid? OrganizationId { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsDeceased { get; set; }
        public bool? RegistryEnabled { get; set; }
        public bool? ExcludeBeneficiaries { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
