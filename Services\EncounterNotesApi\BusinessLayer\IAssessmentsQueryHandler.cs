﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface IAssessmentsQueryHandler<TText>
    {
        Task<IEnumerable<AssessmentsData>> GetAssessmentsByIdAndIsActive(Guid id, Guid OrgId, bool Subscription);
        Task<IEnumerable<AssessmentsData>> GetAllAssessmentsById(Guid id, Guid OrgId, bool Subscription);
        Task<List<string>> GetAllMedications(Guid PatientId, Guid OrgId, bool Subscription);
    }
}
