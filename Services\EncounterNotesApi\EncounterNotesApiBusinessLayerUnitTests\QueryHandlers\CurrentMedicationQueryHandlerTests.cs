using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class CurrentMedicationQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ICurrentMedicationRepository> _currentMedicationRepositoryMock;
        private CurrentMedicationQueryHandler _currentMedicationQueryHandler;
        private Guid _orgId;
        private bool _subscription;
        private Guid _patientId;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _currentMedicationRepositoryMock = new Mock<ICurrentMedicationRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;
            _patientId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.CurrentMedicationRepository).Returns(_currentMedicationRepositoryMock.Object);
            _currentMedicationQueryHandler = new CurrentMedicationQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetMedicationByIdAndIsActive_ShouldReturnActiveMedicationsForPatient()
        {
            // Arrange
            var medications = new List<CurrentMedication>
            {
                new CurrentMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    BrandName = "Lipitor",
                    DrugDetails = "Atorvastatin 20mg",
                    Quantity = "30 tablets",
                    Frequency = "Once daily",
                    isActive = true,
                    StartDate = DateTime.Now.AddDays(-30),
                    EndDate = DateTime.Now.AddDays(30),
                    CheifComplaint = "High cholesterol",
                    CheifComplaintId = Guid.NewGuid(),
                    Subscription = _subscription
                },
                new CurrentMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdatedDate = DateTime.Now.AddDays(-55),
                    BrandName = "Aspirin",
                    DrugDetails = "81mg",
                    Quantity = "90 tablets",
                    Frequency = "Once daily",
                    isActive = false, // Inactive
                    StartDate = DateTime.Now.AddDays(-60),
                    EndDate = DateTime.Now.AddDays(-30),
                    CheifComplaint = "Heart disease prevention",
                    CheifComplaintId = Guid.NewGuid(),
                    Subscription = _subscription
                },
                new CurrentMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-45),
                    UpdatedDate = DateTime.Now.AddDays(-40),
                    BrandName = "Metformin",
                    DrugDetails = "500mg",
                    Quantity = "60 tablets",
                    Frequency = "Twice daily",
                    isActive = true,
                    StartDate = DateTime.Now.AddDays(-45),
                    EndDate = null,
                    CheifComplaint = "Type 2 diabetes",
                    CheifComplaintId = Guid.NewGuid(),
                    Subscription = _subscription
                }
            };

            _currentMedicationRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(medications);

            // Act
            var result = await _currentMedicationQueryHandler.GetMedicationByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.All(m => m.PatientId == _patientId && m.isActive).Should().BeTrue();
        }

        [Test]
        public async Task GetMedicationByIdAndIsActive_WithNoActiveMedications_ShouldReturnEmptyCollection()
        {
            // Arrange
            var medications = new List<CurrentMedication>
            {
                new CurrentMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    BrandName = "Aspirin",
                    DrugDetails = "81mg",
                    isActive = false, // Inactive
                    Subscription = _subscription
                }
            };

            _currentMedicationRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(medications);

            // Act
            var result = await _currentMedicationQueryHandler.GetMedicationByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetAllMedicationsById_ShouldReturnAllMedicationsForPatient()
        {
            // Arrange
            var medications = new List<CurrentMedication>
            {
                new CurrentMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    BrandName = "Lipitor",
                    DrugDetails = "Atorvastatin 20mg",
                    isActive = true,
                    Subscription = _subscription
                },
                new CurrentMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    BrandName = "Aspirin",
                    DrugDetails = "81mg",
                    isActive = false,
                    Subscription = _subscription
                },
                new CurrentMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    BrandName = "Metformin",
                    DrugDetails = "500mg",
                    isActive = true,
                    Subscription = _subscription
                }
            };

            _currentMedicationRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(medications);

            // Act
            var result = await _currentMedicationQueryHandler.GetAllMedicationsById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(m => m.PatientId == _patientId).Should().BeTrue();
        }

        [Test]
        public async Task GetAllMedicationsById_WithNoMedications_ShouldReturnEmptyCollection()
        {
            // Arrange
            var medications = new List<CurrentMedication>();

            _currentMedicationRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(medications);

            // Act
            var result = await _currentMedicationQueryHandler.GetAllMedicationsById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
