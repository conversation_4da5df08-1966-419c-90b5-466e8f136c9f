using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class DiagnosticImagingPageQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IDiagnosticImagingPageRepository> _diagnosticImagingPageRepositoryMock;
        private DiagnosticImagingPageQueryHandler _diagnosticImagingPageQueryHandler;
        private Guid _orgId;
        private bool _subscription;
        private Guid _patientId;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _diagnosticImagingPageRepositoryMock = new Mock<IDiagnosticImagingPageRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;
            _patientId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.DiagnosticImagingPageRepository).Returns(_diagnosticImagingPageRepositoryMock.Object);
            _diagnosticImagingPageQueryHandler = new DiagnosticImagingPageQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetDiagnosticImagingById_ShouldReturnAllDiagnosticImagesForPatient()
        {
            // Arrange
            var diagnosticImages = new List<DiagnosticImagingDTO>
            {
                new DiagnosticImagingDTO
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationID = _orgId,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Status = "Completed",
                    Provider = "Dr. Smith",
                    Facility = "Imaging Center A",
                    AssignedTo = "Dr. Johnson",
                    FutureOrder = false,
                    HigherPriority = false,
                    InHouse = true,
                    Procedgure = "X-ray, Chest",
                    OrderDate = DateTime.Now.AddDays(-35),
                    Reason = "Suspected pneumonia",
                    Recieved = true,
                    Date = DateTime.Now.AddDays(-30),
                    Result = "Normal findings",
                    Notes = "No abnormalities detected",
                    ClassicalInfo = "PA and lateral views",
                    InternalNotes = "Follow up in 6 months",
                    IsActive = true,
                    Subscription = _subscription
                },
                new DiagnosticImagingDTO
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationID = _orgId,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdatedDate = DateTime.Now.AddDays(-55),
                    Status = "Ordered",
                    Provider = "Dr. Brown",
                    Facility = "Imaging Center B",
                    AssignedTo = "Dr. Davis",
                    FutureOrder = true,
                    HigherPriority = true,
                    InHouse = false,
                    Procedgure = "MRI, Lumbar Spine",
                    OrderDate = DateTime.Now.AddDays(-65),
                    Reason = "Lower back pain",
                    Recieved = false,
                    Date = DateTime.Now.AddDays(5), // Future date
                    Result = "",
                    Notes = "Patient to be NPO for 6 hours prior",
                    ClassicalInfo = "With and without contrast",
                    InternalNotes = "Insurance approval pending",
                    IsActive = false,
                    Subscription = _subscription
                }
            };

            _diagnosticImagingPageRepositoryMock.Setup(r => r.GetDiagnosticImagingById(_patientId, _orgId, _subscription))
                .ReturnsAsync(diagnosticImages);

            // Act
            var result = await _diagnosticImagingPageQueryHandler.GetDiagnosticImagingById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(d => d.PatientId == _patientId).Should().BeTrue();
        }

        [Test]
        public async Task GetDiagnosticImagingById_WithNoDiagnosticImages_ShouldReturnEmptyCollection()
        {
            // Arrange
            var diagnosticImages = new List<DiagnosticImagingDTO>();

            _diagnosticImagingPageRepositoryMock.Setup(r => r.GetDiagnosticImagingById(_patientId, _orgId, _subscription))
                .ReturnsAsync(diagnosticImages);

            // Act
            var result = await _diagnosticImagingPageQueryHandler.GetDiagnosticImagingById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetDiagnosticImagingById_WhenRepositoryThrowsException_ShouldHandleExceptionAndReturnEmptyList()
        {
            // Arrange
            _diagnosticImagingPageRepositoryMock.Setup(r => r.GetDiagnosticImagingById(_patientId, _orgId, _subscription))
                .ThrowsAsync(new Exception("Database connection error"));

            // Act
            var result = await _diagnosticImagingPageQueryHandler.GetDiagnosticImagingById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetDiagnosticImagingByIdAndIsActive_ShouldReturnActiveDiagnosticImagesForPatient()
        {
            // Arrange
            var diagnosticImages = new List<DiagnosticImagingDTO>
            {
                new DiagnosticImagingDTO
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationID = _orgId,
                    Status = "Completed",
                    Provider = "Dr. Smith",
                    Procedgure = "X-ray, Chest",
                    IsActive = true,
                    Subscription = _subscription
                },
                new DiagnosticImagingDTO
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationID = _orgId,
                    Status = "Ordered",
                    Provider = "Dr. Brown",
                    Procedgure = "MRI, Lumbar Spine",
                    IsActive = false, // Inactive
                    Subscription = _subscription
                }
            };

            _diagnosticImagingPageRepositoryMock.Setup(r => r.GetDiagnosticImagingByIdAndIsActive(_patientId, _orgId, _subscription))
                .ReturnsAsync(diagnosticImages.Where(d => d.IsActive).ToList());

            // Act
            var result = await _diagnosticImagingPageQueryHandler.GetDiagnosticImagingByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.All(d => d.PatientId == _patientId && d.IsActive).Should().BeTrue();
        }

        [Test]
        public async Task GetDiagnosticImagingByIdAndIsActive_WithNoActiveDiagnosticImages_ShouldReturnEmptyCollection()
        {
            // Arrange
            var diagnosticImages = new List<DiagnosticImagingDTO>();

            _diagnosticImagingPageRepositoryMock.Setup(r => r.GetDiagnosticImagingByIdAndIsActive(_patientId, _orgId, _subscription))
                .ReturnsAsync(diagnosticImages);

            // Act
            var result = await _diagnosticImagingPageQueryHandler.GetDiagnosticImagingByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetDiagnosticImagingByIdAndIsActive_WhenRepositoryThrowsException_ShouldHandleExceptionAndReturnEmptyList()
        {
            // Arrange
            _diagnosticImagingPageRepositoryMock.Setup(r => r.GetDiagnosticImagingByIdAndIsActive(_patientId, _orgId, _subscription))
                .ThrowsAsync(new Exception("Database connection error"));

            // Act
            var result = await _diagnosticImagingPageQueryHandler.GetDiagnosticImagingByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
