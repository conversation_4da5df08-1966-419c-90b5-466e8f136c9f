using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class RelationQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IRelationRepository> _relationRepositoryMock;
        private RelationQueryHandler _relationQueryHandler;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _relationRepositoryMock = new Mock<IRelationRepository>();

            _unitOfWorkMock.Setup(u => u.RelationRepository).Returns(_relationRepositoryMock.Object);
            _relationQueryHandler = new RelationQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetAllRelations_ShouldReturnAllRelations()
        {
            // Arrange
            var relations = new List<Relations>
            {
                new Relations
                {
                    RelationId = Guid.NewGuid(),
                    Relation = "Spouse"
                },
                new Relations
                {
                    RelationId = Guid.NewGuid(),
                    Relation = "Parent"
                },
                new Relations
                {
                    RelationId = Guid.NewGuid(),
                    Relation = "Child"
                },
                new Relations
                {
                    RelationId = Guid.NewGuid(),
                    Relation = "Sibling"
                }
            };

            _relationRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(relations);

            // Act
            var result = await _relationQueryHandler.GetAllRelations();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(4);
            result.Should().BeEquivalentTo(relations);
        }

        [Test]
        public async Task GetAllRelations_WithNoRelations_ShouldReturnEmptyCollection()
        {
            // Arrange
            var relations = new List<Relations>();

            _relationRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(relations);

            // Act
            var result = await _relationQueryHandler.GetAllRelations();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetAllRelations_WhenRepositoryThrowsException_ShouldHandleExceptionAndReturnEmptyList()
        {
            // Arrange
            _relationRepositoryMock.Setup(r => r.GetAsync())
                .ThrowsAsync(new Exception("Database connection error"));

            // Act
            var result = await _relationQueryHandler.GetAllRelations();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
