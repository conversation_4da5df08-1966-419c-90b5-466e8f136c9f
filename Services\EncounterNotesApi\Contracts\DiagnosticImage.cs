﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesContracts
{
    public class DiagnosticImage : IContract
    {
        public Guid RecordID { get; set; }
        public Guid? OrganizationID { get; set; }
        public Guid PatientId { get; set; }
        public string DiCompany { get; set; }
        public string? ccResults { get; set; }
        public string Type { get; set; }
        public string Lookup { get; set; }
        public string OrderName { get; set; }
        public string StartsWith { get; set; }
        public bool IsActive { get; set; }
        public Guid? CreatedBy { get; set; }
        public Guid? UpdatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public bool Subscription { get; set; }
    }
}
