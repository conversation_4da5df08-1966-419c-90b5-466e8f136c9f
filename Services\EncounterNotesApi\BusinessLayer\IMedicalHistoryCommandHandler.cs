﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface IMedicalHistoryCommandHandler<TText>
    {
        Task DeleteMedicalHistoryByEntity(MedicalHistory medicalHistory, Guid OrgId, bool Subscription);
        Task DeleteMedicalHistoryById(Guid id, Guid OrgId, bool Subscription);
        Task AddMedicalHistory(List<TText> texts, Guid OrgId, bool Subscription);
        Task UpdateMedicalHistory(MedicalHistory medicalHistory, Guid OrgId, bool Subscription);
        Task UpdateMedicalHistoryList(List<MedicalHistory> medicalHistories, Guid OrgId, bool Subscription);
    }
}
