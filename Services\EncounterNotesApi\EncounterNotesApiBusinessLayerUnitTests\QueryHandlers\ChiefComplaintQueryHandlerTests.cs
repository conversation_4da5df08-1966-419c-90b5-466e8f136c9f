using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class ChiefComplaintQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IChiefComplaintRepository<ChiefComplaint>> _chiefComplaintRepositoryMock;
        private ChiefComplaintQueryHandler _chiefComplaintQueryHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _chiefComplaintRepositoryMock = new Mock<IChiefComplaintRepository<ChiefComplaint>>();

            _unitOfWorkMock.Setup(u => u.ChiefComplaintRepository).Returns(_chiefComplaintRepositoryMock.Object);

            _chiefComplaintQueryHandler = new ChiefComplaintQueryHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task GetAllChiefComplaints_ShouldReturnAllComplaints()
        {
            // Arrange
            var complaints = new List<ChiefComplaint>
            {
                new ChiefComplaint
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "Headache",
                    DateOfComplaint = DateTime.Now.AddDays(-1),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false
                },
                new ChiefComplaint
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "Fever",
                    DateOfComplaint = DateTime.Now.AddDays(-2),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false
                }
            };

            _chiefComplaintRepositoryMock.Setup(r => r.GetAllAsync(_orgId, _subscription))
                .ReturnsAsync(complaints);

            // Act
            var result = await _chiefComplaintQueryHandler.GetAllChiefComplaints(_orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.Should().Contain(c => c.Description == "Headache");
            result.Should().Contain(c => c.Description == "Fever");
        }

        [Test]
        public async Task GetChiefComplaintById_ShouldReturnComplaintWithMatchingId()
        {
            // Arrange
            var complaintId = Guid.NewGuid();
            var complaint = new ChiefComplaint
            {
                Id = complaintId,
                PatientId = Guid.NewGuid(),
                Description = "Headache",
                DateOfComplaint = DateTime.Now.AddDays(-1),
                OrganizationId = _orgId,
                PcpId = Guid.NewGuid(),
                IsDeleted = false
            };

            _chiefComplaintRepositoryMock.Setup(r => r.GetByIdAsync(complaintId, _orgId, _subscription))
                .ReturnsAsync(complaint);

            // Act
            var result = await _chiefComplaintQueryHandler.GetChiefComplaintById(complaintId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be(complaintId);
            result.Description.Should().Be("Headache");
        }

        [Test]
        public async Task GetChiefComplaintsByPatientId_ShouldReturnComplaintsForPatient()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var complaints = new List<ChiefComplaint>
            {
                new ChiefComplaint
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    Description = "Headache",
                    DateOfComplaint = DateTime.Now.AddDays(-1),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false
                },
                new ChiefComplaint
                {
                    Id = Guid.NewGuid(),
                    PatientId = patientId,
                    Description = "Fever",
                    DateOfComplaint = DateTime.Now.AddDays(-2),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false
                }
            };

            _chiefComplaintRepositoryMock.Setup(r => r.GetByPatientIdAsync(patientId, _orgId, _subscription))
                .ReturnsAsync(complaints);

            // Act
            var result = await _chiefComplaintQueryHandler.GetChiefComplaintsByPatientId(patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(c => c.PatientId == patientId).Should().BeTrue();
            result.Should().Contain(c => c.Description == "Headache");
            result.Should().Contain(c => c.Description == "Fever");
        }

        [Test]
        public async Task GetAllChiefComplaints_WithNoComplaints_ShouldReturnEmptyCollection()
        {
            // Arrange
            var complaints = new List<ChiefComplaint>();

            _chiefComplaintRepositoryMock.Setup(r => r.GetAllAsync(_orgId, _subscription))
                .ReturnsAsync(complaints);

            // Act
            var result = await _chiefComplaintQueryHandler.GetAllChiefComplaints(_orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetChiefComplaintById_WithNonExistentId_ShouldReturnNull()
        {
            // Arrange
            var complaintId = Guid.NewGuid();

            _chiefComplaintRepositoryMock.Setup(r => r.GetByIdAsync(complaintId, _orgId, _subscription))
                .ReturnsAsync((ChiefComplaint)null);

            // Act
            var result = await _chiefComplaintQueryHandler.GetChiefComplaintById(complaintId, _orgId, _subscription);

            // Assert
            result.Should().BeNull();
        }

        [Test]
        public async Task GetChiefComplaintsByPatientId_WithNoComplaints_ShouldReturnEmptyCollection()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var complaints = new List<ChiefComplaint>();

            _chiefComplaintRepositoryMock.Setup(r => r.GetByPatientIdAsync(patientId, _orgId, _subscription))
                .ReturnsAsync(complaints);

            // Act
            var result = await _chiefComplaintQueryHandler.GetChiefComplaintsByPatientId(patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
