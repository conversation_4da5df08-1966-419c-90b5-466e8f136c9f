﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using Microsoft.Azure.Amqp.Framing;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using System.Security.Cryptography;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class VisionExaminationController : ControllerBase
    {
        private readonly IVisionExaminationCommandHandler<VisionRx> _visionExaminationCommandHandler;
        private readonly IVisionExaminationQueryHandler<VisionRx> _visionExaminationQueryHandler;
        private readonly ILogger<VisionExaminationController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;
        public VisionExaminationController(
            IVisionExaminationQueryHandler<VisionRx> visionExaminationQueryHandler,
            IVisionExaminationCommandHandler<VisionRx> visionExaminationCommandHandler,
            ILogger<VisionExaminationController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer,
            RecordDatabaseContext context
            )
        {
            _visionExaminationQueryHandler = visionExaminationQueryHandler;
            _visionExaminationCommandHandler = visionExaminationCommandHandler;
            _logger = logger;
            _localizer = localizer;
        }

        [HttpGet("{OrgID:Guid}/{Subscription:bool}")]
        public async Task<ActionResult<IEnumerable<VisionRx>>> Get(Guid OrgID, bool Subscription)
        {
            ActionResult result;
            try
            {
                var relations = await _visionExaminationQueryHandler.GetAllVisionRecords(OrgID, Subscription);
                result = Ok(relations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Update an existing Record
        /// </summary>
        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateById(Guid id, [FromBody] VisionRx visionRx)
        {
            IActionResult result;
            if (visionRx == null || visionRx.ExaminationId != id)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _visionExaminationCommandHandler.UpdateVisionRecord(visionRx, visionRx.OrganizationId, visionRx.Subscription);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                }
            }
            return result;
        }

        /// <summary>
        ///  Delete an existing Record By Id
        /// </summary>
        [HttpDelete("{id:guid}/{OrgID:Guid}/{Subscription:bool}")]
        public async Task<IActionResult> DeleteById(Guid id, Guid OrgID, bool Subscription)
        {
            IActionResult result;
            try
            {
                await _visionExaminationCommandHandler.DeleteVisionRecordById(id, OrgID, Subscription);
                result = Ok(_localizer["SuccessfulDeletion"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
            }
            return result;
        }

        [HttpGet("{id:Guid}/{OrgID:Guid}/{Subscription:bool}")]
        public async Task<ActionResult<List<VisionRx>>> GetById(Guid id, Guid OrgID, bool Subscription)
        {
            try
            {
                var visionExamination = await _visionExaminationQueryHandler.GetVisionExaminationById(id, OrgID, Subscription);

                if (visionExamination == null || !visionExamination.Any())
                {
                    return NotFound(_localizer["RecordNotFound"]);
                }

                return Ok(visionExamination);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500, _localizer["GetLogError"]);
            }
        }

        /// <summary>
        /// Add new VisionExamination Record
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> AddVisionExamination([FromBody] VisionRx visionExamination)
        {
            IActionResult response;
            if (visionExamination == null)
            {
                _logger.LogWarning(_localizer["InvalidAddressData"]);
                response = BadRequest(_localizer["AddressInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["AddingNewAddress"]);
                    await _visionExaminationCommandHandler.AddVisionExamination(new List<VisionRx> { visionExamination }, visionExamination.OrganizationId, visionExamination.Subscription);
                    _logger.LogInformation(_localizer["AddressAddedSuccessfully"], visionExamination.ExaminationId);
                    response = Ok(_localizer["AddSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorAddingAddress"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        /// <summary>
        ///  Get Active visionExamination Record by Id 
        /// </summary>
        [HttpGet("{id:Guid}/{OrgID:Guid}/{Subscription:bool}/isActive")]
        public async Task<ActionResult<IEnumerable<VisionRx>>> GetAllByIdAndIsActive(Guid id, Guid OrgID, bool Subscription)
        {
            ActionResult result;
            try
            {
                var visionExamination = await _visionExaminationQueryHandler.GetVisionExaminationByIdAndIsActive(id, OrgID, Subscription);
                result = visionExamination != null ? Ok(visionExamination) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

    }
}
