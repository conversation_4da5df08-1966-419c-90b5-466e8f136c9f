using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class SymptomsCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<SymptomsCommandHandler>> _loggerMock;
        private Mock<ISymptomsRepository> _symptomsRepositoryMock;
        private SymptomsCommandHandler _symptomsCommandHandler;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<SymptomsCommandHandler>>();
            _symptomsRepositoryMock = new Mock<ISymptomsRepository>();

            _unitOfWorkMock.Setup(u => u.SymptomsRepository).Returns(_symptomsRepositoryMock.Object);
            _symptomsCommandHandler = new SymptomsCommandHandler(_configurationMock.Object, _unitOfWorkMock.Object, _loggerMock.Object);
        }

        [Test]
        public async Task AddSymptom_ShouldAddSymptomsToRepository()
        {
            // Arrange
            var symptoms = new List<Symptoms>
            {
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Fever"
                },
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Headache"
                }
            };

            _symptomsRepositoryMock.Setup(r => r.AddAsync(It.IsAny<IEnumerable<Symptoms>>()))
                .Returns(Task.CompletedTask);
            
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _symptomsCommandHandler.AddSymptom(symptoms);

            // Assert
            _symptomsRepositoryMock.Verify(r => r.AddAsync(It.Is<IEnumerable<Symptoms>>(s => 
                s.Count() == symptoms.Count() && 
                s.All(symptom => symptoms.Any(x => x.SymptomId == symptom.SymptomId && x.Symptom == symptom.Symptom)))), 
                Times.Once);
            
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task AddSymptom_WithEmptyList_ShouldStillCallRepositoryMethods()
        {
            // Arrange
            var symptoms = new List<Symptoms>();

            _symptomsRepositoryMock.Setup(r => r.AddAsync(It.IsAny<IEnumerable<Symptoms>>()))
                .Returns(Task.CompletedTask);
            
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _symptomsCommandHandler.AddSymptom(symptoms);

            // Assert
            _symptomsRepositoryMock.Verify(r => r.AddAsync(It.Is<IEnumerable<Symptoms>>(s => !s.Any())), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task AddSymptom_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            var symptoms = new List<Symptoms>
            {
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Fever"
                }
            };

            _symptomsRepositoryMock.Setup(r => r.AddAsync(It.IsAny<IEnumerable<Symptoms>>()))
                .ThrowsAsync(new Exception("Database connection error"));

            // Act & Assert
            await FluentActions.Invoking(async () => await _symptomsCommandHandler.AddSymptom(symptoms))
                .Should().ThrowAsync<Exception>()
                .WithMessage("Database connection error");
            
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Test]
        public async Task AddSymptom_WhenSaveThrowsException_ShouldPropagateException()
        {
            // Arrange
            var symptoms = new List<Symptoms>
            {
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Fever"
                }
            };

            _symptomsRepositoryMock.Setup(r => r.AddAsync(It.IsAny<IEnumerable<Symptoms>>()))
                .Returns(Task.CompletedTask);
            
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ThrowsAsync(new Exception("Save operation failed"));

            // Act & Assert
            await FluentActions.Invoking(async () => await _symptomsCommandHandler.AddSymptom(symptoms))
                .Should().ThrowAsync<Exception>()
                .WithMessage("Save operation failed");
            
            _symptomsRepositoryMock.Verify(r => r.AddAsync(It.IsAny<IEnumerable<Symptoms>>()), Times.Once);
        }
    }
}

