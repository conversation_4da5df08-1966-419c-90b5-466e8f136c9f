using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MemberServiceBusinessLayer;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class PageRoleMappingQueryHandler : IPageRoleMappingQueryHandler<PageRoleMapping>
    {
        private readonly IUnitOfWork _unitOfWork;

        public PageRoleMappingQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<PageRoleMapping> GetPageRoleMappingByIdAsync(Guid id, Guid OrganizationId, bool Subscription)
        {
            var pageRoleMapping = await _unitOfWork.PageRoleMappingRepository.GetByIdAsync(id, OrganizationId, Subscription);
            return pageRoleMapping;
        }

        public async Task<List<PageRoleMapping>> GetPageRoleMappingsByPagePathAsync(string pagePath, Guid OrganizationId, bool Subscription)
        {
            var pageRoleMappings = await _unitOfWork.PageRoleMappingRepository.GetAllAsync(OrganizationId,Subscription);
            return pageRoleMappings.Where(prm => prm.PagePath.Contains(pagePath, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        public async Task<List<PageRoleMapping>> GetAllPageRoleMappingsAsync()
        {
            var pageRoleMappings = await _unitOfWork.PageRoleMappingRepository.GetAllAsync();
            return pageRoleMappings.ToList();
        }

        public async Task<List<PageRoleMapping>> GetPagesByRoleIdAsync(Guid roleId, Guid OrganizationId, bool Subscription)
        {
            var pageRoleMappings = await _unitOfWork.PageRoleMappingRepository.GetAllAsync(OrganizationId, Subscription);
            return pageRoleMappings.Where(prm => prm.RoleId == roleId & prm.HasAccess).ToList();
        }

        public async Task<List<string>> GetRolesByPagePathAsync(string pagePath, Guid OrganizationId, bool Subscription)
        {
            if (string.IsNullOrWhiteSpace(pagePath))
                throw new ArgumentException("Page path cannot be null or whitespace.", nameof(pagePath));

            var roles = await _unitOfWork.PageRoleMappingRepository.GetRolesByPagePathAsync(pagePath, OrganizationId, Subscription);
            return roles;
        }
    }
}
