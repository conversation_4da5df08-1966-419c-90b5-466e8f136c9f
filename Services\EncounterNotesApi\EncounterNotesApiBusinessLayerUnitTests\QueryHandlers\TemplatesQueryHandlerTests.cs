using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class TemplatesQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ITemplatesRepository> _templatesRepositoryMock;
        private TemplatesQueryHandler _templatesQueryHandler;
        private Guid _orgId;
        private bool _subscription;
        private Guid _pcpId;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _templatesRepositoryMock = new Mock<ITemplatesRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;
            _pcpId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.TemplatesRepository).Returns(_templatesRepositoryMock.Object);
            _templatesQueryHandler = new TemplatesQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetTemplates_ShouldReturnAllTemplatesForOrganization()
        {
            // Arrange
            var templates = new List<Templates>
            {
                new Templates
                {
                    Id = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = _pcpId,
                    TemplateName = "General Examination",
                    CreatedDate = DateTime.Now.AddDays(-30),
                    IsDefault = true,
                    Template = "<template content>",
                    VisitType = "Initial Visit",
                    Subscription = _subscription
                },
                new Templates
                {
                    Id = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    TemplateName = "Follow-up Visit",
                    CreatedDate = DateTime.Now.AddDays(-20),
                    IsDefault = false,
                    Template = "<template content>",
                    VisitType = "Follow-up",
                    Subscription = _subscription
                }
            };

            _templatesRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(templates);

            // Act
            var result = await _templatesQueryHandler.GetTemplates(_orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.Should().BeEquivalentTo(templates);
        }

        [Test]
        public async Task GetTemplates_WithNoTemplates_ShouldReturnEmptyCollection()
        {
            // Arrange
            var templates = new List<Templates>();

            _templatesRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(templates);

            // Act
            var result = await _templatesQueryHandler.GetTemplates(_orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetTemplatesById_ShouldReturnTemplateWithMatchingId()
        {
            // Arrange
            var templateId = Guid.NewGuid();
            var template = new Templates
            {
                Id = templateId,
                OrganizationId = _orgId,
                PCPId = _pcpId,
                TemplateName = "General Examination",
                CreatedDate = DateTime.Now.AddDays(-30),
                IsDefault = true,
                Template = "<template content>",
                VisitType = "Initial Visit",
                Subscription = _subscription
            };

            _templatesRepositoryMock.Setup(r => r.GetByIdAsync(templateId, _orgId, _subscription))
                .ReturnsAsync(template);

            // Act
            var result = await _templatesQueryHandler.GetTemplatesById(templateId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(template);
        }

        [Test]
        public async Task GetTemplatesById_WithNonExistentId_ShouldReturnNull()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid();

            _templatesRepositoryMock.Setup(r => r.GetByIdAsync(nonExistentId, _orgId, _subscription))
                .ReturnsAsync((Templates)null);

            // Act
            var result = await _templatesQueryHandler.GetTemplatesById(nonExistentId, _orgId, _subscription);

            // Assert
            result.Should().BeNull();
        }

        [Test]
        public async Task GetTemplatesByPCPId_ShouldReturnTemplatesForPCP()
        {
            // Arrange
            var templates = new List<Templates>
            {
                new Templates
                {
                    Id = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = _pcpId,
                    TemplateName = "General Examination",
                    CreatedDate = DateTime.Now.AddDays(-30),
                    IsDefault = true,
                    Template = "<template content>",
                    VisitType = "Initial Visit",
                    Subscription = _subscription
                },
                new Templates
                {
                    Id = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = _pcpId,
                    TemplateName = "Follow-up Visit",
                    CreatedDate = DateTime.Now.AddDays(-20),
                    IsDefault = false,
                    Template = "<template content>",
                    VisitType = "Follow-up",
                    Subscription = _subscription
                }
            };

            _templatesRepositoryMock.Setup(r => r.GetTemplatesByPCPId(_pcpId, _orgId, _subscription))
                .ReturnsAsync(templates);

            // Act
            var result = await _templatesQueryHandler.GetTemplatesByPCPId(_pcpId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.Should().BeEquivalentTo(templates);
        }

        [Test]
        public async Task GetTemplatesByPCPIdAndVisitType_ShouldReturnTemplatesForPCPAndVisitType()
        {
            // Arrange
            var visitType = "Initial Visit";
            var templates = new List<Templates>
            {
                new Templates
                {
                    Id = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = _pcpId,
                    TemplateName = "General Examination",
                    CreatedDate = DateTime.Now.AddDays(-30),
                    IsDefault = true,
                    Template = "<template content>",
                    VisitType = visitType,
                    Subscription = _subscription
                }
            };

            _templatesRepositoryMock.Setup(r => r.GetTemplatesByPCPIdAndVisitType(_pcpId, visitType, _orgId, _subscription))
                .ReturnsAsync(templates);

            // Act
            var result = await _templatesQueryHandler.GetTemplatesByPCPIdAndVisitType(_pcpId, visitType, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.Should().BeEquivalentTo(templates);
        }
    }
}
