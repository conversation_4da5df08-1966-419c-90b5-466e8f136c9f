using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class TherapeuticInterventionsCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<TherapeuticInterventionsCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, TherapeuticInterventionsData, EncounterDataAccessLayer>> _migrationMock;
        private Mock<ITherapeuticInterventionsRepository> _therapeuticInterventionsRepositoryMock;
        private TherapeuticInterventionsCommandHandler _therapeuticInterventionsCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<TherapeuticInterventionsCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, TherapeuticInterventionsData, EncounterDataAccessLayer>>();
            _therapeuticInterventionsRepositoryMock = new Mock<ITherapeuticInterventionsRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;

            _unitOfWorkMock.Setup(u => u.TherapeuticInterventionsRepository).Returns(_therapeuticInterventionsRepositoryMock.Object);
            _therapeuticInterventionsCommandHandler = new TherapeuticInterventionsCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object);
        }

        [Test]
        public async Task AddTherapeuticInterventions_ShouldAddInterventionsToRepository()
        {
            // Arrange
            var interventions = new List<TherapeuticInterventionsData>
            {
                new TherapeuticInterventionsData
                {
                    TherapeuticInterventionsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now,
                    PCPId = Guid.NewGuid(),
                    TherapyType = "Physical Therapy",
                    Notes = "Therapeutic exercise for strengthening",
                    IsActive = true,
                    Subscription = _subscription
                },
                new TherapeuticInterventionsData
                {
                    TherapeuticInterventionsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now,
                    PCPId = Guid.NewGuid(),
                    TherapyType = "Occupational Therapy",
                    Notes = "Activities of daily living training",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _therapeuticInterventionsRepositoryMock.Setup(r => r.AddAsync(It.IsAny<List<TherapeuticInterventionsData>>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _therapeuticInterventionsCommandHandler.AddTherapeuticInterventions(interventions, _orgId, _subscription);

            // Assert
            _therapeuticInterventionsRepositoryMock.Verify(r => r.AddAsync(interventions, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateTherapeuticInterventions_ShouldUpdateInterventionInRepository()
        {
            // Arrange
            var intervention = new TherapeuticInterventionsData
            {
                TherapeuticInterventionsID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                CreatedDate = DateTime.Now.AddDays(-1),
                UpdatedDate = DateTime.Now,
                PCPId = Guid.NewGuid(),
                TherapyType = "Physical Therapy",
                Notes = "Updated therapeutic exercise for strengthening",
                IsActive = true,
                Subscription = _subscription
            };

            _therapeuticInterventionsRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<TherapeuticInterventionsData>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _therapeuticInterventionsCommandHandler.UpdateTherapeuticInterventions(intervention, _orgId, _subscription);

            // Assert
            _therapeuticInterventionsRepositoryMock.Verify(r => r.UpdateAsync(intervention, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteTherapeuticInterventionsById_ShouldDeleteInterventionFromRepository()
        {
            // Arrange
            var interventionId = Guid.NewGuid();

            _therapeuticInterventionsRepositoryMock.Setup(r => r.DeleteByIdAsync(interventionId, _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _therapeuticInterventionsCommandHandler.DeleteTherapeuticInterventionsById(interventionId, _orgId, _subscription);

            // Assert
            _therapeuticInterventionsRepositoryMock.Verify(r => r.DeleteByIdAsync(interventionId, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteTherapeuticInterventionsByEntity_ShouldDeleteInterventionFromRepository()
        {
            // Arrange
            var intervention = new TherapeuticInterventionsData
            {
                TherapeuticInterventionsID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                TherapyType = "Physical Therapy",
                IsActive = false,
                Subscription = _subscription
            };

            _therapeuticInterventionsRepositoryMock.Setup(r => r.DeleteByEntityAsync(intervention, _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _therapeuticInterventionsCommandHandler.DeleteTherapeuticInterventionsByEntity(intervention, _orgId, _subscription);

            // Assert
            _therapeuticInterventionsRepositoryMock.Verify(r => r.DeleteByEntityAsync(intervention, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateTherapeuticInterventionsList_ShouldUpdateInterventionsInRepository()
        {
            // Arrange
            var interventions = new List<TherapeuticInterventionsData>
            {
                new TherapeuticInterventionsData
                {
                    TherapeuticInterventionsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    UpdatedDate = DateTime.Now,
                    PCPId = Guid.NewGuid(),
                    TherapyType = "Physical Therapy",
                    Notes = "Updated notes",
                    IsActive = true,
                    Subscription = _subscription
                },
                new TherapeuticInterventionsData
                {
                    TherapeuticInterventionsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    UpdatedDate = DateTime.Now,
                    PCPId = Guid.NewGuid(),
                    TherapyType = "Occupational Therapy",
                    Notes = "Updated notes",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _therapeuticInterventionsRepositoryMock.Setup(r => r.UpdateRangeAsync(interventions, _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _therapeuticInterventionsCommandHandler.UpdateTherapeuticInterventionsList(interventions, _orgId, _subscription);

            // Assert
            _therapeuticInterventionsRepositoryMock.Verify(r => r.UpdateRangeAsync(interventions, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}

