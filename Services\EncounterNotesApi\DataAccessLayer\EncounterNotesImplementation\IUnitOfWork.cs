using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IUnitOfWork : IDisposable
    {
        IRxNormConceptRepository RxNormConceptRepository { get; }
        IFDBMedicationRepository FDBMedicationRepository { get; }
        IFDBMedicationNameRepository FDBMedicationNameRepository { get; }
        IFDBRoutedMedicationRepository FDBRoutedMedicationRepository { get; }
        IFDBRoutedDosageFormMedicationRepository FDBRoutedDosageFormMedicationRepository { get; }
        IFDBRouteLookUpRepository FDBRouteLookUpRepository { get; }
        IFDBTakeLookUpRepository FDBTakeLookUpRepository { get; }
        IFDBAllergyRepository FDBAllergyRepository { get; }
        IFDBVaccineRepository FDBVaccineRepository { get; }
        IFDBVaccine_CPT_CVXRepository FDBVaccine_CPT_CVXRepository { get; }
        IFDB_ICDRepository FDBICDRepository { get; }
        IRecordRepository RecordRepository { get; }
        ITemplatesRepository TemplatesRepository { get; }
        IPredefinedTemplatesRepository PredefinedTemplatesRepository { get; }
        IFamilyMemberRepository FamilyMemberRepository { get; }
        IHospitalizationRecordRepository HospitalizationRecordRepository { get; }
        IRelationRepository RelationRepository { get; }
        ICurrentMedicationRepository CurrentMedicationRepository { get; }
        ISocialHistoryRepository SocialHistoryRepository { get; }
        ISurgicalRepository SurgicalRepository { get; }
        IDiagnosticImagingAssessmentRepository DiagnosticImagingAssessmentRepository { get; }
        IImmunizationRepository ImmunizationRepository { get; }
        IVaccinesRepository VaccinesRepository { get; }
        IPhysicalExaminationRepository PhysicalExaminationRepository { get; }
        IPastResultsRepository PastResultsRepository { get; }
        IVisionExaminationRepository VisionExaminationRepository { get; }

        IDiagnosticImagingRepository DiagnosticImagingRepository { get; }
        IDiagnosticImagingPageRepository DiagnosticImagingPageRepository { get; }
        ISoapNotesComponentsRepository SoapNotesComponentsRepository { get; }
        IICDRepository ICDRepository { get; }
        ITherapeuticInterventionsListRepository TherapeuticInterventionsListRepository { get; }
        IMedicalHistoryRepository MedicalHistoryRepository { get; }
        IHistoryOfPresentIllnessRepository HistoryOfPresentIllnessRepository { get; }
        ISymptomsRepository SymptomsRepository { get; }
        IAllergyRepository AllergyRepository { get; }
        IChiefComplaintRepository<ChiefComplaint> ChiefComplaintRepository { get; }
        IPhysicalTherapyRepository PhysicalTherapyRepository { get; }
        IAssessmentsRepository AssessmentsRepository { get; }
        ITherapeuticInterventionsRepository TherapeuticInterventionsRepository { get; }
        IReviewOfSystemRepository ReviewOfSystemRepository { get; }
        IVitalRepository VitalRepository { get; }
        IReferralOutgoingRepository ReferralOutgoingRepository { get; }

        IProcedureRepository ProcedureRepository { get; }
        ICPTRepository CPTRepository { get; }

        IPrescriptionMedicationRepository PrescriptionMedicationRepository { get; }
        IObHistoryRepository<ObHistory> ObHistoryRepository { get; }
        IGynHistoryRepository<GynHistory> GynHistoryRepository { get; }
        IExaminationRepository ExaminationRepository { get; }
        ILabTestsRepository LabTestsRepository { get; }
        IBillingRepository BillingRepository { get; }
        Task<int> SaveAsync();
    }
}