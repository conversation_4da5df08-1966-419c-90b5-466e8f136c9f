﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IFDBRoutedMedicationRepository : IGenericRepository<FDBRoutedMedication>
    {
        Task<List<FDBRoutedMedication>> GetRoutedMedicationByMED_NAME_ID(string MED_NAME_ID);
    }
}
