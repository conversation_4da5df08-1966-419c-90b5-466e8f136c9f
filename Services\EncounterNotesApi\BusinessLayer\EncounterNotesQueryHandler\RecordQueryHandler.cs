﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class RecordQueryHandler : IRecordQueryHandler<Record>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public RecordQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<Record>> GetRecord(Guid OrgId, bool Subscription)
        {
            var records = await _unitOfWork.RecordRepository.GetAsync(OrgId, Subscription);
            foreach (var record in records)
            {
                record.WordTimings = await _unitOfWork.RecordRepository.GetWordTimings(record.Id, OrgId, Subscription);
            }
            return records;
        }

        public async Task<Record> GetRecordById(Guid id, Guid OrgId, bool Subscription)
        {
            return await _unitOfWork.RecordRepository.GetByIdAsync(id, OrgId, Subscription);
        }

        public async Task<IEnumerable<Record>> GetRecordByPCPId(Guid id, Guid OrgId, bool Subscription)
        {
            return await _unitOfWork.RecordRepository.GetByPCPIdAsync(id, OrgId, Subscription);
        }

        public async Task<IEnumerable<Record>> GetRecordByPatientId(Guid id, Guid OrgId, bool Subscription)
        {
            return await _unitOfWork.RecordRepository.GetByPatientIdAsync(id, OrgId, Subscription);
        }
    }
}
