﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Web.Resource;
using System;
using System.Linq;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesService.EncounterNotesServiceResources;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Sprache;
using ShardModels;


namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class AllergiesController : ControllerBase
    {
        private readonly RecordDatabaseContext dbContext;
        private readonly IAllergyCommandHandler<Allergy> _allergyDataHandler;
        private readonly IAllergyQueryHandler<Allergy> _allergyQueryHandler;
        private readonly ILogger<AllergiesController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;
        private readonly IUnitOfWork _unitOfWork;

        public AllergiesController(
            IAllergyCommandHandler<Allergy> allergyDataHandler,
            IAllergyQueryHandler<Allergy> allergyQueryHandler,
            ILogger<AllergiesController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer,
            IUnitOfWork unitOfWork
            )
        {
            _allergyDataHandler = allergyDataHandler;
            _allergyQueryHandler = allergyQueryHandler;
            _logger = logger;
            _localizer = localizer;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get all by ID
        /// </summary>
        [HttpGet("{id:guid}/{OrgId:guid}/{Subscription}")]
        public async Task<ActionResult<IEnumerable<Allergy>>> GetAllById(Guid id,  Guid OrgId,  bool Subscription)
        {
            ActionResult result;
            try
            {
                var allergy = await _allergyQueryHandler.GetAllAllergyById(id, OrgId, Subscription);
                result = allergy != null ? Ok(allergy) : NotFound(_localizer["AccessNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogErrorAllergy"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogErrorAllergy"]);
            }
            return result;
        }

        /// <summary>
        /// Get only Active Allergies data for an ID
        /// </summary>
        [HttpGet("{id:guid}/isActive/{OrgId:guid}/{Subscription}")]
        public async Task<ActionResult<IEnumerable<Allergy>>> GetAllByIdAndIsActive(Guid id,  Guid OrgId,  bool Subscription)
        {
            ActionResult result;
            try
            {
                var allergy = await _allergyQueryHandler.GetAllergyByIdAndIsActive(id, OrgId, Subscription);
                result = allergy != null ? Ok(allergy) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogErrorAllergy"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogErrorAllergy"]);
            }
            return result;
        }

        /// <summary>
        /// Add List Of new Medication
        /// </summary>
        [HttpPost]
        [Route("AddAllergy/{OrgId:guid}/{Subscription}")]
        public async Task<IActionResult> Registration([FromBody] List<Allergy> allergies,  Guid OrgId,  bool Subscription)
        {
            if (allergies == null || allergies.Count == 0)
            {
                return BadRequest(_localizer["NoLicense"]);
            }

            try
            {
                await _allergyDataHandler.AddAllergy(allergies, OrgId, Subscription); 
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException ex)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogErrorAllergy"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Delete Medication
        /// </summary>
        [HttpDelete]
        [Route("{OrgId:guid}/{Subscription}")]
        public async Task<IActionResult> DeleteByEntity([FromBody] Allergy allergy,  Guid OrgId,  bool Subscription)
        {
            IActionResult result;
            if (allergy == null)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _allergyDataHandler.DeleteAllergyByEntity(allergy, OrgId, Subscription); 
                    result = Ok(_localizer["DeleteSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["DeleteLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
                }
            }
            return result;
        }

        /// <summary>
        /// Update Single Allergy data By PatientID and MedicineID (DrugId)
        /// </summary>
        [HttpPut("{patientId:guid}/{medicineId:guid}/{OrgId:guid}/{Subscription}")]
        public async Task<IActionResult> UpdateAllergyById(Guid patientId, Guid medicineId, [FromBody] Allergy allergy,  Guid OrgId,  bool Subscription)
        {
            IActionResult result;
            if (allergy == null || allergy.PatientId != patientId || allergy.MedicineId != medicineId)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _allergyDataHandler.UpdateAllergy(allergy, OrgId, Subscription); 
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                }
            }
            return result;
        }

        [HttpPut]
        [Route("UpdateAllergyList/{OrgId:guid}/{Subscription}")]
        public async Task<IActionResult> UpdateAllergyList([FromBody] UpdateAllergyListRequest request,  Guid OrgId,  bool Subscription)
        {
            IActionResult result;
            try
            {
                foreach (var allergy in request.NewAllergies)
                {
                    _unitOfWork.AllergyRepository.AddAsync(allergy, OrgId, Subscription); 
                }

                foreach (var allergy in request.UpdatedAllergies)
                {
                    var existingAllergy = await _unitOfWork.AllergyRepository.GetByIdAsync(request.PatientId, allergy.MedicineId, OrgId, Subscription);

                    if (existingAllergy != null)
                    {
                        existingAllergy.DrugName = allergy.DrugName;
                        existingAllergy.Classification = allergy.Classification;
                        existingAllergy.Agent = allergy.Agent;
                        existingAllergy.Reaction = allergy.Reaction;
                        existingAllergy.Type = allergy.Type;
                        existingAllergy.UpdatedBy = allergy.UpdatedBy;
                        existingAllergy.UpdatedOn = DateTime.UtcNow;
                        _unitOfWork.AllergyRepository.UpdateAsync(existingAllergy, OrgId, Subscription); 
                    }
                }

                foreach (var allergy in request.DeletedAllergies)
                {
                    var existingAllergy = await _unitOfWork.AllergyRepository.GetByIdAsync(request.PatientId, allergy.MedicineId, OrgId, Subscription);

                    if (existingAllergy != null)
                    {
                        existingAllergy.isActive = false; // Soft delete
                        _unitOfWork.AllergyRepository.UpdateAsync(existingAllergy, OrgId, Subscription); 
                    }
                }

                await _unitOfWork.SaveAsync();

                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                result = StatusCode(500, _localizer["UpdateLogError"]);
            }

            return result;
        }

    }
}