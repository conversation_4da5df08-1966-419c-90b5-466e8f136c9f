﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IBillingRepository
    {
        Task AddAsync(Billing billing);
        Task UpdateAsync(Billing billing);
        Task DeleteByIdAsync(Guid id);
        Task<Billing> GetByIdAsync(Guid id);
        Task<List<Billing>> GetAllAsync();
        Task<List<Billing>> GetByPatientIdAsync(Guid patientId);
    }
}
