﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IRolesQueryHandler<T>
    {
        Task<T> GetRoleByIdAsync(Guid id, Guid OrgID, bool Subscription);
        Task<List<T>> GetRolesByNameAsync(string name,Guid OrgID, bool Subscription);
        Task<IEnumerable<T>> GetAllRolesAsync();
        Task<IEnumerable<T>> GetRolesByOrgAsync(Guid id, bool Subscription);
    }
}
