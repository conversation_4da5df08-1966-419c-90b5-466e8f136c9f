using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class MedicalHistoryControllerTests
    {
        private Mock<IMedicalHistoryCommandHandler<MedicalHistory>> _medicalCommandHandlerMock;
        private Mock<IMedicalHistoryQueryHandler<MedicalHistory>> _medicalQueryHandlerMock;
        private Mock<ILogger<MedicalHistoryController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private MedicalHistoryController _controller;
        private List<MedicalHistory> _testMedicalHistories;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _medicalCommandHandlerMock = new Mock<IMedicalHistoryCommandHandler<MedicalHistory>>();
            _medicalQueryHandlerMock = new Mock<IMedicalHistoryQueryHandler<MedicalHistory>>();
            _loggerMock = new Mock<ILogger<MedicalHistoryController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No license available."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error occurred."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error posting data."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting data."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Delete successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testMedicalHistories = new List<MedicalHistory>
            {
                new MedicalHistory {
                    MedicalHistoryID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    History = "Hypertension diagnosed in 2015",
                    IsActive = true,
                    Subscription = _isSubscription
                },
                new MedicalHistory {
                    MedicalHistoryID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-15),
                    History = "Type 2 diabetes diagnosed in 2018",
                    IsActive = true,
                    Subscription = _isSubscription
                }
            };

            _controller = new MedicalHistoryController(
                _medicalCommandHandlerMock.Object,
                _medicalQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetAllById_ReturnsMedicalHistories_WhenMedicalHistoriesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientMedicalHistories = _testMedicalHistories.Select(m => { m.PatientId = patientId; return m; }).ToList();
            _medicalQueryHandlerMock.Setup(x => x.GetAllMedicalHistoriesById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientMedicalHistories);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedMedicalHistories = okResult.Value as IEnumerable<MedicalHistory>;
            returnedMedicalHistories.Should().NotBeNull();
            returnedMedicalHistories.Should().BeEquivalentTo(patientMedicalHistories);
        }

        [Test]
        public async Task GetAllById_ReturnsNotFound_WhenNoMedicalHistoriesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _medicalQueryHandlerMock.Setup(x => x.GetAllMedicalHistoriesById(patientId, _orgId, _isSubscription))
                .ReturnsAsync((IEnumerable<MedicalHistory>)null);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsMedicalHistories_WhenActiveMedicalHistoriesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activeMedicalHistories = _testMedicalHistories.Select(m => { m.PatientId = patientId; m.IsActive = true; return m; }).ToList();
            _medicalQueryHandlerMock.Setup(x => x.GetMedicalHistoryByIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activeMedicalHistories);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedMedicalHistories = okResult.Value as IEnumerable<MedicalHistory>;
            returnedMedicalHistories.Should().NotBeNull();
            returnedMedicalHistories.Should().BeEquivalentTo(activeMedicalHistories);
        }

        [Test]
        public async Task AddMedicalHistory_ValidMedicalHistories_ReturnsOk()
        {
            // Arrange
            var medicalHistories = _testMedicalHistories;

            // Act
            var result = await _controller.AddMedicalHistory(medicalHistories, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _medicalCommandHandlerMock.Verify(x => x.AddMedicalHistory(medicalHistories, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task AddMedicalHistory_EmptyList_ReturnsBadRequest()
        {
            // Arrange
            var emptyMedicalHistories = new List<MedicalHistory>();

            // Act
            var result = await _controller.AddMedicalHistory(emptyMedicalHistories, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);
        }

        [Test]
        public async Task UpdateMedicalHistoryById_ValidMedicalHistory_ReturnsOk()
        {
            // Arrange
            var medicalHistory = _testMedicalHistories[0];

            // Act
            var result = await _controller.UpdateMedicalHistoryById(medicalHistory.PatientId, medicalHistory, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _medicalCommandHandlerMock.Verify(x => x.UpdateMedicalHistory(medicalHistory, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateMedicalHistoryList_ValidMedicalHistories_ReturnsOk()
        {
            // Arrange
            var medicalHistories = _testMedicalHistories;

            // Act
            var result = await _controller.UpdateMedicalHistoryList(medicalHistories, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _medicalCommandHandlerMock.Verify(x => x.UpdateMedicalHistoryList(medicalHistories, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task DeleteByEntity_ValidMedicalHistory_ReturnsOk()
        {
            // Arrange
            var medicalHistory = _testMedicalHistories[0];

            // Act
            var result = await _controller.DeleteByEntity(medicalHistory, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _medicalCommandHandlerMock.Verify(x => x.DeleteMedicalHistoryByEntity(medicalHistory, _orgId, _isSubscription), Times.Once);
        }


    }
}
