using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class PredefinedTemplatesControllerTests
    {
        private Mock<IPredefinedTemplatesCommandHandler<PredefinedTemplates>> _predefinedTemplatesCommandHandlerMock;
        private Mock<IPredefinedTemplatesQueryHandler<PredefinedTemplates>> _predefinedTemplatesQueryHandlerMock;
        private Mock<ILogger<PredefinedTemplatesController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private PredefinedTemplatesController _controller;
        private List<PredefinedTemplates> _testPredefinedTemplates;

        [SetUp]
        public void Setup()
        {
            _predefinedTemplatesCommandHandlerMock = new Mock<IPredefinedTemplatesCommandHandler<PredefinedTemplates>>();
            _predefinedTemplatesQueryHandlerMock = new Mock<IPredefinedTemplatesQueryHandler<PredefinedTemplates>>();
            _loggerMock = new Mock<ILogger<PredefinedTemplatesController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["SuccessfulDeletion"]).Returns(new LocalizedString("SuccessfulDeletion", "Deletion successful."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting data."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Delete successful."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No license available."));

            // Create test data
            _testPredefinedTemplates = new List<PredefinedTemplates>
            {
                new PredefinedTemplates
                {
                    Id = Guid.NewGuid(),
                    Template = "Template for general examination",
                    TemplateName = "General Examination",
                    IsDefault = true,
                    VisitType = "General"
                },
                new PredefinedTemplates
                {
                    Id = Guid.NewGuid(),
                    Template = "Template for follow-up visit",
                    TemplateName = "Follow-up Visit",
                    IsDefault = false,
                    VisitType = "Follow-up"
                }
            };

            _controller = new PredefinedTemplatesController(
                _predefinedTemplatesCommandHandlerMock.Object,
                _predefinedTemplatesQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task Get_ReturnsOk_WhenTemplatesExist()
        {
            // Arrange
            _predefinedTemplatesQueryHandlerMock.Setup(x => x.GetTemplates())
                .ReturnsAsync(_testPredefinedTemplates);

            // Act
            var result = await _controller.Get();

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().BeEquivalentTo(_testPredefinedTemplates);
        }

        [Test]
        public async Task Get_ReturnsOk_WhenNoTemplatesExist()
        {
            // Arrange
            _predefinedTemplatesQueryHandlerMock.Setup(x => x.GetTemplates())
                .ReturnsAsync((List<PredefinedTemplates>)null);

            // Act
            var result = await _controller.Get();

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().BeNull();
        }

        [Test]
        public async Task GetById_ReturnsOk_WhenTemplateExists()
        {
            // Arrange
            var templateId = _testPredefinedTemplates[0].Id;
            _predefinedTemplatesQueryHandlerMock.Setup(x => x.GetTemplatesById(templateId))
                .ReturnsAsync(_testPredefinedTemplates[0]);

            // Act
            var result = await _controller.GetById(templateId);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().BeEquivalentTo(_testPredefinedTemplates[0]);
        }

        [Test]
        public async Task GetById_ReturnsNotFound_WhenTemplateDoesNotExist()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid();
            _predefinedTemplatesQueryHandlerMock.Setup(x => x.GetTemplatesById(nonExistentId))
                .ReturnsAsync((PredefinedTemplates)null);

            // Act
            var result = await _controller.GetById(nonExistentId);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task UpdateById_ReturnsOk_WhenUpdateSuccessful()
        {
            // Arrange
            var template = _testPredefinedTemplates[0];
            var id = template.Id;

            // Act
            var result = await _controller.UpdateById(id, template);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _predefinedTemplatesCommandHandlerMock.Verify(x => x.UpdateTemplates(template), Times.Once);
        }

        [Test]
        public async Task UpdateById_ReturnsBadRequest_WhenIdMismatch()
        {
            // Arrange
            var template = _testPredefinedTemplates[0];
            var wrongId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdateById(wrongId, template);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["InvalidRecord"]);

            // Verify the command handler was not called
            _predefinedTemplatesCommandHandlerMock.Verify(x => x.UpdateTemplates(It.IsAny<PredefinedTemplates>()), Times.Never);
        }

        [Test]
        public async Task DeleteById_ReturnsOk_WhenDeleteSuccessful()
        {
            // Arrange
            var id = Guid.NewGuid();

            // Act
            var result = await _controller.DeleteById(id);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulDeletion"]);

            // Verify the command handler was called
            _predefinedTemplatesCommandHandlerMock.Verify(x => x.DeleteTemplatesById(id), Times.Once);
        }

        [Test]
        public async Task DeleteByEntity_ReturnsOk_WhenDeleteSuccessful()
        {
            // Arrange
            var template = _testPredefinedTemplates[0];

            // Act
            var result = await _controller.DeleteByEntity(template);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _predefinedTemplatesCommandHandlerMock.Verify(x => x.DeleteTemplatesByEntity(template), Times.Once);
        }

        [Test]
        public async Task EncounterNote_ReturnsOk_WhenAddSuccessful()
        {
            // Arrange
            var templates = _testPredefinedTemplates;

            // Act
            var result = await _controller.EncounterNote(templates);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _predefinedTemplatesCommandHandlerMock.Verify(x => x.AddTemplates(templates), Times.Once);
        }

        [Test]
        public async Task EncounterNote_ReturnsOk_WhenEmptyList()
        {
            // Arrange
            var emptyList = new List<PredefinedTemplates>();

            // Act
            var result = await _controller.EncounterNote(emptyList);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _predefinedTemplatesCommandHandlerMock.Verify(x => x.AddTemplates(emptyList), Times.Once);
        }
    }
}
