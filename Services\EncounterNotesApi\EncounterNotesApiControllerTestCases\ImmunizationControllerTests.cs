using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class ImmunizationControllerTests
    {
        private Mock<IImmunizationCommandHandler<Immunization>> _immunizationCommandHandlerMock;
        private Mock<IImmunizationQueryHandler<Immunization>> _immunizationQueryHandlerMock;
        private Mock<ILogger<SurgicalHistoryController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private ImmunizationController _controller;
        private List<Immunization> _testImmunizations;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _immunizationCommandHandlerMock = new Mock<IImmunizationCommandHandler<Immunization>>();
            _immunizationQueryHandlerMock = new Mock<IImmunizationQueryHandler<Immunization>>();
            _loggerMock = new Mock<ILogger<SurgicalHistoryController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No license available."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error occurred."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error posting data."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting data."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Delete successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testImmunizations = new List<Immunization>
            {
                new Immunization {
                    ImmunizationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Immunizations = "Influenza",
                    CPTCode = "90658",
                    CVXCode = "141",
                    Comments = "Annual flu shot",
                    CPTDescription = "Influenza virus vaccine, trivalent",
                    GivenDate = DateTime.Now.AddDays(-30),
                    IsActive = true,
                    Subscription = _isSubscription
                },
                new Immunization {
                    ImmunizationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-15),
                    Immunizations = "COVID-19",
                    CPTCode = "91300",
                    CVXCode = "208",
                    Comments = "First dose",
                    CPTDescription = "SARS-CoV-2 (COVID-19) vaccine",
                    GivenDate = DateTime.Now.AddDays(-20),
                    IsActive = true,
                    Subscription = _isSubscription
                }
            };

            _controller = new ImmunizationController(
                _immunizationCommandHandlerMock.Object,
                _immunizationQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetAllById_ReturnsImmunizations_WhenImmunizationsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientImmunizations = _testImmunizations.Select(i => { i.PatientId = patientId; return i; }).ToList();
            _immunizationQueryHandlerMock.Setup(x => x.GetAllImmunizationsById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientImmunizations);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedImmunizations = okResult.Value as IEnumerable<Immunization>;
            returnedImmunizations.Should().NotBeNull();
            returnedImmunizations.Should().BeEquivalentTo(patientImmunizations);
        }

        [Test]
        public async Task GetAllById_ReturnsNotFound_WhenNoImmunizationsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _immunizationQueryHandlerMock.Setup(x => x.GetAllImmunizationsById(patientId, _orgId, _isSubscription))
                .ReturnsAsync((IEnumerable<Immunization>)null);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task Registration_ValidImmunizations_ReturnsOk()
        {
            // Arrange
            var immunizations = _testImmunizations;

            // Act
            var result = await _controller.Registration(immunizations, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _immunizationCommandHandlerMock.Verify(x => x.AddImmunization(immunizations, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task Registration_EmptyList_ReturnsBadRequest()
        {
            // Arrange
            var emptyImmunizations = new List<Immunization>();

            // Act
            var result = await _controller.Registration(emptyImmunizations, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);
        }

        [Test]
        public async Task UpdateImmunizationById_ValidImmunization_ReturnsOk()
        {
            // Arrange
            var immunization = _testImmunizations[0];

            // Act
            var result = await _controller.UpdateImmunizationById(immunization.PatientId, immunization, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _immunizationCommandHandlerMock.Verify(x => x.UpdateImmunization(immunization, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task DeleteByEntity_ValidImmunization_ReturnsOk()
        {
            // Arrange
            var immunization = _testImmunizations[0];

            // Act
            var result = await _controller.DeleteByEntity(immunization, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _immunizationCommandHandlerMock.Verify(x => x.DeleteImmunizationByEntity(immunization, _orgId, _isSubscription), Times.Once);
        }
    }
}
