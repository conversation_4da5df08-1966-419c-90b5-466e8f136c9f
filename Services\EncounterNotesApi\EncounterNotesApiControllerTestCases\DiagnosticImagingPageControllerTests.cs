using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;
using EncounterNotesDataAccessLayer.EncounterNotesContext;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class DiagnosticImagingPageControllerTests
    {
        private Mock<IDiagnosticImagingPageCommandHandler<DiagnosticImagingDTO>> _diagnosticImagingCommandHandlerMock;
        private Mock<IDiagnosticImagingPageQueryHandler<DiagnosticImagingDTO>> _diagnosticImagingQueryHandlerMock;
        private Mock<ILogger<DiagnosticImagingPageController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;

        private DiagnosticImagingPageController _controller;
        private List<DiagnosticImagingDTO> _testDiagnosticImagings;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _diagnosticImagingCommandHandlerMock = new Mock<IDiagnosticImagingPageCommandHandler<DiagnosticImagingDTO>>();
            _diagnosticImagingQueryHandlerMock = new Mock<IDiagnosticImagingPageQueryHandler<DiagnosticImagingDTO>>();
            _loggerMock = new Mock<ILogger<DiagnosticImagingPageController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();

            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["SuccessfulDeletion"]).Returns(new LocalizedString("SuccessfulDeletion", "Deletion successful."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting data."));
            _localizerMock.Setup(x => x["InvalidAddressData"]).Returns(new LocalizedString("InvalidAddressData", "Invalid address data."));
            _localizerMock.Setup(x => x["AddressInvalidMessage"]).Returns(new LocalizedString("AddressInvalidMessage", "Address data is invalid."));
            _localizerMock.Setup(x => x["AddingNewAddress"]).Returns(new LocalizedString("AddingNewAddress", "Adding new address."));
            _localizerMock.Setup(x => x["AddressAddedSuccessfully"]).Returns(new LocalizedString("AddressAddedSuccessfully", "Address added successfully."));
            _localizerMock.Setup(x => x["AddSuccessful"]).Returns(new LocalizedString("AddSuccessful", "Add successful."));
            _localizerMock.Setup(x => x["ErrorAddingAddress"]).Returns(new LocalizedString("ErrorAddingAddress", "Error adding address."));
            _localizerMock.Setup(x => x["InternalServerErrorMessage"]).Returns(new LocalizedString("InternalServerErrorMessage", "Internal server error."));
            _localizerMock.Setup(x => x["AccessError"]).Returns(new LocalizedString("AccessError", "Access error."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));

            // Create test data
            _testDiagnosticImagings = new List<DiagnosticImagingDTO>
            {
                new DiagnosticImagingDTO
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationID = _orgId,
                    Status = "Completed",
                    Provider = "Dr. Smith",
                    Facility = "Main Hospital",
                    AssignedTo = "Radiology Dept",
                    FutureOrder = false,
                    HigherPriority = true,
                    InHouse = true,
                    Procedgure = "X-Ray",
                    OrderDate = DateTime.Now.AddDays(-5),
                    Reason = "Chest pain",
                    Recieved = true,
                    Date = DateTime.Now.AddDays(-3),
                    Result = "Normal findings",
                    Notes = "Patient reported improvement after test",
                    ClassicalInfo = "No abnormalities detected",
                    InternalNotes = "Follow up in 6 months",
                    IsActive = true,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-5),
                    UpdatedDate = DateTime.Now.AddDays(-3),
                    Assessments = new List<DiagnosticImagingAssessment>(),
                    Subscription = _isSubscription
                },
                new DiagnosticImagingDTO
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationID = _orgId,
                    Status = "Pending",
                    Provider = "Dr. Johnson",
                    Facility = "Imaging Center",
                    AssignedTo = "MRI Dept",
                    FutureOrder = true,
                    HigherPriority = false,
                    InHouse = false,
                    Procedgure = "MRI",
                    OrderDate = DateTime.Now.AddDays(-2),
                    Reason = "Knee pain",
                    Recieved = false,
                    Date = DateTime.Now.AddDays(3),
                    Result = "",
                    Notes = "Patient has metal implant",
                    ClassicalInfo = "Previous injury 5 years ago",
                    InternalNotes = "Check for contraindications",
                    IsActive = true,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-2),
                    UpdatedDate = DateTime.Now.AddDays(-2),
                    Assessments = new List<DiagnosticImagingAssessment>(),
                    Subscription = _isSubscription
                }
            };

            _controller = new DiagnosticImagingPageController(
                _diagnosticImagingQueryHandlerMock.Object,
                _diagnosticImagingCommandHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object,
                null // Pass null for the context parameter
            );
        }

        [Test]
        public async Task GetById_ReturnsOk_WhenDiagnosticImagingsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var diagnosticImagings = _testDiagnosticImagings.ToList();
            _diagnosticImagingQueryHandlerMock.Setup(x => x.GetDiagnosticImagingById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(diagnosticImagings);

            // Act
            var result = await _controller.GetById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().BeEquivalentTo(diagnosticImagings);
        }

        [Test]
        public async Task GetById_ReturnsNotFound_WhenNoDiagnosticImagingsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _diagnosticImagingQueryHandlerMock.Setup(x => x.GetDiagnosticImagingById(patientId, _orgId, _isSubscription))
                .ReturnsAsync((List<DiagnosticImagingDTO>)null);

            // Act
            var result = await _controller.GetById(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task UpdateById_ReturnsOk_WhenUpdateSuccessful()
        {
            // Arrange
            var diagnosticImaging = _testDiagnosticImagings[0];
            var id = diagnosticImaging.RecordID;

            // Act
            var result = await _controller.UpdateById(id, diagnosticImaging, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _diagnosticImagingCommandHandlerMock.Verify(x => x.UpdateDiagnosticImaging(diagnosticImaging, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateById_ReturnsBadRequest_WhenIdMismatch()
        {
            // Arrange
            var diagnosticImaging = _testDiagnosticImagings[0];
            var wrongId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdateById(wrongId, diagnosticImaging, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["InvalidRecord"]);

            // Verify the command handler was not called
            _diagnosticImagingCommandHandlerMock.Verify(x => x.UpdateDiagnosticImaging(It.IsAny<DiagnosticImagingDTO>(), _orgId, _isSubscription), Times.Never);
        }

        [Test]
        public async Task DeleteById_ReturnsOk_WhenDeleteSuccessful()
        {
            // Arrange
            var id = Guid.NewGuid();

            // Act
            var result = await _controller.DeleteById(id, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulDeletion"]);

            // Verify the command handler was called
            _diagnosticImagingCommandHandlerMock.Verify(x => x.DeleteDiagnosticImagingById(id, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task AddDiagnosticImaging_ReturnsOk_WhenAddSuccessful()
        {
            // Arrange
            var diagnosticImagings = _testDiagnosticImagings;

            // Act
            var result = await _controller.AddDiagnosticImaging(diagnosticImagings, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["AddSuccessful"]);

            // Verify the command handler was called
            _diagnosticImagingCommandHandlerMock.Verify(x => x.AddDiagnosticImaging(diagnosticImagings, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task AddDiagnosticImaging_ReturnsBadRequest_WhenEmptyList()
        {
            // Arrange
            var emptyList = new List<DiagnosticImagingDTO>();

            // Act
            var result = await _controller.AddDiagnosticImaging(emptyList, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["AddressInvalidMessage"]);

            // Verify the command handler was not called
            _diagnosticImagingCommandHandlerMock.Verify(x => x.AddDiagnosticImaging(It.IsAny<List<DiagnosticImagingDTO>>(), _orgId, _isSubscription), Times.Never);
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsOk_WhenActiveDiagnosticImagingsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activeDiagnosticImagings = _testDiagnosticImagings.Where(d => d.IsActive).ToList();
            _diagnosticImagingQueryHandlerMock.Setup(x => x.GetDiagnosticImagingByIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activeDiagnosticImagings);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().BeEquivalentTo(activeDiagnosticImagings);
        }

        [Test]
        public async Task UpdateAccess_ReturnsOk_WhenUpdateSuccessful()
        {
            // Arrange
            var diagnosticImagings = _testDiagnosticImagings;
            _diagnosticImagingCommandHandlerMock.Setup(x => x.UpdateDiagnosticImagingList(diagnosticImagings, _orgId, _isSubscription))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.UpdateAccess(diagnosticImagings, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            // The value is an anonymous type, so we can't check its exact type

            // Verify the command handler was called
            _diagnosticImagingCommandHandlerMock.Verify(x => x.UpdateDiagnosticImagingList(diagnosticImagings, _orgId, _isSubscription), Times.Once);
        }


    }
}
