using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class CPTControllerTests
    {
        private Mock<ICPTQueryHandler<CPT>> _cptQueryHandlerMock;
        private Mock<ILogger<CPTController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private RecordDatabaseContext _contextMock;
        private CPTController _controller;
        private List<CPT> _testCPTs;

        [SetUp]
        public void Setup()
        {
            _cptQueryHandlerMock = new Mock<ICPTQueryHandler<CPT>>();
            _loggerMock = new Mock<ILogger<CPTController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();

            // Since the controller doesn't actually use the context, we can pass null
            _contextMock = null;

            // Mock localizer strings
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error fetching CPT codes."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testCPTs = new List<CPT>
            {
                new CPT { CPTCode = "99201", Description = "Office or other outpatient visit for the evaluation and management of a new patient" },
                new CPT { CPTCode = "99202", Description = "Office or other outpatient visit for the evaluation and management of a new patient, which requires a medically appropriate history and/or examination" },
                new CPT { CPTCode = "99203", Description = "Office or other outpatient visit for the evaluation and management of a new patient, which requires a medically appropriate history and/or examination and low level of medical decision making" }
            };

            _controller = new CPTController(
                _cptQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object,
                _contextMock
            );
        }

        [Test]
        public async Task Get_ReturnsListOfCPTCodes()
        {
            // Arrange
            _cptQueryHandlerMock.Setup(x => x.GetCPT()).ReturnsAsync(_testCPTs);

            // Act
            var result = await _controller.Get();

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var cptCodes = okResult.Value as IEnumerable<CPT>;
            cptCodes.Should().NotBeNull();
            cptCodes.Should().BeEquivalentTo(_testCPTs);
        }

        [Test]
        public async Task Get_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            _cptQueryHandlerMock.Setup(x => x.GetCPT()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Get();

            // Assert
            var statusCodeResult = result.Result as ObjectResult;
            statusCodeResult.Should().NotBeNull();
            statusCodeResult.StatusCode.Should().Be(500);
            statusCodeResult.Value.Should().Be(_localizerMock.Object["GetLogError"]);
        }
    }
}
