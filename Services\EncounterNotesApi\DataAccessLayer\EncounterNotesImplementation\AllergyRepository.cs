﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Text;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interfaces.ShardManagement;

using Microsoft.Extensions.Logging;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class AllergyRepository : ShardGenericRepository<Allergy>, IAllergyRepository
    {
        private readonly ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> _shardMapManagerService;
        private readonly string _shardMapName;
        public AllergyRepository(RecordDatabaseContext context, ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> shardMapManagerService,
                                 IStringLocalizer<EncounterDataAccessLayer> localizer, ILogger<RecordDatabaseContext> logger)
            : base(context, shardMapManagerService, localizer, logger)
        {
            _shardMapManagerService = shardMapManagerService;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }

        // Add allergy with sharding
        public async Task AddAsync(Allergy allergy, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            await context.Allergies.AddAsync(allergy);
            await context.SaveChangesAsync();
        }

        // Get allergy by PatientId and MedicineId with sharding
        public async Task<Allergy> GetByIdAsync(Guid patientId, Guid medicineId, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return null;
            return await context.Allergies
                .FirstOrDefaultAsync(a => a.PatientId == patientId && a.MedicineId == medicineId);
        }

        // Update allergy with sharding
        public async Task UpdateAsync(Allergy allergy, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            context.Allergies.Update(allergy);
            await context.SaveChangesAsync();
        }

        // Get all allergies with sharding
        public async Task<IEnumerable<Allergy>> GetAllAllergyAsync(Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return Enumerable.Empty<Allergy>();
            return await context.Allergies
                .AsNoTracking()
                .ToListAsync();
        }

        // Update range of allergies with sharding
        public async Task UpdateRangeAsync(List<Allergy> allergies, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            context.Allergies.UpdateRange(allergies);
            await context.SaveChangesAsync();
        }
    }
}
