using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class BillingControllerTests
    {
        private Mock<IBillingQueryHandler<Billing>> _billingQueryHandlerMock;
        private Mock<IBillingCommandHandler<Billing>> _billingCommandHandlerMock;
        private Mock<ILogger<BillingController>> _loggerMock;
        private Mock<IStringLocalizer<BillingController>> _localizerMock;
        private BillingController _controller;
        private List<Billing> _testBillings;
        private Billing _testBilling;

        [SetUp]
        public void Setup()
        {
            _billingQueryHandlerMock = new Mock<IBillingQueryHandler<Billing>>();
            _billingCommandHandlerMock = new Mock<IBillingCommandHandler<Billing>>();
            _loggerMock = new Mock<ILogger<BillingController>>();
            _localizerMock = new Mock<IStringLocalizer<BillingController>>();

            // Mock localizer strings
            _localizerMock.Setup(x => x["FetchingBillingWithID"]).Returns(new LocalizedString("FetchingBillingWithID", "Fetching billing with ID: {0}"));
            _localizerMock.Setup(x => x["BillingNotFound"]).Returns(new LocalizedString("BillingNotFound", "Billing not found with ID: {0}"));
            _localizerMock.Setup(x => x["BillingNotFoundMessage"]).Returns(new LocalizedString("BillingNotFoundMessage", "Billing not found"));
            _localizerMock.Setup(x => x["BillingFetchedSuccessfully"]).Returns(new LocalizedString("BillingFetchedSuccessfully", "Billing fetched successfully with ID: {0}"));
            _localizerMock.Setup(x => x["ErrorFetchingBilling"]).Returns(new LocalizedString("ErrorFetchingBilling", "Error fetching billing with ID: {0}"));
            _localizerMock.Setup(x => x["InternalServerErrorMessage"]).Returns(new LocalizedString("InternalServerErrorMessage", "An internal server error occurred"));
            _localizerMock.Setup(x => x["FetchingBillingWithPatientID"]).Returns(new LocalizedString("FetchingBillingWithPatientID", "Fetching billing with patient ID: {0}"));
            _localizerMock.Setup(x => x["InvalidBillingData"]).Returns(new LocalizedString("InvalidBillingData", "Invalid billing data"));
            _localizerMock.Setup(x => x["BillingInvalidMessage"]).Returns(new LocalizedString("BillingInvalidMessage", "Billing data is invalid"));
            _localizerMock.Setup(x => x["AddingNewBilling"]).Returns(new LocalizedString("AddingNewBilling", "Adding new billing"));
            _localizerMock.Setup(x => x["BillingAddedSuccessfully"]).Returns(new LocalizedString("BillingAddedSuccessfully", "Billing added successfully with ID: {0}"));
            _localizerMock.Setup(x => x["ErrorAddingBilling"]).Returns(new LocalizedString("ErrorAddingBilling", "Error adding billing"));
            _localizerMock.Setup(x => x["InvalidDataForUpdate"]).Returns(new LocalizedString("InvalidDataForUpdate", "Invalid data for update with ID: {0}"));
            _localizerMock.Setup(x => x["UpdatingBillingWithID"]).Returns(new LocalizedString("UpdatingBillingWithID", "Updating billing with ID: {0}"));
            _localizerMock.Setup(x => x["BillingUpdatedSuccessfully"]).Returns(new LocalizedString("BillingUpdatedSuccessfully", "Billing updated successfully with ID: {0}"));
            _localizerMock.Setup(x => x["BillingNotFoundForUpdate"]).Returns(new LocalizedString("BillingNotFoundForUpdate", "Billing not found for update with ID: {0}"));
            _localizerMock.Setup(x => x["ErrorUpdatingBilling"]).Returns(new LocalizedString("ErrorUpdatingBilling", "Error updating billing"));
            _localizerMock.Setup(x => x["DeletingBillingWithID"]).Returns(new LocalizedString("DeletingBillingWithID", "Deleting billing with ID: {0}"));
            _localizerMock.Setup(x => x["BillingDeletedSuccessfully"]).Returns(new LocalizedString("BillingDeletedSuccessfully", "Billing deleted successfully with ID: {0}"));
            _localizerMock.Setup(x => x["BillingNotFoundForDeletion"]).Returns(new LocalizedString("BillingNotFoundForDeletion", "Billing not found for deletion with ID: {0}"));
            _localizerMock.Setup(x => x["ErrorDeletingBilling"]).Returns(new LocalizedString("ErrorDeletingBilling", "Error deleting billing"));
            _localizerMock.Setup(x => x["FetchingAllBillings"]).Returns(new LocalizedString("FetchingAllBillings", "Fetching all billings"));
            _localizerMock.Setup(x => x["NoBillingsFound"]).Returns(new LocalizedString("NoBillingsFound", "No billings found"));
            _localizerMock.Setup(x => x["BillingsNotFoundMessage"]).Returns(new LocalizedString("BillingsNotFoundMessage", "No billings found"));
            _localizerMock.Setup(x => x["AllBillingsFetchedSuccessfully"]).Returns(new LocalizedString("AllBillingsFetchedSuccessfully", "All billings fetched successfully"));
            _localizerMock.Setup(x => x["ErrorFetchingBillings"]).Returns(new LocalizedString("ErrorFetchingBillings", "Error fetching billings"));

            _testBilling = new Billing
            {
                BillingId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                VisitTypeId = Guid.NewGuid(),
                AssessmentId = Guid.NewGuid(),
                ProcedureCodeId = Guid.NewGuid(),
                EMCodeId = Guid.NewGuid(),
                BillingNotes = "Test billing notes",
                Amount = 100.50m,
                CreatedDate = DateTime.UtcNow
            };

            _testBillings = new List<Billing>
            {
                _testBilling,
                new Billing
                {
                    BillingId = Guid.NewGuid(),
                    PatientId = _testBilling.PatientId, // Same patient for testing GetBillingByPatientId
                    VisitTypeId = Guid.NewGuid(),
                    AssessmentId = Guid.NewGuid(),
                    ProcedureCodeId = Guid.NewGuid(),
                    EMCodeId = Guid.NewGuid(),
                    BillingNotes = "Another test billing",
                    Amount = 75.25m,
                    CreatedDate = DateTime.UtcNow.AddDays(-1)
                }
            };

            _controller = new BillingController(
                _billingQueryHandlerMock.Object,
                _billingCommandHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetBillingById_ExistingId_ReturnsBilling()
        {
            // Arrange
            var billingId = _testBilling.BillingId;
            _billingQueryHandlerMock.Setup(x => x.GetBillingByIdAsync(billingId))
                .ReturnsAsync(_testBilling);

            // Act
            var result = await _controller.GetBillingById(billingId);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            
            var billing = okResult.Value as Billing;
            billing.Should().NotBeNull();
            billing.Should().BeEquivalentTo(_testBilling);
        }

        [Test]
        public async Task GetBillingById_NonExistingId_ReturnsNotFound()
        {
            // Arrange
            var nonExistingId = Guid.NewGuid();
            _billingQueryHandlerMock.Setup(x => x.GetBillingByIdAsync(nonExistingId))
                .ReturnsAsync((Billing)null);

            // Act
            var result = await _controller.GetBillingById(nonExistingId);

            // Assert
            var notFoundResult = result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
        }

        [Test]
        public async Task GetBillingByPatientId_ExistingPatientId_ReturnsBillings()
        {
            // Arrange
            var patientId = _testBilling.PatientId;
            var patientBillings = _testBillings.FindAll(b => b.PatientId == patientId);
            _billingQueryHandlerMock.Setup(x => x.GetBillingByPatientIdAsync(patientId))
                .ReturnsAsync(patientBillings);

            // Act
            var result = await _controller.GetBillingByPatientId(patientId);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            
            var billings = okResult.Value as List<Billing>;
            billings.Should().NotBeNull();
            billings.Should().BeEquivalentTo(patientBillings);
        }

        [Test]
        public async Task GetBillingByPatientId_NonExistingPatientId_ReturnsNotFound()
        {
            // Arrange
            var nonExistingPatientId = Guid.NewGuid();
            _billingQueryHandlerMock.Setup(x => x.GetBillingByPatientIdAsync(nonExistingPatientId))
                .ReturnsAsync(new List<Billing>());

            // Act
            var result = await _controller.GetBillingByPatientId(nonExistingPatientId);

            // Assert
            var notFoundResult = result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
        }

        [Test]
        public async Task AddBilling_ValidBilling_ReturnsCreatedAtAction()
        {
            // Arrange
            var newBilling = new Billing
            {
                BillingId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                VisitTypeId = Guid.NewGuid(),
                AssessmentId = Guid.NewGuid(),
                ProcedureCodeId = Guid.NewGuid(),
                EMCodeId = Guid.NewGuid(),
                BillingNotes = "New billing",
                Amount = 150.75m
            };

            // Act
            var result = await _controller.AddBilling(newBilling);

            // Assert
            var createdAtActionResult = result as CreatedAtActionResult;
            createdAtActionResult.Should().NotBeNull();
            createdAtActionResult.StatusCode.Should().Be(201);
            createdAtActionResult.ActionName.Should().Be(nameof(BillingController.GetBillingById));
            createdAtActionResult.RouteValues["id"].Should().Be(newBilling.BillingId);
            
            var returnedBilling = createdAtActionResult.Value as Billing;
            returnedBilling.Should().NotBeNull();
            returnedBilling.Should().BeEquivalentTo(newBilling);
            
            // Verify the command handler was called
            _billingCommandHandlerMock.Verify(x => x.AddBillingAsync(newBilling), Times.Once);
        }

        [Test]
        public async Task AddBilling_NullBilling_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddBilling(null);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            
            // Verify the command handler was not called
            _billingCommandHandlerMock.Verify(x => x.AddBillingAsync(It.IsAny<Billing>()), Times.Never);
        }

        [Test]
        public async Task UpdateBilling_ValidBilling_ReturnsNoContent()
        {
            // Arrange
            var billingToUpdate = _testBilling;

            // Act
            var result = await _controller.UpdateBilling(billingToUpdate.BillingId, billingToUpdate);

            // Assert
            var noContentResult = result as NoContentResult;
            noContentResult.Should().NotBeNull();
            noContentResult.StatusCode.Should().Be(204);
            
            // Verify the command handler was called
            _billingCommandHandlerMock.Verify(x => x.UpdateBillingAsync(billingToUpdate), Times.Once);
        }

        [Test]
        public async Task UpdateBilling_IdMismatch_ReturnsBadRequest()
        {
            // Arrange
            var billingToUpdate = _testBilling;
            var differentId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdateBilling(differentId, billingToUpdate);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            
            // Verify the command handler was not called
            _billingCommandHandlerMock.Verify(x => x.UpdateBillingAsync(It.IsAny<Billing>()), Times.Never);
        }

        [Test]
        public async Task DeleteBilling_ExistingId_ReturnsNoContent()
        {
            // Arrange
            var billingId = _testBilling.BillingId;

            // Act
            var result = await _controller.DeleteBilling(billingId);

            // Assert
            var noContentResult = result as NoContentResult;
            noContentResult.Should().NotBeNull();
            noContentResult.StatusCode.Should().Be(204);
            
            // Verify the command handler was called
            _billingCommandHandlerMock.Verify(x => x.DeleteBillingAsync(billingId), Times.Once);
        }

        [Test]
        public async Task GetAllBillings_BillingsExist_ReturnsAllBillings()
        {
            // Arrange
            _billingQueryHandlerMock.Setup(x => x.GetAllBillingsAsync())
                .ReturnsAsync(_testBillings);

            // Act
            var result = await _controller.GetAllBillings();

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            
            var billings = okResult.Value as List<Billing>;
            billings.Should().NotBeNull();
            billings.Should().BeEquivalentTo(_testBillings);
        }

        [Test]
        public async Task GetAllBillings_NoBillingsExist_ReturnsNotFound()
        {
            // Arrange
            _billingQueryHandlerMock.Setup(x => x.GetAllBillingsAsync())
                .ReturnsAsync(new List<Billing>());

            // Act
            var result = await _controller.GetAllBillings();

            // Assert
            var notFoundResult = result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
        }
    }
}
