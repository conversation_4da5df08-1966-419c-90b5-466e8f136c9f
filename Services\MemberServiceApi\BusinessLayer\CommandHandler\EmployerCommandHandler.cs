﻿using Contracts;
using MemberServiceBusinessLayer;
using MemberServiceDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class EmployerCommandHandler : IEmployerCommandHandler<Employer>
    {
        private readonly IUnitOfWork _unitOfWork;

        public EmployerCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddEmployerAsync(List<Employer> Employers, Guid orgId, bool Subscription)
        {
            await _unitOfWork.EmployerRepository.AddAsync(Employers, orgId, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateEmployerAsync(Employer Employer, Guid orgId, bool Subscription)
        {
            await _unitOfWork.EmployerRepository.UpdateAsync(Employer, orgId, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteEmployerAsync(Guid id, Guid orgId, bool Subscription)
        {
            await _unitOfWork.EmployerRepository.DeleteByIdAsync(id, orgId, Subscription);
            await _unitOfWork.SaveAsync();
        }
    }
}
