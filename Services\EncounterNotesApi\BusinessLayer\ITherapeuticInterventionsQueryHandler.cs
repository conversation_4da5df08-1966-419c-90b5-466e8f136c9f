﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface ITherapeuticInterventionsQueryHandler<TText>
    {
        Task<IEnumerable<TherapeuticInterventionsData>> GetTherapeuticInterventionsByIdAndIsActive(Guid id, Guid OrgId, bool Subscription);
        Task<IEnumerable<TherapeuticInterventionsData>> GetAllTherapeuticInterventionsById(Guid id, Guid OrgId, bool Subscription);
    }
}
