using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using EncounterNotesDataAccessLayer.EncounterNotesContext;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class TherapeuticInterventionsListControllerTests
    {
        private Mock<ITherapeuticInterventionsListQueryHandler<TherapeuticInterventionsList>> _therapeuticInterventionsListQueryHandlerMock;
        private Mock<ILogger<TherapeuticInterventionsListController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private RecordDatabaseContext _contextMock;
        private TherapeuticInterventionsListController _controller;
        private List<TherapeuticInterventionsList> _testTherapeuticInterventionsList;

        [SetUp]
        public void Setup()
        {
            _therapeuticInterventionsListQueryHandlerMock = new Mock<ITherapeuticInterventionsListQueryHandler<TherapeuticInterventionsList>>();
            _loggerMock = new Mock<ILogger<TherapeuticInterventionsListController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            // Since the controller doesn't actually use the context, we can pass null
            _contextMock = null;

            // Mock localizer strings
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error fetching therapeutic interventions list."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testTherapeuticInterventionsList = new List<TherapeuticInterventionsList>
            {
                new TherapeuticInterventionsList { ID = Guid.NewGuid(), TherapyType = "Physical Therapy", Description = "Therapeutic exercise" },
                new TherapeuticInterventionsList { ID = Guid.NewGuid(), TherapyType = "Physical Therapy", Description = "Manual therapy techniques" },
                new TherapeuticInterventionsList { ID = Guid.NewGuid(), TherapyType = "Occupational Therapy", Description = "Activities of daily living training" },
                new TherapeuticInterventionsList { ID = Guid.NewGuid(), TherapyType = "Speech Therapy", Description = "Speech and language exercises" }
            };

            _controller = new TherapeuticInterventionsListController(
                _therapeuticInterventionsListQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object,
                _contextMock
            );
        }

        [Test]
        public async Task Get_ReturnsListOfTherapeuticInterventionsList()
        {
            // Arrange
            _therapeuticInterventionsListQueryHandlerMock.Setup(x => x.GetTherapeuticInterventionsList()).ReturnsAsync(_testTherapeuticInterventionsList);

            // Act
            var result = await _controller.Get();

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var interventionsList = okResult.Value as IEnumerable<TherapeuticInterventionsList>;
            interventionsList.Should().NotBeNull();
            interventionsList.Should().BeEquivalentTo(_testTherapeuticInterventionsList);
        }

        [Test]
        public async Task Get_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            _therapeuticInterventionsListQueryHandlerMock.Setup(x => x.GetTherapeuticInterventionsList()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Get();

            // Assert
            var statusCodeResult = result.Result as ObjectResult;
            statusCodeResult.Should().NotBeNull();
            statusCodeResult.StatusCode.Should().Be(500);
            statusCodeResult.Value.Should().Be(_localizerMock.Object["GetLogError"]);
        }
    }
}
