using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class PastResultsCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<PastResultsCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, PastResults, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IPastResultsRepository> _pastResultsRepositoryMock;
        private PastResultsCommandHandler _pastResultsCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<PastResultsCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, PastResults, EncounterDataAccessLayer>>();
            _pastResultsRepositoryMock = new Mock<IPastResultsRepository>();

            _unitOfWorkMock.Setup(u => u.PastResultsRepository).Returns(_pastResultsRepositoryMock.Object);

            _pastResultsCommandHandler = new PastResultsCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task AddResult_ValidResults_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var results = new List<PastResults>
            {
                new PastResults
                {
                    ResultId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now,
                    OrderName = "Complete Blood Count",
                    OrderDate = DateTime.Now.AddDays(-2),
                    ResultDate = DateTime.Now.AddDays(-1),
                    OrderType = "Laboratory",
                    OrderBy = "Dr. Smith",
                    ViewResults = "WBC: 7.5, RBC: 4.8, Hgb: 14.2, Hct: 42.1, Platelets: 250",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            // Act
            await _pastResultsCommandHandler.AddResult(results, _orgId, _subscription);

            // Assert
            _pastResultsRepositoryMock.Verify(r => r.AddAsync(results, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateResult_ValidResult_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var result = new PastResults
            {
                ResultId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-30),
                UpdatedDate = DateTime.Now,
                OrderName = "Updated Order Name",
                OrderDate = DateTime.Now.AddDays(-5),
                ResultDate = DateTime.Now.AddDays(-3),
                OrderType = "Updated Order Type",
                OrderBy = "Updated Order By",
                ViewResults = "Updated Results",
                IsActive = true,
                Subscription = _subscription
            };

            // Act
            await _pastResultsCommandHandler.UpdateResult(result, _orgId, _subscription);

            // Assert
            _pastResultsRepositoryMock.Verify(r => r.UpdateAsync(result, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteResultById_ValidId_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var id = Guid.NewGuid();

            // Act
            await _pastResultsCommandHandler.DeleteResultById(id, _orgId, _subscription);

            // Assert
            _pastResultsRepositoryMock.Verify(r => r.DeleteByIdAsync(id, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteResultsByEntity_ValidEntity_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var result = new PastResults
            {
                ResultId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                IsActive = false,
                Subscription = _subscription
            };

            // Act
            await _pastResultsCommandHandler.DeleteResultsByEntity(result, _orgId, _subscription);

            // Assert
            _pastResultsRepositoryMock.Verify(r => r.DeleteByEntityAsync(result, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdatePastList_ValidResults_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var results = new List<PastResults>
            {
                new PastResults
                {
                    ResultId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    UpdatedDate = DateTime.Now,
                    OrderName = "Updated Order Name 1",
                    IsActive = true,
                    Subscription = _subscription
                },
                new PastResults
                {
                    ResultId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    UpdatedDate = DateTime.Now,
                    OrderName = "Updated Order Name 2",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            // Act
            await _pastResultsCommandHandler.UpdatePastList(results, _orgId, _subscription);

            // Assert
            _pastResultsRepositoryMock.Verify(r => r.UpdateRangeAsync(results, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}
