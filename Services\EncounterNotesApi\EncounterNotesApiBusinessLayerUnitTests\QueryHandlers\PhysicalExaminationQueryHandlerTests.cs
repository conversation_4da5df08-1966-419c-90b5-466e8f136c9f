using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class PhysicalExaminationQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IPhysicalExaminationRepository> _physicalExaminationRepositoryMock;
        private PhysicalExaminationQueryHandler _physicalExaminationQueryHandler;
        private Guid _orgId;
        private bool _subscription;
        private Guid _patientId;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _physicalExaminationRepositoryMock = new Mock<IPhysicalExaminationRepository>();

            _unitOfWorkMock.Setup(u => u.PhysicalExaminationRepository).Returns(_physicalExaminationRepositoryMock.Object);
            _physicalExaminationQueryHandler = new PhysicalExaminationQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);

            _orgId = Guid.NewGuid();
            _subscription = false;
            _patientId = Guid.NewGuid();
        }

        [Test]
        public async Task GetAllExaminationsById_ShouldReturnAllExaminationsForPatient()
        {
            // Arrange
            var examinations = new List<PhysicalExamination>
            {
                new PhysicalExamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Skin = "Normal",
                    Rash = "None",
                    Tester = "Dr. Smith",
                    Moles = "Few on back",
                    Hemangioma = "None",
                    VascularMalformation = "None",
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    Subscription = _subscription
                },
                new PhysicalExamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdatedDate = DateTime.Now.AddDays(-55),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Skin = "Dry",
                    Rash = "Small on arm",
                    Tester = "Dr. Johnson",
                    Moles = "None",
                    Hemangioma = "Small on chest",
                    VascularMalformation = "None",
                    PCPId = Guid.NewGuid(),
                    IsActive = false,
                    Subscription = _subscription
                },
                new PhysicalExamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-45),
                    UpdatedDate = DateTime.Now.AddDays(-40),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Skin = "Normal",
                    Rash = "None",
                    Tester = "Dr. Wilson",
                    Moles = "None",
                    Hemangioma = "None",
                    VascularMalformation = "None",
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _physicalExaminationRepositoryMock.Setup(r => r.GetAllExaminationsAsync(_orgId, _subscription))
                .ReturnsAsync(examinations);

            // Act
            var result = await _physicalExaminationQueryHandler.GetAllExaminationsById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(e => e.PatientId == _patientId).Should().BeTrue();
        }

        [Test]
        public async Task GetAllExaminationsById_WithNoExaminations_ShouldReturnEmptyCollection()
        {
            // Arrange
            var examinations = new List<PhysicalExamination>();

            _physicalExaminationRepositoryMock.Setup(r => r.GetAllExaminationsAsync(_orgId, _subscription))
                .ReturnsAsync(examinations);

            // Act
            var result = await _physicalExaminationQueryHandler.GetAllExaminationsById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetExaminationByIdAndIsActive_ShouldReturnOnlyActiveExaminationsForPatient()
        {
            // Arrange
            var examinations = new List<PhysicalExamination>
            {
                new PhysicalExamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Skin = "Normal",
                    Rash = "None",
                    Tester = "Dr. Smith",
                    Moles = "Few on back",
                    Hemangioma = "None",
                    VascularMalformation = "None",
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    Subscription = _subscription
                },
                new PhysicalExamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdatedDate = DateTime.Now.AddDays(-55),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Skin = "Dry",
                    Rash = "Small on arm",
                    Tester = "Dr. Johnson",
                    Moles = "None",
                    Hemangioma = "Small on chest",
                    VascularMalformation = "None",
                    PCPId = Guid.NewGuid(),
                    IsActive = false, // Not active
                    Subscription = _subscription
                },
                new PhysicalExamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-45),
                    UpdatedDate = DateTime.Now.AddDays(-40),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Skin = "Normal",
                    Rash = "None",
                    Tester = "Dr. Wilson",
                    Moles = "None",
                    Hemangioma = "None",
                    VascularMalformation = "None",
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _physicalExaminationRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(examinations);

            // Act
            var result = await _physicalExaminationQueryHandler.GetExaminationByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.All(e => e.PatientId == _patientId && e.IsActive).Should().BeTrue();
        }

        [Test]
        public async Task GetExaminationByIdAndIsActive_WithNoActiveExaminations_ShouldReturnEmptyCollection()
        {
            // Arrange
            var examinations = new List<PhysicalExamination>
            {
                new PhysicalExamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    IsActive = false, // Not active
                    Subscription = _subscription
                },
                new PhysicalExamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _physicalExaminationRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(examinations);

            // Act
            var result = await _physicalExaminationQueryHandler.GetExaminationByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
