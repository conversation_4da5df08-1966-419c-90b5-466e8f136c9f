using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class ProcedureCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<ProcedureCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, Procedure, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IProcedureRepository> _procedureRepositoryMock;
        private ProcedureCommandHandler _procedureCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<ProcedureCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, Procedure, EncounterDataAccessLayer>>();
            _procedureRepositoryMock = new Mock<IProcedureRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;

            _unitOfWorkMock.Setup(u => u.ProcedureRepository).Returns(_procedureRepositoryMock.Object);
            _procedureCommandHandler = new ProcedureCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object);
        }

        [Test]
        public async Task AddProcedure_ShouldAddProceduresToRepository()
        {
            // Arrange
            var procedures = new List<Procedure>
            {
                new Procedure
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    CreatedByUserId = Guid.NewGuid(),
                    UpdatedByUserId = Guid.NewGuid(),
                    CPTCode = "99213",
                    Description = "Office or other outpatient visit",
                    Notes = "Routine follow-up",
                    OrderDate = DateTime.Now.AddDays(-30),
                    LastUpdatedDate = DateTime.Now.AddDays(-25),
                    OrderedBy = "Dr. Smith",
                    AssessmentData = "Hypertension, well-controlled",
                    AssessmentId = Guid.NewGuid(),
                    IsDeleted = true,
                    Subscription = _subscription
                },
                new Procedure
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    CreatedByUserId = Guid.NewGuid(),
                    UpdatedByUserId = Guid.NewGuid(),
                    CPTCode = "99214",
                    Description = "Office or other outpatient visit, moderate complexity",
                    Notes = "Evaluation of new symptoms",
                    OrderDate = DateTime.Now.AddDays(-60),
                    LastUpdatedDate = DateTime.Now.AddDays(-55),
                    OrderedBy = "Dr. Johnson",
                    AssessmentData = "Diabetes mellitus type 2",
                    AssessmentId = Guid.NewGuid(),
                    IsDeleted = true,
                    Subscription = _subscription
                }
            };

            _procedureRepositoryMock.Setup(r => r.AddAsync(It.IsAny<IEnumerable<Procedure>>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _procedureCommandHandler.AddProcedure(procedures, _orgId, _subscription);

            // Assert
            _procedureRepositoryMock.Verify(r => r.AddAsync(procedures, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateProcedure_ShouldUpdateProcedureInRepository()
        {
            // Arrange
            var procedure = new Procedure
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PcpId = Guid.NewGuid(),
                CreatedByUserId = Guid.NewGuid(),
                UpdatedByUserId = Guid.NewGuid(),
                CPTCode = "99213",
                Description = "Office or other outpatient visit",
                Notes = "Updated notes",
                OrderDate = DateTime.Now.AddDays(-30),
                LastUpdatedDate = DateTime.Now,
                OrderedBy = "Dr. Smith",
                AssessmentData = "Hypertension, well-controlled",
                AssessmentId = Guid.NewGuid(),
                IsDeleted = true,
                Subscription = _subscription
            };

            _procedureRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Procedure>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _procedureCommandHandler.UpdateProcedure(procedure, _orgId, _subscription);

            // Assert
            _procedureRepositoryMock.Verify(r => r.UpdateAsync(procedure, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateProcedureListAsync_ShouldUpdateProceduresInRepository()
        {
            // Arrange
            var procedures = new List<Procedure>
            {
                new Procedure
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    CPTCode = "99213",
                    Description = "Office or other outpatient visit",
                    IsDeleted = true,
                    Subscription = _subscription
                },
                new Procedure
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    CPTCode = "99214",
                    Description = "Office or other outpatient visit, moderate complexity",
                    IsDeleted = true,
                    Subscription = _subscription
                }
            };

            _procedureRepositoryMock.Setup(r => r.UpdateRangeAsync(It.IsAny<List<Procedure>>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _procedureCommandHandler.UpdateProcedureListAsync(procedures, _orgId, _subscription);

            // Assert
            _procedureRepositoryMock.Verify(r => r.UpdateRangeAsync(procedures, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}

