﻿using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class InsuranceCommandHandler : IInsuranceCommandHandler<Insurance>
    {
        private readonly IUnitOfWork _unitOfWork;

        public InsuranceCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddInsuranceAsync(List<Insurance> insurances, Guid OrgID, bool Subscription)
        {
            await _unitOfWork.InsuranceRepository.AddAsync(insurances, OrgID, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateInsuranceAsync(Insurance insurance, Guid OrgID, bool Subscription)
        {
            await _unitOfWork.InsuranceRepository.UpdateAsync(insurance, OrgID, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteInsuranceAsync(Guid id, Guid OrgID, bool Subscription)
        {
            await _unitOfWork.InsuranceRepository.DeleteByIdAsync(id, OrgID, Subscription);
            await _unitOfWork.SaveAsync();
        }
    }
}
