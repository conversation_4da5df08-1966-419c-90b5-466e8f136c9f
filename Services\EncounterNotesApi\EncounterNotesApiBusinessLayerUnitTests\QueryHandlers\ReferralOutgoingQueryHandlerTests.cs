using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class ReferralOutgoingQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IReferralOutgoingRepository> _referralOutgoingRepositoryMock;
        private ReferralOutgoingQueryHandler _referralOutgoingQueryHandler;
        private Guid _orgId;
        private bool _subscription;
        private Guid _patientId;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _referralOutgoingRepositoryMock = new Mock<IReferralOutgoingRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;
            _patientId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.ReferralOutgoingRepository).Returns(_referralOutgoingRepositoryMock.Object);
            _referralOutgoingQueryHandler = new ReferralOutgoingQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetAllPatientReferralOutgoingbyId_ShouldReturnAllReferralsForPatient()
        {
            // Arrange
            var referrals = new List<PatientReferralOutgoing>
            {
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    TreatmentPlan = "Physical Therapy",
                    ReferralFrom = "Dr. Smith",
                    Tests = "X-ray, MRI",
                    ReferralTo = "Physical Therapy Center",
                    ReferralReason = "Lower back pain",
                    isActive = true,
                    Subscription = _subscription
                },
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdatedDate = DateTime.Now.AddDays(-55),
                    TreatmentPlan = "Cardiology Consultation",
                    ReferralFrom = "Dr. Johnson",
                    Tests = "ECG, Stress Test",
                    ReferralTo = "Dr. Williams, Cardiology",
                    ReferralReason = "Chest pain and palpitations",
                    isActive = false,
                    Subscription = _subscription
                },
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-45),
                    UpdatedDate = DateTime.Now.AddDays(-40),
                    TreatmentPlan = "Dermatology Consultation",
                    ReferralFrom = "Dr. Brown",
                    Tests = "Skin Biopsy",
                    ReferralTo = "Dr. Davis, Dermatology",
                    ReferralReason = "Suspicious skin lesion",
                    isActive = true,
                    Subscription = _subscription
                }
            };

            _referralOutgoingRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(referrals);

            // Act
            var result = await _referralOutgoingQueryHandler.GetAllPatientReferralOutgoingbyId(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(r => r.PatientId == _patientId).Should().BeTrue();
        }

        [Test]
        public async Task GetAllPatientReferralOutgoingbyId_WithNoReferrals_ShouldReturnEmptyCollection()
        {
            // Arrange
            var referrals = new List<PatientReferralOutgoing>();

            _referralOutgoingRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(referrals);

            // Act
            var result = await _referralOutgoingQueryHandler.GetAllPatientReferralOutgoingbyId(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetPatientReferralOutgoingByIdAndIsActive_ShouldReturnActiveReferralsForPatient()
        {
            // Arrange
            var referrals = new List<PatientReferralOutgoing>
            {
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    TreatmentPlan = "Physical Therapy",
                    ReferralFrom = "Dr. Smith",
                    ReferralTo = "Physical Therapy Center",
                    ReferralReason = "Lower back pain",
                    isActive = true,
                    Subscription = _subscription
                },
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    TreatmentPlan = "Cardiology Consultation",
                    ReferralFrom = "Dr. Johnson",
                    ReferralTo = "Dr. Williams, Cardiology",
                    ReferralReason = "Chest pain and palpitations",
                    isActive = false, // Inactive
                    Subscription = _subscription
                },
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    TreatmentPlan = "Dermatology Consultation",
                    ReferralFrom = "Dr. Brown",
                    ReferralTo = "Dr. Davis, Dermatology",
                    ReferralReason = "Suspicious skin lesion",
                    isActive = true,
                    Subscription = _subscription
                }
            };

            _referralOutgoingRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(referrals);

            // Act
            var result = await _referralOutgoingQueryHandler.GetPatientReferralOutgoingByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.All(r => r.PatientId == _patientId && r.isActive).Should().BeTrue();
        }

        [Test]
        public async Task GetPatientReferralOutgoingByIdAndIsActive_WithNoActiveReferrals_ShouldReturnEmptyCollection()
        {
            // Arrange
            var referrals = new List<PatientReferralOutgoing>
            {
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    TreatmentPlan = "Cardiology Consultation",
                    ReferralFrom = "Dr. Johnson",
                    ReferralTo = "Dr. Williams, Cardiology",
                    ReferralReason = "Chest pain and palpitations",
                    isActive = false, // Inactive
                    Subscription = _subscription
                }
            };

            _referralOutgoingRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(referrals);

            // Act
            var result = await _referralOutgoingQueryHandler.GetPatientReferralOutgoingByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
