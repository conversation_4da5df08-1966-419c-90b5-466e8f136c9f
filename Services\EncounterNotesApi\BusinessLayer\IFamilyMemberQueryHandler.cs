﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IFamilyMemberQueryHandler<T>
    {
        Task<IEnumerable<FamilyMember>> GetFamilyMemberByIdAndIsActive(Guid id, Guid OrgId, bool Subscription);
        Task<List<T>> GetFamilyMembersById(Guid id, Guid OrgId, bool Subscription);

    }
}
