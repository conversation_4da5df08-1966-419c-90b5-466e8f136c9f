﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesContracts
{
    public class VisionRx : IContract
    {
        public Guid ExaminationId { get; set; }
        public Guid PatientId { get; set; }
        public Guid OrganizationId { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid UpdatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public bool IsActive { get; set; } 
        public bool Subscription { get; set; }

        //Presenting Prescription fields
        public string? PresentingSpectacleSphOD { get; set; } 
        public string? PresentingSpectacleSphOS { get; set; } 
        public string? PresentingSpectacleCylOD { get; set; } 
        public string? PresentingSpectacleCylOS { get; set; } 
        public string? PresentingSpectacleAxisOD { get; set; } 
        public string? PresentingSpectacleAxisOS { get; set; } 
        public string? PresentingSpectacleHPrismOD { get; set; } 
        public string? PresentingSpectacleHPrismOS { get; set; } 
        public string? PresentingSpectacleVPrismOD { get; set; } 
        public string? PresentingSpectacleVPrismOS { get; set; } 
        public string? PresentingSpectacleAddOD { get; set; } 
        public string? PresentingSpectacleAddOS { get; set; } 
        public string? PresentingSpectacleDvaOU { get; set; } 
        public string? PresentingSpectacleDvaOS { get; set; } 
        public string? PresentingSpectacleDvaOD { get; set; }
        public string? PresentingSpectacleNvaOD { get; set; } 
        public string? PresentingSpectacleNvaOU { get; set; } 
        public string? PresentingSpectacleNvaOS { get; set; } 
        public string? PresentingSpectaclePhOD { get; set; } 
        public string? PresentingSpectaclePhOU { get; set; }
        public string? PresentingSpectaclePHOS { get; set; }
        // Visual Acuity fields
        public decimal? UnaidedFlatKOD { get; set; } 
        public decimal? UnaidedFlatKOS { get; set; }
        public decimal? UnaidedSteepKOD { get; set; } 
        public decimal? UnaidedSteepKOS { get; set; }
        public string? UnaidedDvaOD { get; set; } 
        public string? UnaidedDvaOU { get; set; } 
        public string? UnaidedDvaOS { get; set; } 
        public string? UnaidedNvaOD { get; set; } 
        public string? UnaidedNvaOU { get; set; } 
        public string? UnaidedNvaOS { get; set; } 
        public string? UnaidedPhOD { get; set; } 
        public string? UnaidedPhOU { get; set; } 
        public string? UnaidedPhOS { get; set; } 
        
        public decimal? UnaidedVertexSpec { get; set; }
        public decimal? UnaidedSpecBC { get; set; }
        public decimal? UnaidedWorkDist { get; set; }
        public string? UnaidedSIUnit { get; set; }

        public string? ManifestSphOD { get; set; } 
        public string? ManifestSphOS { get; set; } 
        public string? ManifestCylOD { get; set; } 
        public string? ManifestCylOS { get; set; } 
        public string? ManifestAxisOD { get; set; } 
        public string? ManifestAxisOS { get; set; } 
        public string? ManifestHPrismOD { get; set; } 
        public string? ManifestHPrismOS { get; set; } 
        public string? ManifestVPrismOD { get; set; } 
        public string? ManifestVPrismOS { get; set; }
        public string? ManifestAddOD { get; set; } 
        public string? ManifestAddOS { get; set; } 
        public string? ManifestDvaOS { get; set; } 
        public string? ManifestDvaOD { get; set; }
        public string? ManifestNvaOD { get; set; } 
        public string? ManifestNvaOS { get; set; } 
        public string? ManifestPhOD { get; set; } 
        public string? ManifestPHOS { get; set; }

        //PD
        public decimal? PdDistOD { get; set; } 
        public decimal? PdNearOS { get; set; } 
        public decimal? PdDistOS { get; set; } 
        public decimal? PdNearOD { get; set; } 
        public decimal? PdDistOU { get; set; } 
        public decimal? PdNearOU { get; set; } 

        //Auto Refraction
        public string? RefractionSphOD { get; set; }
        public string? RefractionSphOS { get; set; }
        public string? RefractionCylOD { get; set; }
        public string? RefractionCylOS { get; set; }
        public string? RefractionAxisOD { get; set; }
        public string? RefractionAxisOS { get; set; }
        public string? RefractionHPrismOD { get; set; }
        public string? RefractionHPrismOS { get; set; }
        public string? RefractionVPrismOD { get; set; }
        public string? RefractionVPrismOS { get; set; }
        public string? RefractionAddOD { get; set; }
        public string? RefractionAddOS { get; set; }
        public string? RefractionDvaOS { get; set; }
        public string? RefractionDvaOD { get; set; }
        public string? RefractionNvaOD { get; set; }
        public string? RefractionNvaOS { get; set; }
        public string? RefractionPhOD { get; set; }
        public string? RefractionPHOS { get; set; }

        // Cycloplegic
        public string? CycloplegicSphOD { get; set; } 
        public string? CycloplegicSphOS { get; set; } 
        public string? CycloplegicCylOD { get; set; }
        public string? CycloplegicCylOS { get; set; } 
        public string? CycloplegicAxisOD { get; set; } 
        public string? CycloplegicAxisOS { get; set; } 
        public string? CycloplegicHPrismOD { get; set; } 
        public string? CycloplegicHPrismOS { get; set; } 
        public string? CycloplegicVPrismOD { get; set; } 
        public string? CycloplegicVPrismOS { get; set; } 
        public string? CycloplegicAddOD { get; set; } 
        public string? CycloplegicAddOS { get; set; } 
        public string? CycloplegicDvaOS { get; set; } 
        public string? CycloplegicDvaOD { get; set; }
        public string? CycloplegicNvaOD { get; set; } 
        public string? CycloplegicNvaOS { get; set; } 
        public string? CycloplegicPhOD { get; set; } 
        public string? CycloplegicPHOS { get; set; }

        // Final Spectacle
        public string? FinalSpectacleSphOD { get; set; } 
        public string? FinalSpectacleSphOS { get; set; } 
        public string? FinalSpectacleCylOD { get; set; } 
        public string? FinalSpectacleCylOS { get; set; } 
        public string? FinalSpectacleAxisOD { get; set; } 
        public string? FinalSpectacleAxisOS { get; set; } 
        public string? FinalSpectacleHPrismOD { get; set; } 
        public string? FinalSpectacleHPrismOS { get; set; } 
        public string? FinalSpectacleVPrismOD { get; set; } 
        public string? FinalSpectacleVPrismOS { get; set; }
        public string? FinalSpectacleAddOD { get; set; } 
        public string? FinalSpectacleAddOS { get; set; } 
        public string? FinalSpectacleDvaOU { get; set; } 
        public string? FinalSpectacleDvaOS { get; set; }
        public string? FinalSpectacleDvaOD { get; set; }
        public string? FinalSpectacleNvaOD { get; set; } 
        public string? FinalSpectacleNvaOU { get; set; } 
        public string? FinalSpectacleNvaOS { get; set; } 
        public string? FinalSpectaclePhOD { get; set; } 
        public string? FinalSpectaclePhOU { get; set; } 
        public string? FinalSpectaclePHOS { get; set; }
    }
}
