using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class AllergiesControllerTests
    {
        private Mock<IAllergyCommandHandler<Allergy>> _allergyCommandHandlerMock;
        private Mock<IAllergyQueryHandler<Allergy>> _allergyQueryHandlerMock;
        private Mock<ILogger<AllergiesController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private AllergiesController _controller;
        private List<Allergy> _testAllergies;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _allergyCommandHandlerMock = new Mock<IAllergyCommandHandler<Allergy>>();
            _allergyQueryHandlerMock = new Mock<IAllergyQueryHandler<Allergy>>();
            _loggerMock = new Mock<ILogger<AllergiesController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _orgId = Guid.NewGuid();
            _subscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["GetLogErrorAllergy"]).Returns(new LocalizedString("GetLogErrorAllergy", "Error fetching allergies."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["AccessNotFound"]).Returns(new LocalizedString("AccessNotFound", "Access not found."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No license provided."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error."));
            _localizerMock.Setup(x => x["PostLogErrorAllergy"]).Returns(new LocalizedString("PostLogErrorAllergy", "Error posting allergy."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Delete successful."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting allergy."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating allergy."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));

            _testAllergies = new List<Allergy>
            {
                new Allergy {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    DrugName = "Peanuts",
                    Reaction = "Hives",
                    Type = "Food",
                    isActive = true,
                    Subscription = _subscription
                },
                new Allergy {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    DrugName = "Penicillin",
                    Reaction = "Rash",
                    Type = "Medication",
                    isActive = true,
                    Subscription = _subscription
                }
            };

            _controller = new AllergiesController(
                _allergyCommandHandlerMock.Object,
                _allergyQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object,
                _unitOfWorkMock.Object
            );
        }

        [Test]
        public async Task GetAllById_ReturnsAllergies_WhenAllergyExists()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var allergies = _testAllergies.Select(a => { a.PatientId = patientId; return a; }).ToList();
            _allergyQueryHandlerMock.Setup(x => x.GetAllAllergyById(patientId, _orgId, _subscription))
                .ReturnsAsync(allergies);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _subscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedAllergies = okResult.Value as IEnumerable<Allergy>;
            returnedAllergies.Should().NotBeNull();
            returnedAllergies.Should().BeEquivalentTo(allergies);
        }

        [Test]
        public async Task GetAllById_ReturnsNotFound_WhenNoAllergiesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _allergyQueryHandlerMock.Setup(x => x.GetAllAllergyById(patientId, _orgId, _subscription))
                .ReturnsAsync((IEnumerable<Allergy>)null);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _subscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsActiveAllergies_WhenAllergyExists()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var allergies = _testAllergies.Select(a => { a.PatientId = patientId; return a; }).ToList();
            _allergyQueryHandlerMock.Setup(x => x.GetAllergyByIdAndIsActive(patientId, _orgId, _subscription))
                .ReturnsAsync(allergies);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId, _orgId, _subscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedAllergies = okResult.Value as IEnumerable<Allergy>;
            returnedAllergies.Should().NotBeNull();
            returnedAllergies.Should().BeEquivalentTo(allergies);
        }

        [Test]
        public async Task Registration_ValidAllergies_ReturnsOk()
        {
            // Arrange
            var allergies = _testAllergies;

            // Act
            var result = await _controller.Registration(allergies, _orgId, _subscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _allergyCommandHandlerMock.Verify(x => x.AddAllergy(allergies, _orgId, _subscription), Times.Once);
        }

        [Test]
        public async Task Registration_EmptyAllergies_ReturnsBadRequest()
        {
            // Arrange
            var emptyAllergies = new List<Allergy>();

            // Act
            var result = await _controller.Registration(emptyAllergies, _orgId, _subscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);

            // Verify the command handler was not called
            _allergyCommandHandlerMock.Verify(x => x.AddAllergy(It.IsAny<List<Allergy>>(), _orgId, _subscription), Times.Never);
        }

        [Test]
        public async Task DeleteByEntity_ValidAllergy_ReturnsOk()
        {
            // Arrange
            var allergy = _testAllergies[0];

            // Act
            var result = await _controller.DeleteByEntity(allergy, _orgId, _subscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _allergyCommandHandlerMock.Verify(x => x.DeleteAllergyByEntity(allergy, _orgId, _subscription), Times.Once);
        }

        [Test]
        public async Task UpdateAllergyById_ValidAllergy_ReturnsOk()
        {
            // Arrange
            var allergy = _testAllergies[0];
            var patientId = allergy.PatientId;
            var medicineId = allergy.MedicineId;

            // Act
            var result = await _controller.UpdateAllergyById(patientId, medicineId, allergy, _orgId, _subscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _allergyCommandHandlerMock.Verify(x => x.UpdateAllergy(allergy, _orgId, _subscription), Times.Once);
        }

        [Test]
        public async Task UpdateAllergyList_ValidRequest_ReturnsOk()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var request = new UpdateAllergyListRequest
            {
                PatientId = patientId,
                NewAllergies = new List<Allergy> { _testAllergies[0] },
                UpdatedAllergies = new List<Allergy> { _testAllergies[1] },
                DeletedAllergies = new List<Allergy>()
            };

            var allergyRepositoryMock = new Mock<IAllergyRepository>();
            _unitOfWorkMock.Setup(u => u.AllergyRepository).Returns(allergyRepositoryMock.Object);

            // Setup for existing allergy
            allergyRepositoryMock.Setup(r => r.GetByIdAsync(patientId, _testAllergies[1].MedicineId, _orgId, _subscription))
                .ReturnsAsync(_testAllergies[1]);

            // Act
            var result = await _controller.UpdateAllergyList(request, _orgId, _subscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify repository methods were called
            allergyRepositoryMock.Verify(r => r.AddAsync(request.NewAllergies[0], _orgId, _subscription), Times.Once);
            allergyRepositoryMock.Verify(r => r.GetByIdAsync(patientId, _testAllergies[1].MedicineId, _orgId, _subscription), Times.Once);
            allergyRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<Allergy>(), _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}
