using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class ReferralOutgoingControllerTests
    {
        private Mock<IReferralOutgoingCommandHandler<PatientReferralOutgoing>> _referralOutgoingCommandHandlerMock;
        private Mock<IReferralOutgoingQueryHandler<PatientReferralOutgoing>> _referralOutgoingQueryHandlerMock;
        private Mock<ILogger<ReferralOutgoingController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private ReferralOutgoingController _controller;
        private List<PatientReferralOutgoing> _testReferrals;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _referralOutgoingCommandHandlerMock = new Mock<IReferralOutgoingCommandHandler<PatientReferralOutgoing>>();
            _referralOutgoingQueryHandlerMock = new Mock<IReferralOutgoingQueryHandler<PatientReferralOutgoing>>();
            _loggerMock = new Mock<ILogger<ReferralOutgoingController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No license available."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error occurred."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error posting data."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting data."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Delete successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            // Create test data
            _testReferrals = new List<PatientReferralOutgoing>
            {
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    TreatmentPlan = "Physical Therapy",
                    ReferralFrom = "Dr. Smith",
                    ReferralTo = "Physical Therapy Center",
                    ReferralReason = "Lower back pain",
                    isActive = true,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-10),
                    UpdatedDate = DateTime.Now.AddDays(-10),
                    Subscription = _isSubscription
                },
                new PatientReferralOutgoing
                {
                    PlanReferralId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    TreatmentPlan = "Cardiology Consultation",
                    ReferralFrom = "Dr. Johnson",
                    ReferralTo = "Dr. Williams, Cardiology",
                    ReferralReason = "Chest pain and palpitations",
                    isActive = true,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-5),
                    UpdatedDate = DateTime.Now.AddDays(-5),
                    Subscription = _isSubscription
                }
            };

            _controller = new ReferralOutgoingController(
                _referralOutgoingCommandHandlerMock.Object,
                _referralOutgoingQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetAllById_ReturnsReferrals_WhenReferralsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientReferrals = _testReferrals.Select(r => { r.PatientId = patientId; return r; }).ToList();
            _referralOutgoingQueryHandlerMock.Setup(x => x.GetAllPatientReferralOutgoingbyId(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientReferrals);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedReferrals = okResult.Value as IEnumerable<PatientReferralOutgoing>;
            returnedReferrals.Should().NotBeNull();
            returnedReferrals.Should().BeEquivalentTo(patientReferrals);
        }

        [Test]
        public async Task GetAllById_ReturnsNotFound_WhenNoReferralsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _referralOutgoingQueryHandlerMock.Setup(x => x.GetAllPatientReferralOutgoingbyId(patientId, _orgId, _isSubscription))
                .ReturnsAsync((IEnumerable<PatientReferralOutgoing>)null);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsReferrals_WhenActiveReferralsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activeReferrals = _testReferrals.Select(r => { r.PatientId = patientId; r.isActive = true; return r; }).ToList();
            _referralOutgoingQueryHandlerMock.Setup(x => x.GetPatientReferralOutgoingByIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activeReferrals);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedReferrals = okResult.Value as IEnumerable<PatientReferralOutgoing>;
            returnedReferrals.Should().NotBeNull();
            returnedReferrals.Should().BeEquivalentTo(activeReferrals);
        }

        [Test]
        public async Task Registration_ValidReferrals_ReturnsOk()
        {
            // Arrange
            var referrals = _testReferrals;

            // Act
            var result = await _controller.Registration(referrals, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _referralOutgoingCommandHandlerMock.Verify(x => x.AddReferralOutgoing(referrals, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task Registration_EmptyList_ReturnsBadRequest()
        {
            // Arrange
            var emptyReferrals = new List<PatientReferralOutgoing>();

            // Act
            var result = await _controller.Registration(emptyReferrals, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);
        }

        [Test]
        public async Task DeleteByEntity_ValidReferral_ReturnsOk()
        {
            // Arrange
            var referral = _testReferrals[0];

            // Act
            var result = await _controller.DeleteByEntity(referral, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _referralOutgoingCommandHandlerMock.Verify(x => x.DeleteReferralOutgoingByEntity(referral, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateReferralOutgoingById_ValidReferral_ReturnsOk()
        {
            // Arrange
            var referral = _testReferrals[0];

            // Act
            var result = await _controller.UpdateReferralOutgoingById(referral.PatientId, referral, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _referralOutgoingCommandHandlerMock.Verify(x => x.UpdateReferralOutgoing(referral, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateReferralOutgoingList_ValidReferrals_ReturnsOk()
        {
            // Arrange
            var referrals = _testReferrals;

            // Act
            var result = await _controller.UpdateReferralOutgoingList(referrals, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _referralOutgoingCommandHandlerMock.Verify(x => x.UpdateReferralOutgoingList(referrals, _orgId, _isSubscription), Times.Once);
        }


    }
}
