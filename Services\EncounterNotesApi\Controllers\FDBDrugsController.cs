﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]

    public class FDBDrugsController : ControllerBase
    {
        private readonly IFDBDrugsQueryHandler _medicQueryHandler;
        private readonly ILogger<FDBDrugsController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;

        public FDBDrugsController(
            IFDBDrugsQueryHandler medicQueryHandler,
            ILogger<FDBDrugsController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
            )
        {
            _medicQueryHandler = medicQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        /// Get all drug names FDB
        /// </summary>

        [HttpGet("MedicationName")]
        public async Task<ActionResult<IEnumerable<FDBMedicationName>>> GetMedicationNameString()
        {
            ActionResult result;
            try
            {
                var medication = await _medicQueryHandler.GetAllMedicationNames();
                result = medication != null ? Ok(medication) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get Strength
        /// </summary>
        /// <param name="ROUTED_DOSAGE_FORM_MED_ID"></param>
        /// <returns></returns>

        [HttpGet("Medication/{ROUTED_DOSAGE_FORM_MED_ID}")]
        public async Task<ActionResult<IEnumerable<FDBMedication>>> GetMedicationString(string ROUTED_DOSAGE_FORM_MED_ID)
        {
            ActionResult result;
            try
            {
                var medication = await _medicQueryHandler.GetAllMedications(ROUTED_DOSAGE_FORM_MED_ID);
                result = medication != null ? Ok(medication) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get Route
        /// </summary>
        /// <param name="MED_NAME_ID"></param>
        /// <returns></returns>
        [HttpGet("RoutedMedication/{MED_NAME_ID}")]
        public async Task<ActionResult<IEnumerable<FDBRoutedMedication>>> GetRoutedMedication(string MED_NAME_ID)
        {
            ActionResult result;
            try
            {
                var medication = await _medicQueryHandler.GetAllRoutedMedication(MED_NAME_ID);
                result = medication != null ? Ok(medication) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get Take
        /// </summary>
        /// <param name="ROUTED_MED_ID"></param>
        /// <returns></returns>
        [HttpGet("RoutedMedicationFormDosage/{ROUTED_MED_ID}")]
        public async Task<ActionResult<IEnumerable<FDBRoutedDosageFormMedication>>> GetRoutedDosageFormMedication(string ROUTED_MED_ID)
        {
            ActionResult result;
            try
            {
                var medication = await _medicQueryHandler.GetAllRoutedDosageFormMedication(ROUTED_MED_ID);
                result = medication != null ? Ok(medication) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }


        /// <summary>
        /// Get Route Forms
        /// </summary>
        /// <returns></returns>
        [HttpGet("RouteForms")]
        public async Task<ActionResult<IEnumerable<FDBRouteLookUp>>> GetRouteForms()
        {
            ActionResult result;
            try
            {
                var medication = await _medicQueryHandler.GetAllRouteForms();
                result = medication != null ? Ok(medication) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get Take Forms
        /// </summary>
        /// <returns></returns>

        [HttpGet("TakeForms")]
        public async Task<ActionResult<IEnumerable<FDBTakeLookUp>>> GetTakeForms()
        {
            ActionResult result;
            try
            {
                var medication = await _medicQueryHandler.GetAllTakeForms();
                result = medication != null ? Ok(medication) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }


        /// <summary>
        /// Get all ICD codes
        /// </summary>
        /// <returns></returns>
        [HttpGet("ICD")]
        public async Task<ActionResult<IEnumerable<FDB_ICD>>> GetICD()
        {
            ActionResult result;
            try
            {
                var medication = await _medicQueryHandler.GetAllICD();
                result = medication != null ? Ok(medication) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get all Allergies
        /// </summary>
        /// <returns></returns>
        [HttpGet("Allergy")]
        public async Task<ActionResult<IEnumerable<FDBAllergies>>> GetAllergy()
        {
            ActionResult result;
            try
            {
                var medication = await _medicQueryHandler.GetAllAllergyNames();
                result = medication != null ? Ok(medication) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get all vaccines
        /// </summary>
        /// <returns></returns>
        [HttpGet("Vaccine")]
        public async Task<ActionResult<IEnumerable<FDBVaccines>>> GetVaccine()
        {
            ActionResult result;
            try
            {
                var medication = await _medicQueryHandler.GetAllVaccineNames();
                result = medication != null ? Ok(medication) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get CPT for Vaccines
        /// </summary>
        /// <param name="CVX"></param>
        /// <returns></returns>
        [HttpGet("VaccineCPT/{CVX}")]
        public async Task<ActionResult<FDBVaccine_CPT_CVX>> GetCPTforVaccine(string CVX)
        {
            ActionResult result;
            try
            {
                var CPT = await _medicQueryHandler.GetCPT_CVX_Vaccine(CVX);
                result = CPT != null ? Ok(CPT) : Ok(null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get RxNorm Medications
        /// </summary>
        /// <returns></returns>
        [HttpGet("RxNormConcept")]
        public async Task<ActionResult<IEnumerable<RxNormConcept>>> GetRXnormConcMedication()
        {
            ActionResult result;
            try
            {
                var medication = await _medicQueryHandler.GetAllRxConceptMedication();
                result = medication != null ? Ok(medication) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get RxNorm Dosage
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        [HttpGet("RxNormConceptSBDC/{str}")]
        public async Task<ActionResult<IEnumerable<RxNormConcept>>> GetRXnormConcMedication(string str)
        {
            ActionResult result;
            try
            {
                var medication = await _medicQueryHandler.GetAllRxConceptMedicationSBDC(str);
                result = medication != null ? Ok(medication) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

    }
}