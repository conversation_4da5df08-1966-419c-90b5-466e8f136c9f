﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IPastResultsQueryHandler<TText>
    {
        Task<IEnumerable<PastResults>> GetResultByIdAndIsActive(Guid id, Guid OrgId, bool Subscription);
        Task<IEnumerable<PastResults>> GetAllResultsById(Guid id, Guid OrgId, bool Subscription);
    }
}