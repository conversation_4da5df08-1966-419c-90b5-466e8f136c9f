﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Interfaces.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class FamilyMemberRepository : ShardGenericRepository<FamilyMember>, IFamilyMemberRepository
    {
        private readonly RecordDatabaseContext _context;
        private readonly ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> _shardMapManagerService;
        private readonly IStringLocalizer<EncounterDataAccessLayer> _localizer;
        private readonly ILogger<RecordDatabaseContext> _logger;
        private readonly string _shardMapName;

        public FamilyMemberRepository(
            RecordDatabaseContext context,
            ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> shardMapManagerService,
            IStringLocalizer<EncounterDataAccessLayer> localizer,
            ILogger<RecordDatabaseContext> logger)
            : base(context, shardMapManagerService, localizer, logger)
        {
            _context = context;
            _shardMapManagerService = shardMapManagerService;
            _localizer = localizer;
            _logger = logger;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }

        public async Task<List<FamilyMember>> GetFamilyMembersById(Guid recordId, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return new List<FamilyMember>();

            return await context.FamilyMembers
                .Where(wt => wt.PatientId == recordId)
                .ToListAsync();
        }
    }
}
