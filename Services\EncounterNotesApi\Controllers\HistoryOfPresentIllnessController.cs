﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using Microsoft.Extensions.Localization;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesContracts;
using EncounterNotesService.EncounterNotesServiceResources;
using System.Linq;
using EncounterNotesBusinessLayer;
using ShardModels;

namespace EncounterNotesService.Controllers
{

    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class HistoryOfPresentIllnessController : ControllerBase
    {
        private readonly IHistoryOfPresentIllnessCommandHandler<HistoryOfPresentIllness> _hpiCommandHandler;
        private readonly IHistoryOfPresentIllnessQueryHandler<HistoryOfPresentIllness> _hpiQueryHandler;
        private readonly ILogger<HistoryOfPresentIllnessController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;

        public HistoryOfPresentIllnessController(
            IHistoryOfPresentIllnessQueryHandler<HistoryOfPresentIllness> hpiQueryHandler,
            IHistoryOfPresentIllnessCommandHandler<HistoryOfPresentIllness> hpiCommandHandler,
            ILogger<HistoryOfPresentIllnessController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer)
        {
            _hpiCommandHandler = hpiCommandHandler;
            _hpiQueryHandler = hpiQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }


        /// <summary>
        /// Get History of Present Illness by patient Id
        /// </summary>
        /// <param name="patientId"></param>
        /// <returns></returns>
        [HttpGet("patient/{patientId:guid}/{orgId:guid}/{isSubscription}")]
        [AllowAnonymous] // Remove this in production
        public async Task<ActionResult<IEnumerable<HistoryOfPresentIllness>>> GetByPatientId(Guid patientId, Guid orgId, bool isSubscription)
        {
            ActionResult result;
            try
            {
                var records = await _hpiQueryHandler.GetHistoryOfPresentIllnessByPatientId(patientId, orgId, isSubscription);
                result = records != null ? Ok(records) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(500, _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get all the history of present illness by patient id and active
        /// </summary>
        /// <param name="patientId"></param>
        /// <returns></returns>
        [HttpGet("patient/{patientId:guid}/active/{orgId:guid}/{isSubscription}")]
        [AllowAnonymous] // Remove this in production
        public async Task<ActionResult<IEnumerable<HistoryOfPresentIllness>>> GetByPatientIdAndActive(Guid patientId, Guid orgId, bool isSubscription)
        {
            ActionResult result;
            try
            {
                var records = await _hpiQueryHandler.GetHistoryOfPresentIllnessByPatientIdAndActive(patientId, orgId, isSubscription);
                result = records != null ? Ok(records) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(500, _localizer["GetLogError"]);
            }
            return result;
        }
        /// <summary>
        /// Adding new History of Present Illness to the database 
        /// </summary>
        /// <param name="records"></param>
        /// <returns></returns>
        [HttpPost("AddHPI/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> CreateHistoryOfPresentIllness([FromBody] List<HistoryOfPresentIllness> records, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            if (records == null || records.Count == 0)
            {
                result = BadRequest(_localizer["InvalidData"]);
            }

            try
            {
                await _hpiCommandHandler.AddHistoryOfPresentIllness(records, orgId, isSubscription);
                result = Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                result = StatusCode(500, _localizer["PostLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Update History of Present Illness by Id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="record"></param>
        /// <returns></returns>
        [HttpPut("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateHistoryOfPresentIllness(Guid id, [FromBody] HistoryOfPresentIllness record, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            if (record == null || record.Id != id)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }

            try
            {
                await _hpiCommandHandler.UpdateHistoryOfPresentIllness(record, orgId, isSubscription);
                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                result = StatusCode(500, _localizer["UpdateLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Update History of Present Illness in bulk 
        /// </summary>
        /// <param name="records"></param>
        /// <returns></returns>
        [HttpPut("bulk-update/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateHistoryOfPresentIllnessBulk([FromBody] List<HistoryOfPresentIllness> records, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            if (records == null || records.Count == 0)
            {
                result = BadRequest(_localizer["InvalidRecords"]);
            }

            try
            {
                await _hpiCommandHandler.UpdateHistoryOfPresentIllnessBulk(records, orgId, isSubscription);
                result = Ok(_localizer["BulkUpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["BulkUpdateLogError"]);
                result = StatusCode(500, _localizer["BulkUpdateLogError"]);
            }
            return result;
        }

    }
}
