﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class ProductOrganizationMappingsQueryHandler : IProductOrganizationMappingsQueryHandler<ProductOrganizationMapping>
    {
        private readonly IUnitOfWork _unitOfWork;

        public ProductOrganizationMappingsQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<ProductOrganizationMapping> GetProductOrganizationMappingByIdAsync(Guid id)
        {
            var ProductOrganizationMapping = await _unitOfWork.ProductOrganizationMappingRepository.GetByIdAsync(id);
            return ProductOrganizationMapping;
        }

        public async Task<List<ProductOrganizationMapping>> GetAllProductOrganizationMappingsAsync()
        {
            var ProductOrganizationMappings = await _unitOfWork.ProductOrganizationMappingRepository.GetAllAsync();
            return ProductOrganizationMappings.ToList();
        }
    }
}
