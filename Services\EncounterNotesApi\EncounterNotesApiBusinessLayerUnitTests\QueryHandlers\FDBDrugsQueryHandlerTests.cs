using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerUnitTests.QueryHandlers
{
    [TestFixture]
    public class FDBDrugsQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IFDBMedicationRepository> _fdbMedicationRepositoryMock;
        private Mock<IFDBMedicationNameRepository> _fdbMedicationNameRepositoryMock;
        private Mock<IFDBRoutedMedicationRepository> _fdbRoutedMedicationRepositoryMock;
        private Mock<IFDBRoutedDosageFormMedicationRepository> _fdbRoutedDosageFormMedicationRepositoryMock;
        private Mock<IFDBRouteLookUpRepository> _fdbRouteLookUpRepositoryMock;
        private Mock<IFDBTakeLookUpRepository> _fdbTakeLookUpRepositoryMock;
        private Mock<IFDB_ICDRepository> _fdbIcdRepositoryMock;
        private Mock<IFDBAllergyRepository> _fdbAllergyRepositoryMock;
        private Mock<IFDBVaccineRepository> _fdbVaccineRepositoryMock;
        private Mock<IFDBVaccine_CPT_CVXRepository> _fdbVaccineCptCvxRepositoryMock;
        private Mock<IRxNormConceptRepository> _rxNormConceptRepositoryMock;
        private FDBDrugsQueryHandler _fdbDrugsQueryHandler;

        [SetUp]
        public void SetUp()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();

            // Setup repository mocks
            _fdbMedicationRepositoryMock = new Mock<IFDBMedicationRepository>();
            _fdbMedicationNameRepositoryMock = new Mock<IFDBMedicationNameRepository>();
            _fdbRoutedMedicationRepositoryMock = new Mock<IFDBRoutedMedicationRepository>();
            _fdbRoutedDosageFormMedicationRepositoryMock = new Mock<IFDBRoutedDosageFormMedicationRepository>();
            _fdbRouteLookUpRepositoryMock = new Mock<IFDBRouteLookUpRepository>();
            _fdbTakeLookUpRepositoryMock = new Mock<IFDBTakeLookUpRepository>();
            _fdbIcdRepositoryMock = new Mock<IFDB_ICDRepository>();
            _fdbAllergyRepositoryMock = new Mock<IFDBAllergyRepository>();
            _fdbVaccineRepositoryMock = new Mock<IFDBVaccineRepository>();
            _fdbVaccineCptCvxRepositoryMock = new Mock<IFDBVaccine_CPT_CVXRepository>();
            _rxNormConceptRepositoryMock = new Mock<IRxNormConceptRepository>();

            // Setup UnitOfWork to return repository mocks
            _unitOfWorkMock.Setup(u => u.FDBMedicationRepository).Returns(_fdbMedicationRepositoryMock.Object);
            _unitOfWorkMock.Setup(u => u.FDBMedicationNameRepository).Returns(_fdbMedicationNameRepositoryMock.Object);
            _unitOfWorkMock.Setup(u => u.FDBRoutedMedicationRepository).Returns(_fdbRoutedMedicationRepositoryMock.Object);
            _unitOfWorkMock.Setup(u => u.FDBRoutedDosageFormMedicationRepository).Returns(_fdbRoutedDosageFormMedicationRepositoryMock.Object);
            _unitOfWorkMock.Setup(u => u.FDBRouteLookUpRepository).Returns(_fdbRouteLookUpRepositoryMock.Object);
            _unitOfWorkMock.Setup(u => u.FDBTakeLookUpRepository).Returns(_fdbTakeLookUpRepositoryMock.Object);
            _unitOfWorkMock.Setup(u => u.FDBICDRepository).Returns(_fdbIcdRepositoryMock.Object);
            _unitOfWorkMock.Setup(u => u.FDBAllergyRepository).Returns(_fdbAllergyRepositoryMock.Object);
            _unitOfWorkMock.Setup(u => u.FDBVaccineRepository).Returns(_fdbVaccineRepositoryMock.Object);
            _unitOfWorkMock.Setup(u => u.FDBVaccine_CPT_CVXRepository).Returns(_fdbVaccineCptCvxRepositoryMock.Object);
            _unitOfWorkMock.Setup(u => u.RxNormConceptRepository).Returns(_rxNormConceptRepositoryMock.Object);

            // Create the handler with mocked dependencies
            _fdbDrugsQueryHandler = new FDBDrugsQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetAllMedications_ShouldReturnMedications_WhenRepositoryReturnsData()
        {
            // Arrange
            var routedDosageFormMedId = "TEST123";
            var expectedMedications = new List<FDBMedication>
            {
                new FDBMedication { MEDID = "MED1", ROUTED_DOSAGE_FORM_MED_ID = routedDosageFormMedId, MED_MEDID_DESC = "Test Medication 1" },
                new FDBMedication { MEDID = "MED2", ROUTED_DOSAGE_FORM_MED_ID = routedDosageFormMedId, MED_MEDID_DESC = "Test Medication 2" }
            };

            _fdbMedicationRepositoryMock.Setup(r => r.GetMedicationByROUTED_DOSAGE_FORM_MED_ID(routedDosageFormMedId))
                .ReturnsAsync(expectedMedications);

            // Act
            var result = await _fdbDrugsQueryHandler.GetAllMedications(routedDosageFormMedId);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.First().MEDID.Should().Be("MED1");
            result.Last().MEDID.Should().Be("MED2");

            // Verify repository was called with correct parameter
            _fdbMedicationRepositoryMock.Verify(r => r.GetMedicationByROUTED_DOSAGE_FORM_MED_ID(routedDosageFormMedId), Times.Once);
        }

        [Test]
        public async Task GetAllMedicationNames_ShouldReturnMedicationNames_WhenRepositoryReturnsData()
        {
            // Arrange
            var expectedMedicationNames = new List<FDBMedicationName>
            {
                new FDBMedicationName { MED_NAME_ID = "NAME1", MED_NAME = "Test Name 1" },
                new FDBMedicationName { MED_NAME_ID = "NAME2", MED_NAME = "Test Name 2" }
            };

            _fdbMedicationNameRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(expectedMedicationNames);

            // Act
            var result = await _fdbDrugsQueryHandler.GetAllMedicationNames();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.First().MED_NAME_ID.Should().Be("NAME1");
            result.Last().MED_NAME_ID.Should().Be("NAME2");

            // Verify repository was called
            _fdbMedicationNameRepositoryMock.Verify(r => r.GetAsync(), Times.Once);
        }

        [Test]
        public async Task GetAllRoutedMedication_ShouldReturnRoutedMedications_WhenRepositoryReturnsData()
        {
            // Arrange
            var medNameId = "NAME123";
            var expectedRoutedMedications = new List<FDBRoutedMedication>
            {
                new FDBRoutedMedication { ROUTED_MED_ID = "RM1", MED_NAME_ID = medNameId, MED_ROUTED_MED_ID_DESC = "Test Routed Med 1" },
                new FDBRoutedMedication { ROUTED_MED_ID = "RM2", MED_NAME_ID = medNameId, MED_ROUTED_MED_ID_DESC = "Test Routed Med 2" }
            };

            _fdbRoutedMedicationRepositoryMock.Setup(r => r.GetRoutedMedicationByMED_NAME_ID(medNameId))
                .ReturnsAsync(expectedRoutedMedications);

            // Act
            var result = await _fdbDrugsQueryHandler.GetAllRoutedMedication(medNameId);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.First().ROUTED_MED_ID.Should().Be("RM1");
            result.Last().ROUTED_MED_ID.Should().Be("RM2");

            // Verify repository was called with correct parameter
            _fdbRoutedMedicationRepositoryMock.Verify(r => r.GetRoutedMedicationByMED_NAME_ID(medNameId), Times.Once);
        }

        [Test]
        public async Task GetAllRoutedDosageFormMedication_ShouldReturnRoutedDosageForms_WhenRepositoryReturnsData()
        {
            // Arrange
            var routedMedId = "RM123";
            var expectedRoutedDosageForms = new List<FDBRoutedDosageFormMedication>
            {
                new FDBRoutedDosageFormMedication { ROUTED_DOSAGE_FORM_MED_ID = "RDF1", ROUTED_MED_ID = routedMedId },
                new FDBRoutedDosageFormMedication { ROUTED_DOSAGE_FORM_MED_ID = "RDF2", ROUTED_MED_ID = routedMedId }
            };

            _fdbRoutedDosageFormMedicationRepositoryMock.Setup(r => r.GetRoutedDosageFormMedicationByROUTED_MED_ID(routedMedId))
                .ReturnsAsync(expectedRoutedDosageForms);

            // Act
            var result = await _fdbDrugsQueryHandler.GetAllRoutedDosageFormMedication(routedMedId);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.First().ROUTED_DOSAGE_FORM_MED_ID.Should().Be("RDF1");
            result.Last().ROUTED_DOSAGE_FORM_MED_ID.Should().Be("RDF2");

            // Verify repository was called with correct parameter
            _fdbRoutedDosageFormMedicationRepositoryMock.Verify(r => r.GetRoutedDosageFormMedicationByROUTED_MED_ID(routedMedId), Times.Once);
        }

        [Test]
        public async Task GetAllRouteForms_ShouldReturnRouteForms_WhenRepositoryReturnsData()
        {
            // Arrange
            var expectedRouteForms = new List<FDBRouteLookUp>
            {
                new FDBRouteLookUp { MED_ROUTE_ID = "ROUTE1", Route_Name = "Oral" },
                new FDBRouteLookUp { MED_ROUTE_ID = "ROUTE2", Route_Name = "Intravenous" }
            };

            _fdbRouteLookUpRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(expectedRouteForms);

            // Act
            var result = await _fdbDrugsQueryHandler.GetAllRouteForms();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.First().MED_ROUTE_ID.Should().Be("ROUTE1");
            result.Last().MED_ROUTE_ID.Should().Be("ROUTE2");

            // Verify repository was called
            _fdbRouteLookUpRepositoryMock.Verify(r => r.GetAsync(), Times.Once);
        }

        [Test]
        public async Task GetAllTakeForms_ShouldReturnTakeForms_WhenRepositoryReturnsData()
        {
            // Arrange
            var expectedTakeForms = new List<FDBTakeLookUp>
            {
                new FDBTakeLookUp { MED_DOSAGE_FORM_ID = "TAKE1", Take_Name = "Tablet" },
                new FDBTakeLookUp { MED_DOSAGE_FORM_ID = "TAKE2", Take_Name = "Capsule" }
            };

            _fdbTakeLookUpRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(expectedTakeForms);

            // Act
            var result = await _fdbDrugsQueryHandler.GetAllTakeForms();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.First().MED_DOSAGE_FORM_ID.Should().Be("TAKE1");
            result.Last().MED_DOSAGE_FORM_ID.Should().Be("TAKE2");

            // Verify repository was called
            _fdbTakeLookUpRepositoryMock.Verify(r => r.GetAsync(), Times.Once);
        }

        [Test]
        public async Task GetAllICD_ShouldReturnICDCodes_WhenRepositoryReturnsData()
        {
            // Arrange
            var expectedICDCodes = new List<FDB_ICD>
            {
                new FDB_ICD { ICD_CD = "ICD1", ICD_DESC = "Test ICD 1" },
                new FDB_ICD { ICD_CD = "ICD2", ICD_DESC = "Test ICD 2" }
            };

            _fdbIcdRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(expectedICDCodes);

            // Act
            var result = await _fdbDrugsQueryHandler.GetAllICD();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.First().ICD_CD.Should().Be("ICD1");
            result.Last().ICD_CD.Should().Be("ICD2");

            // Verify repository was called
            _fdbIcdRepositoryMock.Verify(r => r.GetAsync(), Times.Once);
        }

        [Test]
        public async Task GetAllAllergyNames_ShouldReturnAllergies_WhenRepositoryReturnsData()
        {
            // Arrange
            var expectedAllergies = new List<FDBAllergies>
            {
                new FDBAllergies { DAM_CONCEPT_ID = "ALLERGY1", DAM_CONCEPT_ID_DESC = "Test Allergy 1" },
                new FDBAllergies { DAM_CONCEPT_ID = "ALLERGY2", DAM_CONCEPT_ID_DESC = "Test Allergy 2" }
            };

            _fdbAllergyRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(expectedAllergies);

            // Act
            var result = await _fdbDrugsQueryHandler.GetAllAllergyNames();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.First().DAM_CONCEPT_ID.Should().Be("ALLERGY1");
            result.Last().DAM_CONCEPT_ID.Should().Be("ALLERGY2");

            // Verify repository was called
            _fdbAllergyRepositoryMock.Verify(r => r.GetAsync(), Times.Once);
        }

        [Test]
        public async Task GetAllVaccineNames_ShouldReturnVaccines_WhenRepositoryReturnsData()
        {
            // Arrange
            var expectedVaccines = new List<FDBVaccines>
            {
                new FDBVaccines { EVD_CVX_CD = "VAX1", EVD_CVX_CD_DESC_SHORT = "Test Vaccine 1" },
                new FDBVaccines { EVD_CVX_CD = "VAX2", EVD_CVX_CD_DESC_SHORT = "Test Vaccine 2" }
            };

            _fdbVaccineRepositoryMock.Setup(r => r.GetActiveVaccines())
                .ReturnsAsync(expectedVaccines);

            // Act
            var result = await _fdbDrugsQueryHandler.GetAllVaccineNames();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.First().EVD_CVX_CD.Should().Be("VAX1");
            result.Last().EVD_CVX_CD.Should().Be("VAX2");

            // Verify repository was called
            _fdbVaccineRepositoryMock.Verify(r => r.GetActiveVaccines(), Times.Once);
        }

        [Test]
        public async Task GetCPT_CVX_Vaccine_ShouldReturnVaccineCPT_WhenRepositoryReturnsData()
        {
            // Arrange
            var cvxCode = "VAX123";
            var expectedVaccineCPT = new FDBVaccine_CPT_CVX
            {
                EVD_CVX_CD = cvxCode,
                EVD_CPT_CD = "CPT123",
                EVD_VACCINE_NAME = "Test Vaccine"
            };

            _fdbVaccineCptCvxRepositoryMock.Setup(r => r.GetCPTforCVX(cvxCode))
                .ReturnsAsync(expectedVaccineCPT);

            // Act
            var result = await _fdbDrugsQueryHandler.GetCPT_CVX_Vaccine(cvxCode);

            // Assert
            result.Should().NotBeNull();
            result.EVD_CVX_CD.Should().Be(cvxCode);
            result.EVD_CPT_CD.Should().Be("CPT123");

            // Verify repository was called with correct parameter
            _fdbVaccineCptCvxRepositoryMock.Verify(r => r.GetCPTforCVX(cvxCode), Times.Once);
        }

        [Test]
        public async Task GetAllRxConceptMedication_ShouldReturnFilteredMedications_WhenRepositoryReturnsData()
        {
            // Arrange
            var allMedications = new List<RxNormConcept>
            {
                new RxNormConcept { RXAUI = "RX1", TTY = "BN", STR = "Brand Name Med" },
                new RxNormConcept { RXAUI = "RX2", TTY = "IN", STR = "Ingredient Med" },
                new RxNormConcept { RXAUI = "RX3", TTY = "OTHER", STR = "Other Med" } // Should be filtered out
            };

            _rxNormConceptRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(allMedications);

            // Act
            var result = await _fdbDrugsQueryHandler.GetAllRxConceptMedication();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2); // Only BN and IN types should be returned
            result.Should().NotContain(m => m.RXAUI == "RX3"); // The OTHER type should be filtered out

            // Verify repository was called
            _rxNormConceptRepositoryMock.Verify(r => r.GetAsync(), Times.Once);
        }

        [Test]
        public async Task GetAllRxConceptMedicationSBDC_ShouldReturnFilteredMedications_WhenRepositoryReturnsData()
        {
            // Arrange
            var searchString = "aspirin";
            var allMedications = new List<RxNormConcept>
            {
                new RxNormConcept { RXAUI = "RX1", TTY = "SBDC", STR = "Aspirin 325 MG" }, // Should match
                new RxNormConcept { RXAUI = "RX2", TTY = "SBDC", STR = "Aspirin 500 MG" }, // Should match
                new RxNormConcept { RXAUI = "RX3", TTY = "SBDC", STR = "Ibuprofen 200 MG" }, // Should not match
                new RxNormConcept { RXAUI = "RX4", TTY = "BN", STR = "Aspirin Brand" } // Wrong TTY, should not match
            };

            _rxNormConceptRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(allMedications);

            // Act
            var result = await _fdbDrugsQueryHandler.GetAllRxConceptMedicationSBDC(searchString);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2); // Only SBDC types containing "aspirin" should be returned
            result.All(m => m.TTY == "SBDC" && m.STR.Contains(searchString, StringComparison.OrdinalIgnoreCase)).Should().BeTrue();

            // Verify repository was called
            _rxNormConceptRepositoryMock.Verify(r => r.GetAsync(), Times.Once);
        }
    }
}
