﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IProductOrganizationMappingsCommandHandler<T>
    {
        Task AddProductOrganizationMappingAsync(List<T> ProductOrganizationMappings, Guid OrgId);

        Task UpdateProductOrganizationMappingAsync(T ProductOrganizationMapping);

        Task DeleteProductOrganizationMappingAsync(Guid id);
    }
}
