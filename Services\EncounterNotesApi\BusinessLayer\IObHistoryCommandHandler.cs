﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using ShardModels;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public interface IObHistoryCommandHandler<T> where T : class
    {
        Task AddAsync(IEnumerable<T> obhistory, Guid OrgId, bool Subscription);
        Task UpdateAsync(T complaint, Guid OrgId, bool Subscription);
        Task UpdateObHistoryListAsync(IEnumerable<ObHistory> obhistory, Guid OrgId, bool Subscription);
        Task DeleteByIdAsync(Guid ObId, Guid OrgId, bool Subscription);
        Task DeleteByEntity(T obhistory, Guid OrgId, bool Subscription);
    }
}
