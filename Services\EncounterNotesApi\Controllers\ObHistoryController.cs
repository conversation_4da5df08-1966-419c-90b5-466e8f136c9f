﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using Microsoft.AspNetCore.Authorization;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.Identity.Web.Resource;
using System.Text.Json;
using System.Text;
using System.Text.Json.Serialization;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using ShardModels;

namespace EncounterNotesApi.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ObHistoryController : ControllerBase
    {
        private readonly IObHistoryCommandHandler<ObHistory> _obHistoryCommandHandler;
        private readonly IObHistoryQueryHandler<ObHistory> _obHistoryQueryHandler;
        private readonly ILogger<ObHistoryController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;
        private readonly JsonSerializerOptions _jsonOptions;

        private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(10);
        private readonly TimeSpan _suggestionCacheExpiration = TimeSpan.FromMinutes(5);
        private JsonDocument content;

        /// <summary>
        /// Initializes a new instance of the <see cref="ObHistoryController"/> class.
        /// </summary>
        /// <param name="obHistoryQueryHandler">The OB history query handler.</param>
        /// <param name="obHistoryCommandHandler">The OB history command handler.</param>
        /// <param name="logger">The logger instance.</param>
        /// <param name="localizer">The localizer for the application strings.</param>
        public ObHistoryController(
            IObHistoryQueryHandler<ObHistory> obHistoryQueryHandler,
            IObHistoryCommandHandler<ObHistory> obHistoryCommandHandler,
            ILogger<ObHistoryController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer)
        {
            _obHistoryCommandHandler = obHistoryCommandHandler;
            _obHistoryQueryHandler = obHistoryQueryHandler;
            _logger = logger;
            _localizer = localizer;
            _jsonOptions = new JsonSerializerOptions
            {
                ReferenceHandler = ReferenceHandler.IgnoreCycles,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
        }

        /// <summary>
        /// Creates a new OB history record.
        /// </summary>
        /// <param name="obHistoryDto">The OB history data transfer object.</param>
        /// <returns>The action result indicating success or failure.</returns>

        [HttpPost]
        [Route("{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> CreateObHistory([FromBody] ObHistory obHistoryDto, Guid orgId, bool isSubscription)
        {
            try
            {
                var obHistory = new ObHistory
                {
                    obId = Guid.NewGuid(),
                    PatientId = obHistoryDto.PatientId,
                    Notes = obHistoryDto.Notes,
                    Symptoms = obHistoryDto.Symptoms,
                    DateOfComplaint = obHistoryDto.DateOfComplaint, // Added line
                    OrganizationId = obHistoryDto.OrganizationId,
                    PcpId = obHistoryDto.PcpId
                };

                await _obHistoryCommandHandler.AddAsync(new List<ObHistory> { obHistory }, orgId, isSubscription);
                //return CreatedAtAction(nameof(GetObHistoryById), new { id = obHistory.obId }, obHistory);
                return CreatedAtAction(
                    nameof(GetObHistoryById),
                    new { id = obHistory.obId, orgId = orgId, isSubscription = isSubscription },
                    obHistory
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorCreatingObHistory"]);
                return StatusCode(500, _localizer["ErrorCreatingObHistory"]);
            }
        }

        /// <summary>
        /// Retrieves an OB history by its ID.
        /// </summary>
        /// <param name="id">The OB history ID.</param>
        /// <returns>The action result containing the OB history or an error message.</returns>
        [HttpGet("{id}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> GetObHistoryById(Guid id, Guid orgId, bool isSubscription)
        {
            try
            {
                var obHistory = await _obHistoryQueryHandler.GetByIdAsync(id, orgId, isSubscription);
                if (obHistory == null)
                {
                    return NotFound(_localizer["ObHistoryNotFound"]);
                }

                return Ok(obHistory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingObHistory"]);
                return BadRequest(_localizer["ErrorFetchingObHistory"]);
            }
        }

        /// <summary>
        /// Retrieves all OB histories for a specific patient.
        /// </summary>
        /// <param name="patientId">The patient ID.</param>
        /// <returns>The action result containing a list of OB histories.</returns>
        [HttpGet("patient/{patientId}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> GetByPatientIdAsync(Guid patientId, Guid orgId, bool isSubscription)
        {
            try
            {
                var obHistories = await _obHistoryQueryHandler.GetByPatientIdAsync(patientId, orgId, isSubscription);
                return Ok(obHistories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingObHistories"]);
                return BadRequest(_localizer["ErrorFetchingObHistories"]);
            }
        }

        /// <summary>
        /// Deletes an OB history record by its ID.
        /// </summary>
        /// <param name="id">The OB history ID.</param>
        /// <returns>The action result indicating success or failure.</returns>
        [HttpDelete("{id}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> DeleteObHistory(Guid id, Guid orgId, bool isSubscription)
        {
            try
            {
                var obHistory = await _obHistoryQueryHandler.GetByIdAsync(id, orgId, isSubscription);
                if (obHistory == null)
                {
                    return NotFound(_localizer["ObHistoryNotFound"]);
                }

                await _obHistoryCommandHandler.DeleteByIdAsync(id, orgId, isSubscription);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingObHistory"]);
                return BadRequest(_localizer["ErrorDeletingObHistory"]);
            }
        }

        /// <summary>
        /// Bulk updates a list of OB history records.
        /// </summary>
        /// <param name="obHistories">The list of OB history records to update.</param>
        /// <returns>The action result indicating success or failure.</returns>
        [HttpPut("bulk-update/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateObHistoryList([FromBody] List<ObHistory> obHistories, Guid orgId, bool isSubscription)
        {
            if (obHistories == null || !obHistories.Any())
            {
                return BadRequest("Invalid records provided.");
            }

            try
            {
                await _obHistoryCommandHandler.UpdateObHistoryListAsync(obHistories, orgId, isSubscription);
                return Ok("Bulk update successful");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating ObHistory list");
                return StatusCode(500, "An error occurred while updating the ObHistory list.");
            }
        }

        /// <summary>
        /// Updates an existing OB history record by its ID.
        /// </summary>
        /// <param name="id">The OB history ID.</param>
        /// <param name="obHistoryDto">The OB history data transfer object.</param>
        /// <returns>The action result containing the updated OB history or an error message.</returns>
        [HttpPut("{id}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateObHistory(Guid id, [FromBody] ObHistory obHistoryDto, Guid orgId, bool isSubscription)
        {
            try
            {
                var existingObHistory = await _obHistoryQueryHandler.GetByIdAsync(id, orgId, isSubscription);
                if (existingObHistory == null)
                {
                    return NotFound(_localizer["ObHistoryNotFound"]);
                }

                existingObHistory.Notes = obHistoryDto.Notes;
                existingObHistory.Symptoms = obHistoryDto.Symptoms;
                existingObHistory.DateOfComplaint = obHistoryDto.DateOfComplaint; // Added line
                existingObHistory.OrganizationId = obHistoryDto.OrganizationId;
                existingObHistory.PcpId = obHistoryDto.PcpId;
                await _obHistoryCommandHandler.UpdateAsync(existingObHistory, orgId, isSubscription);

                return Ok(existingObHistory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingObHistory"]);
                return StatusCode(500, _localizer["ErrorUpdatingObHistory"]);
            }
        }

    }
}
