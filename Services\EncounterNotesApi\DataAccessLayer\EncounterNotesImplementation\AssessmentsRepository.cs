﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Interfaces.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class AssessmentsRepository : ShardGenericRepository<AssessmentsData>, IAssessmentsRepository
    {
        private readonly ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> _shardMapManagerService;
        private readonly IStringLocalizer<EncounterDataAccessLayer> _localizer;
        private readonly ILogger<RecordDatabaseContext> _logger;
        private readonly string _shardMapName;

        public AssessmentsRepository(
            RecordDatabaseContext context,
            ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> shardMapManagerService,
            IStringLocalizer<EncounterDataAccessLayer> localizer,
            ILogger<RecordDatabaseContext> logger)
            : base(context, shardMapManagerService, localizer, logger)
        {
            _shardMapManagerService = shardMapManagerService;
            _localizer = localizer;
            _logger = logger;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }

        // Get all assessments with sharding support
        public async Task<IEnumerable<AssessmentsData>> GetAllAssessmentsAsync(Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return Enumerable.Empty<AssessmentsData>();

            return await context._Assessments
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<List<string>> GetAssessmentRelatedMedications(Guid PatientId, Guid OrgId, bool Subscription)
        {
            using var _context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (_context == null) return new List<string>();
            var query = from a in _context._Assessments
                        join cc in _context.ChiefComplaint
                            on new { CheifComplaintId = a.CheifComplaintId, PatientId = a.PatientId }
                            equals new { CheifComplaintId = cc.Id, PatientId = cc.PatientId }
                        join m in _context.CurrentMedications
                            on new { CheifComplaintId = cc.Id, PatientId = cc.PatientId }
                            equals new { CheifComplaintId = m.CheifComplaintId, PatientId = m.PatientId }
                        where a.PatientId == PatientId && a.IsActive == true && m.isActive == true
                        select new
                        {
                            a.AssessmentsID,
                            a.PatientId,
                            a.Diagnosis,
                            ChiefComplaint = cc.Description,
                            m.MedicineId,
                            m.BrandName,
                            m.DrugDetails
                        };
            List<string> result = await query.Select(x => $"{x.Diagnosis} == {x.DrugDetails}").ToListAsync();
            return result;
        }

        public async Task<List<AssessmentsData>> GetListOfAssesmentsThroughCheifComplaintId(Guid CheifComplaintId, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return new List<AssessmentsData>();

            return await context._Assessments
                .Where(a => a.CheifComplaintId == CheifComplaintId)
                .AsNoTracking()
                .ToListAsync();
        }
    }
}
