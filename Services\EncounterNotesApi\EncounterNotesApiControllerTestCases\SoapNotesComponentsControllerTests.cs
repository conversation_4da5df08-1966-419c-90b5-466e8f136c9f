using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using EncounterNotesDataAccessLayer.EncounterNotesContext;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class SoapNotesComponentsControllerTests
    {
        private Mock<ISoapNotesComponentsQueryHandler<SoapNotesComponents>> _soapNotesComponentsQueryHandlerMock;
        private Mock<ILogger<SoapNotesComponentsController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private RecordDatabaseContext _contextMock;
        private SoapNotesComponentsController _controller;
        private List<SoapNotesComponents> _testSoapNotesComponents;

        [SetUp]
        public void Setup()
        {
            _soapNotesComponentsQueryHandlerMock = new Mock<ISoapNotesComponentsQueryHandler<SoapNotesComponents>>();
            _loggerMock = new Mock<ILogger<SoapNotesComponentsController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            // Since the controller doesn't actually use the context, we can pass null
            _contextMock = null;

            // Mock localizer strings
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error fetching SOAP notes components."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testSoapNotesComponents = new List<SoapNotesComponents>
            {
                new SoapNotesComponents { Id = Guid.NewGuid(), Name = "Subjective" },
                new SoapNotesComponents { Id = Guid.NewGuid(), Name = "Objective" },
                new SoapNotesComponents { Id = Guid.NewGuid(), Name = "Assessment" },
                new SoapNotesComponents { Id = Guid.NewGuid(), Name = "Plan" }
            };

            _controller = new SoapNotesComponentsController(
                _soapNotesComponentsQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object,
                _contextMock
            );
        }

        [Test]
        public async Task Get_ReturnsListOfSoapNotesComponents()
        {
            // Arrange
            _soapNotesComponentsQueryHandlerMock.Setup(x => x.GetDetails()).ReturnsAsync(_testSoapNotesComponents);

            // Act
            var result = await _controller.Get();

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var components = okResult.Value as IEnumerable<SoapNotesComponents>;
            components.Should().NotBeNull();
            components.Should().BeEquivalentTo(_testSoapNotesComponents);
        }

        [Test]
        public async Task Get_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            _soapNotesComponentsQueryHandlerMock.Setup(x => x.GetDetails()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Get();

            // Assert
            var statusCodeResult = result.Result as ObjectResult;
            statusCodeResult.Should().NotBeNull();
            statusCodeResult.StatusCode.Should().Be(500);
            statusCodeResult.Value.Should().Be(_localizerMock.Object["GetLogError"]);
        }
    }
}
