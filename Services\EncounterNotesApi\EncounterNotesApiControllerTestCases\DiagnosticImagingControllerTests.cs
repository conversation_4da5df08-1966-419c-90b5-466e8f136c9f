using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;
using EncounterNotesDataAccessLayer.EncounterNotesContext;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class DiagnosticImagingControllerTests
    {
        private Mock<IDiagnosticImagingCommandHandler<DiagnosticImage>> _diagnosticImagingCommandHandlerMock;
        private Mock<IDiagnosticImagingQueryHandler<DiagnosticImage>> _diagnosticImagingQueryHandlerMock;
        private Mock<ILogger<DiagnosticImagingController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private DiagnosticImagingController _controller;
        private List<DiagnosticImage> _testDiagnosticImages;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _diagnosticImagingCommandHandlerMock = new Mock<IDiagnosticImagingCommandHandler<DiagnosticImage>>();
            _diagnosticImagingQueryHandlerMock = new Mock<IDiagnosticImagingQueryHandler<DiagnosticImage>>();
            _loggerMock = new Mock<ILogger<DiagnosticImagingController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error fetching diagnostic images."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Diagnostic image not found."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No diagnostic images provided."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Diagnostic images added successfully."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error adding diagnostic images."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Diagnostic image deleted successfully."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting diagnostic image."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid diagnostic image record."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Diagnostic image updated successfully."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating diagnostic image."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));

            _testDiagnosticImages = new List<DiagnosticImage>
            {
                new DiagnosticImage {
                    RecordID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationID = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Type = "X-Ray",
                    DiCompany = "Hospital Radiology",
                    ccResults = "Normal chest X-ray",
                    Lookup = "Chest",
                    OrderName = "Chest X-Ray",
                    StartsWith = "C",
                    IsActive = true,
                    Subscription = _isSubscription
                },
                new DiagnosticImage {
                    RecordID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationID = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-15),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Type = "MRI",
                    DiCompany = "Imaging Center",
                    ccResults = "Small lesion in left frontal lobe",
                    Lookup = "Brain",
                    OrderName = "Brain MRI",
                    StartsWith = "B",
                    IsActive = true,
                    Subscription = _isSubscription
                }
            };

            // Instead of mocking the DbContext, pass null since we're not using it in the tests
            _controller = new DiagnosticImagingController(
                _diagnosticImagingQueryHandlerMock.Object,
                _diagnosticImagingCommandHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object,
                null // Pass null instead of trying to mock the context
            );
        }

        [Test]
        public async Task GetById_ReturnsDiagnosticImages_WhenImagesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientImages = _testDiagnosticImages.Select(i => { i.PatientId = patientId; return i; }).ToList();
            _diagnosticImagingQueryHandlerMock.Setup(x => x.GetDiagnosticImagingById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientImages);

            // Act
            var result = await _controller.GetById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedImages = okResult.Value as IEnumerable<DiagnosticImage>;
            returnedImages.Should().NotBeNull();
            returnedImages.Should().BeEquivalentTo(patientImages);
        }

        [Test]
        public async Task GetById_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _diagnosticImagingQueryHandlerMock.Setup(x => x.GetDiagnosticImagingById(patientId, _orgId, _isSubscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetById(patientId, _orgId, _isSubscription);

            // Assert
            var statusCodeResult = result.Result as ObjectResult;
            statusCodeResult.Should().NotBeNull();
            statusCodeResult.StatusCode.Should().Be(500);
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsActiveDiagnosticImages_WhenImagesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activeImages = _testDiagnosticImages.Select(i => { i.PatientId = patientId; i.IsActive = true; return i; }).ToList();
            _diagnosticImagingQueryHandlerMock.Setup(x => x.GetDiagnosticImagingByIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activeImages);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedImages = okResult.Value as IEnumerable<DiagnosticImage>;
            returnedImages.Should().NotBeNull();
            returnedImages.Should().BeEquivalentTo(activeImages);
        }

        [Test]
        public async Task AddDiagnosticImaging_ValidImages_ReturnsOk()
        {
            // Arrange
            var imagesToAdd = _testDiagnosticImages;

            // Act
            var result = await _controller.AddDiagnosticImaging(imagesToAdd, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["AddSuccessful"]);

            // Verify the command handler was called
            _diagnosticImagingCommandHandlerMock.Verify(x => x.AddDiagnosticImaging(imagesToAdd, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task AddDiagnosticImaging_EmptyImages_ReturnsBadRequest()
        {
            // Arrange
            var emptyImages = new List<DiagnosticImage>();

            // Act
            var result = await _controller.AddDiagnosticImaging(emptyImages, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["AddressInvalidMessage"]);

            // Verify the command handler was not called
            _diagnosticImagingCommandHandlerMock.Verify(x => x.AddDiagnosticImaging(It.IsAny<List<DiagnosticImage>>(), _orgId, _isSubscription), Times.Never);
        }

        [Test]
        public async Task DeleteById_ValidImage_ReturnsOk()
        {
            // Arrange
            var imageToDelete = _testDiagnosticImages[0];
            var imageId = imageToDelete.RecordID;

            // Act
            var result = await _controller.DeleteById(imageId, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulDeletion"]);

            // Verify the command handler was called
            _diagnosticImagingCommandHandlerMock.Verify(x => x.DeleteDiagnosticImagingById(imageId, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateById_ValidImage_ReturnsOk()
        {
            // Arrange
            var imageToUpdate = _testDiagnosticImages[0];
            var imageId = imageToUpdate.RecordID;

            // Act
            var result = await _controller.UpdateById(imageId, imageToUpdate, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _diagnosticImagingCommandHandlerMock.Verify(x => x.UpdateDiagnosticImaging(imageToUpdate, _orgId, _isSubscription), Times.Once);
        }


    }
}
