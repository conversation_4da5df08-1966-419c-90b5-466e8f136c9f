﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using Microsoft.Azure.Amqp.Framing;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using Sprache;
using ShardModels;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class DiagnosticImagingController : ControllerBase
    {
        private readonly IDiagnosticImagingCommandHandler<DiagnosticImage> _diagnosticImagingCommandHandler;
        private readonly IDiagnosticImagingQueryHandler<DiagnosticImage> _diagnosticImagingQueryHandler;
        private readonly ILogger<DiagnosticImagingController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;
        public DiagnosticImagingController(
            IDiagnosticImagingQueryHandler<DiagnosticImage> diagnosticImagingQueryHandler,
            IDiagnosticImagingCommandHandler<DiagnosticImage> diagnosticImagingCommandHandler,
            ILogger<DiagnosticImagingController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer,
            RecordDatabaseContext context
            )
        {
            _diagnosticImagingCommandHandler = diagnosticImagingCommandHandler;
            _diagnosticImagingQueryHandler = diagnosticImagingQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        ///  Get All DiagnosticImaging by Id 
        /// </summary>
        [HttpGet("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<List<DiagnosticImage>>> GetById(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult<List<DiagnosticImage>> result;
            try
            {
                var diagnosticImagings = await _diagnosticImagingQueryHandler.GetDiagnosticImagingById(id, orgId, isSubscription);
                result = diagnosticImagings != null && diagnosticImagings.Any()
                    ? Ok(diagnosticImagings)
                    : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Update an existing DiagnosticImaging
        /// </summary>
        [HttpPut("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateById(Guid id, [FromBody] DiagnosticImage diagnosticImaging, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            if (diagnosticImaging == null || diagnosticImaging.RecordID != id)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _diagnosticImagingCommandHandler.UpdateDiagnosticImaging(diagnosticImaging, orgId, isSubscription);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                }
            }
            return result;
        }

        /// <summary>
        ///  Delete an existing DiagnosticImaging By Id
        /// </summary>
        [HttpDelete("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> DeleteById(Guid id, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            try
            {
                await _diagnosticImagingCommandHandler.DeleteDiagnosticImagingById(id, orgId, isSubscription);
                result = Ok(_localizer["SuccessfulDeletion"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Add new DiagnosticImaging
        /// </summary>
        [HttpPost]
        [Route("{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> AddDiagnosticImaging([FromBody] List<DiagnosticImage> diagnosticImaging, Guid orgId, bool isSubscription)
        {
            IActionResult response;
            if (diagnosticImaging == null || diagnosticImaging.Count == 0)
            {
                _logger.LogWarning(_localizer["InvalidAddressData"]);
                response = BadRequest(_localizer["AddressInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["AddingNewAddress"]);
                    await _diagnosticImagingCommandHandler.AddDiagnosticImaging(diagnosticImaging, orgId, isSubscription);
                    _logger.LogInformation(_localizer["AddressAddedSuccessfully"]);
                    response = Ok(_localizer["AddSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorAddingAddress"]);
                    response = StatusCode(int.Parse(_localizer["500"]), _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        /// <summary>
        ///  Get Active Diagnostic Imaging by Id 
        /// </summary>
        [HttpGet("{id:guid}/isActive/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<DiagnosticImage>>> GetAllByIdAndIsActive(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult result;
            try
            {
                var diagnosticImaging = await _diagnosticImagingQueryHandler.GetDiagnosticImagingByIdAndIsActive(id, orgId, isSubscription);
                result = diagnosticImaging != null ? Ok(diagnosticImaging) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        ///  Update an existing List of diagnostic Imaging
        /// </summary>
        [HttpPut("updateDiagnosticImaging/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateAccess(List<DiagnosticImage> diagnosticImaging, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            if (diagnosticImaging == null || !diagnosticImaging.Any())
            {
                _logger.LogWarning(_localizer["AccessError"]);
                result = BadRequest(_localizer["AccessError"]);
            }
            else
            {
                try
                {
                    var success = await _diagnosticImagingCommandHandler.UpdateDiagnosticImagingList(diagnosticImaging, orgId, isSubscription);
                    result = success
                        ? Ok(new { Message = _localizer["UpdateSuccessful"] })
                        : NotFound(_localizer["RecordNotFound"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["InternalServerErrorMessage"]);
                }
            }
            return result;
        }

    }
}
