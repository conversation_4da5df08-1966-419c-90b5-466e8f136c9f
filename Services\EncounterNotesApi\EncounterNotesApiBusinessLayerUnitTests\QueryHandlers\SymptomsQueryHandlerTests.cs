using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class SymptomsQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ISymptomsRepository> _symptomsRepositoryMock;
        private SymptomsQueryHandler _symptomsQueryHandler;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _symptomsRepositoryMock = new Mock<ISymptomsRepository>();

            _unitOfWorkMock.Setup(u => u.SymptomsRepository).Returns(_symptomsRepositoryMock.Object);
            _symptomsQueryHandler = new SymptomsQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetAllSymptoms_ShouldReturnAllSymptoms()
        {
            // Arrange
            var symptoms = new List<Symptoms>
            {
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Fever"
                },
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Headache"
                },
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Cough"
                },
                new Symptoms
                {
                    SymptomId = Guid.NewGuid(),
                    Symptom = "Fatigue"
                }
            };

            _symptomsRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(symptoms);

            // Act
            var result = await _symptomsQueryHandler.GetAllSymptoms();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(4);
            result.Should().BeEquivalentTo(symptoms);
        }

        [Test]
        public async Task GetAllSymptoms_WithNoSymptoms_ShouldReturnEmptyCollection()
        {
            // Arrange
            var symptoms = new List<Symptoms>();

            _symptomsRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(symptoms);

            // Act
            var result = await _symptomsQueryHandler.GetAllSymptoms();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetAllSymptoms_WhenRepositoryThrowsException_ShouldHandleExceptionAndReturnEmptyList()
        {
            // Arrange
            _symptomsRepositoryMock.Setup(r => r.GetAsync())
                .ThrowsAsync(new Exception("Database connection error"));

            // Act
            var result = await _symptomsQueryHandler.GetAllSymptoms();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
