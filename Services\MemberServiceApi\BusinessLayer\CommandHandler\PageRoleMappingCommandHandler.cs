using System;
using System.Threading.Tasks;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class PageRoleMappingCommandHandler : IPageRoleMappingCommandHandler<PageRoleMapping>
    {
        private readonly IUnitOfWork _unitOfWork;

        public PageRoleMappingCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddPageRoleMappingAsync(PageRoleMapping pageRoleMapping, Guid OrganizationId, bool Subscription)
        {
            if (pageRoleMapping == null)
                throw new ArgumentNullException(nameof(pageRoleMapping));

            await _unitOfWork.PageRoleMappingRepository.AddAsync(new List<PageRoleMapping> { pageRoleMapping }, OrganizationId, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdatePageRoleMappingAsync(PageRoleMapping pageRoleMapping, Guid OrganizationId, bool Subscription)
        {
            if (pageRoleMapping == null)
                throw new ArgumentNullException(nameof(pageRoleMapping));

            await _unitOfWork.PageRoleMappingRepository.UpdateAsync(pageRoleMapping, OrganizationId, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeletePageRoleMappingAsync(Guid id, Guid OrganizationId, bool Subscription)
        {
            await _unitOfWork.PageRoleMappingRepository.DeleteByIdAsync(id, OrganizationId, Subscription);
            await _unitOfWork.SaveAsync();
        }
    }
}
