using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class DiagnosticImagingPageCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<DiagnosticImagingPageCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, DiagnosticImagingDTO, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IDiagnosticImagingPageRepository> _diagnosticImagingPageRepositoryMock;
        private Mock<IDiagnosticImagingAssessmentRepository> _diagnosticImagingAssessmentRepositoryMock;
        private DiagnosticImagingPageCommandHandler _diagnosticImagingPageCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<DiagnosticImagingPageCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, DiagnosticImagingDTO, EncounterDataAccessLayer>>();
            _diagnosticImagingPageRepositoryMock = new Mock<IDiagnosticImagingPageRepository>();
            _diagnosticImagingAssessmentRepositoryMock = new Mock<IDiagnosticImagingAssessmentRepository>();

            _unitOfWorkMock.Setup(u => u.DiagnosticImagingPageRepository).Returns(_diagnosticImagingPageRepositoryMock.Object);
            _unitOfWorkMock.Setup(u => u.DiagnosticImagingAssessmentRepository).Returns(_diagnosticImagingAssessmentRepositoryMock.Object);

            _diagnosticImagingPageCommandHandler = new DiagnosticImagingPageCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task AddDiagnosticImaging_WithAssessments_ShouldCallBothRepositoriesAndSave()
        {
            // Arrange
            var assessments = new List<DiagnosticImagingAssessment>
            {
                new DiagnosticImagingAssessment
                {
                    ID = Guid.NewGuid(),
                    AssessmentID = Guid.NewGuid()
                }
            };

            var diagnosticImages = new List<DiagnosticImagingDTO>
            {
                new DiagnosticImagingDTO
                {
                    RecordID = Guid.NewGuid(),
                    OrganizationID = _orgId,
                    PatientId = Guid.NewGuid(),
                    Status = "Pending",
                    Provider = "Test Provider",
                    Facility = "Test Facility",
                    AssignedTo = "Dr. Smith",
                    FutureOrder = false,
                    HigherPriority = true,
                    InHouse = true,
                    Procedgure = "X-Ray",
                    OrderDate = DateTime.Now,
                    Reason = "Chest pain",
                    Recieved = false,
                    Date = DateTime.Now.AddDays(1),
                    Result = null,
                    Notes = "Patient reports chest pain for 3 days",
                    IsActive = true,
                    CreatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now,
                    Subscription = _subscription,
                    Assessments = assessments
                }
            };

            // Act
            await _diagnosticImagingPageCommandHandler.AddDiagnosticImaging(diagnosticImages, _orgId, _subscription);

            // Assert
            _diagnosticImagingPageRepositoryMock.Verify(r => r.AddAsync(diagnosticImages, _orgId, _subscription), Times.Once);
            _diagnosticImagingAssessmentRepositoryMock.Verify(r => r.AddAsync(diagnosticImages[0].Assessments), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task AddDiagnosticImaging_WithoutAssessments_ShouldCallOnlyPageRepositoryAndSave()
        {
            // Arrange
            var diagnosticImages = new List<DiagnosticImagingDTO>
            {
                new DiagnosticImagingDTO
                {
                    RecordID = Guid.NewGuid(),
                    OrganizationID = _orgId,
                    PatientId = Guid.NewGuid(),
                    Status = "Pending",
                    Provider = "Test Provider",
                    Facility = "Test Facility",
                    IsActive = true,
                    CreatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now,
                    Subscription = _subscription,
                    Assessments = null
                }
            };

            // Act
            await _diagnosticImagingPageCommandHandler.AddDiagnosticImaging(diagnosticImages, _orgId, _subscription);

            // Assert
            _diagnosticImagingPageRepositoryMock.Verify(r => r.AddAsync(diagnosticImages, _orgId, _subscription), Times.Once);
            _diagnosticImagingAssessmentRepositoryMock.Verify(r => r.AddAsync(It.IsAny<ICollection<DiagnosticImagingAssessment>>()), Times.Never);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateDiagnosticImaging_ValidDiagnosticImage_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var diagnosticImage = new DiagnosticImagingDTO
            {
                RecordID = Guid.NewGuid(),
                OrganizationID = _orgId,
                PatientId = Guid.NewGuid(),
                Status = "Updated Status",
                Provider = "Updated Provider",
                Facility = "Updated Facility",
                IsActive = true,
                UpdatedBy = Guid.NewGuid(),
                UpdatedDate = DateTime.Now,
                Subscription = _subscription
            };

            // Act
            await _diagnosticImagingPageCommandHandler.UpdateDiagnosticImaging(diagnosticImage, _orgId, _subscription);

            // Assert
            _diagnosticImagingPageRepositoryMock.Verify(r => r.UpdateAsync(diagnosticImage, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteDiagnosticImagingById_ValidId_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var id = Guid.NewGuid();

            // Act
            await _diagnosticImagingPageCommandHandler.DeleteDiagnosticImagingById(id, _orgId, _subscription);

            // Assert
            _diagnosticImagingPageRepositoryMock.Verify(r => r.DeleteByIdAsync(id, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}
