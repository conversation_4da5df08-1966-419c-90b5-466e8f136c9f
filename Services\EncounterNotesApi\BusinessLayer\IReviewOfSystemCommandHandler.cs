﻿using EncounterNotesContracts;
using ShardModels;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface IReviewOfSystemCommandHandler<TText>
    {
        Task DeleteReviewOfSystemByEntity(ReviewOfSystem reviewOfSystem, Guid OrgId, bool Subscription);
        Task DeleteReviewOfSystemById(Guid id, Guid OrgId, bool Subscription);
        Task AddReviewOfSystem(List<TText> texts, Guid OrgId, bool Subscription);
        Task UpdateReviewOfSystem(ReviewOfSystem reviewOfSystem, Guid OrgId, bool Subscription);
        Task UpdateReviewOfSystemList(List<ReviewOfSystem> reviewOfSystems, Guid OrgId, bool Subscription);
    }
}
