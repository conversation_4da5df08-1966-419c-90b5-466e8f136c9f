using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class ImmunizationQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IImmunizationRepository> _immunizationRepositoryMock;
        private ImmunizationQueryHandler _immunizationQueryHandler;
        private Guid _orgId;
        private bool _subscription;
        private Guid _patientId;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _immunizationRepositoryMock = new Mock<IImmunizationRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;
            _patientId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.ImmunizationRepository).Returns(_immunizationRepositoryMock.Object);
            _immunizationQueryHandler = new ImmunizationQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetAllImmunizationsById_ShouldReturnAllImmunizationsForPatient()
        {
            // Arrange
            var immunizations = new List<Immunization>
            {
                new Immunization
                {
                    ImmunizationId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Immunizations = "Influenza",
                    CPTCode = "90658",
                    CVXCode = "88",
                    Comments = "Annual flu shot",
                    CPTDescription = "Influenza virus vaccine, trivalent",
                    GivenDate = DateTime.Now.AddDays(-30),
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    Subscription = _subscription
                },
                new Immunization
                {
                    ImmunizationId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-60),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Immunizations = "COVID-19",
                    CPTCode = "91300",
                    CVXCode = "213",
                    Comments = "First dose",
                    CPTDescription = "SARS-CoV-2 (COVID-19) vaccine",
                    GivenDate = DateTime.Now.AddDays(-60),
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    Subscription = _subscription
                },
                new Immunization
                {
                    ImmunizationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-45),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Immunizations = "Tetanus",
                    CPTCode = "90715",
                    CVXCode = "115",
                    Comments = "Booster",
                    CPTDescription = "Tetanus, diphtheria toxoids and acellular pertussis vaccine",
                    GivenDate = DateTime.Now.AddDays(-45),
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _immunizationRepositoryMock.Setup(r => r.GetAllImmunizationsAsync(_orgId, _subscription))
                .ReturnsAsync(immunizations);

            // Act
            var result = await _immunizationQueryHandler.GetAllImmunizationsById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(i => i.PatientId == _patientId).Should().BeTrue();
        }

        [Test]
        public async Task GetAllImmunizationsById_WithNoImmunizations_ShouldReturnEmptyCollection()
        {
            // Arrange
            var immunizations = new List<Immunization>();

            _immunizationRepositoryMock.Setup(r => r.GetAllImmunizationsAsync(_orgId, _subscription))
                .ReturnsAsync(immunizations);

            // Act
            var result = await _immunizationQueryHandler.GetAllImmunizationsById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetImmunizationByIdAndIsActive_ShouldReturnActiveImmunizationsForPatient()
        {
            // Arrange
            var immunizations = new List<Immunization>
            {
                new Immunization
                {
                    ImmunizationId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    Immunizations = "Influenza",
                    IsActive = true,
                    Subscription = _subscription
                },
                new Immunization
                {
                    ImmunizationId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    Immunizations = "COVID-19",
                    IsActive = false, // Inactive
                    Subscription = _subscription
                },
                new Immunization
                {
                    ImmunizationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    Immunizations = "Tetanus",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _immunizationRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(immunizations);

            // Act
            var result = await _immunizationQueryHandler.GetImmunizationByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.All(i => i.PatientId == _patientId && i.IsActive).Should().BeTrue();
        }

        [Test]
        public async Task GetImmunizationByIdAndIsActive_WithNoActiveImmunizations_ShouldReturnEmptyCollection()
        {
            // Arrange
            var immunizations = new List<Immunization>
            {
                new Immunization
                {
                    ImmunizationId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    Immunizations = "Influenza",
                    IsActive = false, // Inactive
                    Subscription = _subscription
                }
            };

            _immunizationRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(immunizations);

            // Act
            var result = await _immunizationQueryHandler.GetImmunizationByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
