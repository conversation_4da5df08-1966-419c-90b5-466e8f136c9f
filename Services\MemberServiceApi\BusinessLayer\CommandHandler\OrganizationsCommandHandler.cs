﻿using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class OrganizationsCommandHandler : IOrganizationsCommandHandler<Organization>
    {
        private readonly IUnitOfWork _unitOfWork;

        public OrganizationsCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddOrganizationAsync(List<Organization> organizations, Guid OrgId)
        {
            await _unitOfWork.OrganizationRepository.AddAsync(organizations);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateOrganizationAsync(Organization organization)
        {
            await _unitOfWork.OrganizationRepository.UpdateAsync(organization);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteOrganizationAsync(Guid id)
        {
            await _unitOfWork.OrganizationRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }
    }
}
