using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;
using EncounterNotesDataAccessLayer.EncounterNotesContext;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class HospitalizationRecordControllerTests
    {
        private Mock<IHospitalizationRecordCommandHandler<HospitalizationRecord>> _hospitalizationRecordCommandHandlerMock;
        private Mock<IHospitalizationRecordQueryHandler<HospitalizationRecord>> _hospitalizationRecordQueryHandlerMock;
        private Mock<ILogger<HospitalizationRecordController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;

        private HospitalizationRecordController _controller;
        private List<HospitalizationRecord> _testHospitalizationRecords;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _hospitalizationRecordCommandHandlerMock = new Mock<IHospitalizationRecordCommandHandler<HospitalizationRecord>>();
            _hospitalizationRecordQueryHandlerMock = new Mock<IHospitalizationRecordQueryHandler<HospitalizationRecord>>();
            _loggerMock = new Mock<ILogger<HospitalizationRecordController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();

            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["SuccessfulDeletion"]).Returns(new LocalizedString("SuccessfulDeletion", "Deletion successful."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting data."));
            _localizerMock.Setup(x => x["InvalidAddressData"]).Returns(new LocalizedString("InvalidAddressData", "Invalid address data."));
            _localizerMock.Setup(x => x["AddressInvalidMessage"]).Returns(new LocalizedString("AddressInvalidMessage", "Address data is invalid."));
            _localizerMock.Setup(x => x["AddingNewAddress"]).Returns(new LocalizedString("AddingNewAddress", "Adding new address."));
            _localizerMock.Setup(x => x["AddressAddedSuccessfully"]).Returns(new LocalizedString("AddressAddedSuccessfully", "Address added successfully."));
            _localizerMock.Setup(x => x["AddSuccessful"]).Returns(new LocalizedString("AddSuccessful", "Add successful."));
            _localizerMock.Setup(x => x["ErrorAddingAddress"]).Returns(new LocalizedString("ErrorAddingAddress", "Error adding address."));
            _localizerMock.Setup(x => x["InternalServerErrorMessage"]).Returns(new LocalizedString("InternalServerErrorMessage", "Internal server error."));
            _localizerMock.Setup(x => x["AccessError"]).Returns(new LocalizedString("AccessError", "Access error."));
            _localizerMock.Setup(x => x["Record not found"]).Returns(new LocalizedString("Record not found", "Record not found."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));

            // Create test data
            _testHospitalizationRecords = new List<HospitalizationRecord>
            {
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationID = _orgId,
                    Reason = "Pneumonia",
                    Date = DateTime.Now.AddMonths(-6),
                    IsActive = true,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddMonths(-6),
                    UpdatedDate = DateTime.Now.AddMonths(-5),
                    Subscription = _isSubscription
                },
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationID = _orgId,
                    Reason = "Appendectomy",
                    Date = DateTime.Now.AddYears(-2),
                    IsActive = true,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddYears(-2),
                    UpdatedDate = DateTime.Now.AddYears(-2),
                    Subscription = _isSubscription
                }
            };

            _controller = new HospitalizationRecordController(
                _hospitalizationRecordQueryHandlerMock.Object,
                _hospitalizationRecordCommandHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object,
                null
            );
        }

        [Test]
        public async Task GetById_ReturnsOk_WhenHospitalizationRecordsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var hospitalizationRecords = _testHospitalizationRecords.ToList();
            _hospitalizationRecordQueryHandlerMock.Setup(x => x.GetHospitalizationRecordById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(hospitalizationRecords);

            // Act
            var result = await _controller.GetById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().BeEquivalentTo(hospitalizationRecords);
        }

        [Test]
        public async Task GetById_ReturnsNotFound_WhenNoHospitalizationRecordsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _hospitalizationRecordQueryHandlerMock.Setup(x => x.GetHospitalizationRecordById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(new List<HospitalizationRecord>());

            // Act
            var result = await _controller.GetById(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task UpdateById_ReturnsOk_WhenUpdateSuccessful()
        {
            // Arrange
            var hospitalizationRecord = _testHospitalizationRecords[0];
            var id = hospitalizationRecord.RecordID;

            // Act
            var result = await _controller.UpdateById(id, hospitalizationRecord, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _hospitalizationRecordCommandHandlerMock.Verify(x => x.UpdateHospitalizationRecord(hospitalizationRecord, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateById_ReturnsBadRequest_WhenIdMismatch()
        {
            // Arrange
            var hospitalizationRecord = _testHospitalizationRecords[0];
            var wrongId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdateById(wrongId, hospitalizationRecord, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["InvalidRecord"]);

            // Verify the command handler was not called
            _hospitalizationRecordCommandHandlerMock.Verify(x => x.UpdateHospitalizationRecord(It.IsAny<HospitalizationRecord>(), _orgId, _isSubscription), Times.Never);
        }

        [Test]
        public async Task DeleteById_ReturnsOk_WhenDeleteSuccessful()
        {
            // Arrange
            var id = Guid.NewGuid();

            // Act
            var result = await _controller.DeleteById(id, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulDeletion"]);

            // Verify the command handler was called
            _hospitalizationRecordCommandHandlerMock.Verify(x => x.DeleteHospitalizationRecordById(id, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task AddAddress_ReturnsOk_WhenAddSuccessful()
        {
            // Arrange
            var hospitalizationRecords = _testHospitalizationRecords;

            // Act
            var result = await _controller.AddAddress(hospitalizationRecords, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["AddSuccessful"]);

            // Verify the command handler was called
            _hospitalizationRecordCommandHandlerMock.Verify(x => x.AddHospitalizationRecord(hospitalizationRecords, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task AddAddress_ReturnsBadRequest_WhenEmptyList()
        {
            // Arrange
            var emptyList = new List<HospitalizationRecord>();

            // Act
            var result = await _controller.AddAddress(emptyList, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["AddressInvalidMessage"]);

            // Verify the command handler was not called
            _hospitalizationRecordCommandHandlerMock.Verify(x => x.AddHospitalizationRecord(It.IsAny<List<HospitalizationRecord>>(), _orgId, _isSubscription), Times.Never);
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsOk_WhenActiveHospitalizationRecordsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activeHospitalizationRecords = _testHospitalizationRecords.Where(h => h.IsActive).ToList();
            _hospitalizationRecordQueryHandlerMock.Setup(x => x.GetHospitalizationRecordByIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activeHospitalizationRecords);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().BeEquivalentTo(activeHospitalizationRecords);
        }

        [Test]
        public async Task UpdateAccess_ReturnsOk_WhenUpdateSuccessful()
        {
            // Arrange
            var hospitalizationRecords = _testHospitalizationRecords;
            _hospitalizationRecordCommandHandlerMock.Setup(x => x.UpdateHospitalizationRecordList(hospitalizationRecords, _orgId, _isSubscription))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.UpdateAccess(hospitalizationRecords, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            // Verify the command handler was called
            _hospitalizationRecordCommandHandlerMock.Verify(x => x.UpdateHospitalizationRecordList(hospitalizationRecords, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateAccess_ReturnsNotFound_WhenUpdateFails()
        {
            // Arrange
            var hospitalizationRecords = _testHospitalizationRecords;
            _hospitalizationRecordCommandHandlerMock.Setup(x => x.UpdateHospitalizationRecordList(hospitalizationRecords, _orgId, _isSubscription))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.UpdateAccess(hospitalizationRecords, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["Record not found"]);

            // Verify the command handler was called
            _hospitalizationRecordCommandHandlerMock.Verify(x => x.UpdateHospitalizationRecordList(hospitalizationRecords, _orgId, _isSubscription), Times.Once);
        }


    }
}
