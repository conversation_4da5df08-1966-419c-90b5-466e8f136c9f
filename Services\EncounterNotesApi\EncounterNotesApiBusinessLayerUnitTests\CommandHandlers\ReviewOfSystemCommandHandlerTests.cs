using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class ReviewOfSystemCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<ReviewOfSystemCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, ReviewOfSystem, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IReviewOfSystemRepository> _reviewOfSystemRepositoryMock;
        private ReviewOfSystemCommandHandler _reviewOfSystemCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<ReviewOfSystemCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, ReviewOfSystem, EncounterDataAccessLayer>>();
            _reviewOfSystemRepositoryMock = new Mock<IReviewOfSystemRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;

            _unitOfWorkMock.Setup(u => u.ReviewOfSystemRepository).Returns(_reviewOfSystemRepositoryMock.Object);
            _reviewOfSystemCommandHandler = new ReviewOfSystemCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object);
        }

        [Test]
        public async Task AddReviewOfSystem_ShouldAddReviewOfSystemsToRepository()
        {
            // Arrange
            var reviewOfSystems = new List<ReviewOfSystem>
            {
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Decription = "Comprehensive review of systems",
                    IsActive = true,
                    RunnyNose = true,
                    Congestion = true,
                    ShortnessOfBreath = false,
                    ChestPain = false,
                    ItchyEyes = true,
                    Subscription = _subscription
                },
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Decription = "Follow-up review of systems",
                    IsActive = true,
                    RunnyNose = false,
                    Congestion = false,
                    ShortnessOfBreath = true,
                    ChestPain = true,
                    ItchyEyes = false,
                    Subscription = _subscription
                }
            };

            _reviewOfSystemRepositoryMock.Setup(r => r.AddAsync(It.IsAny<List<ReviewOfSystem>>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _reviewOfSystemCommandHandler.AddReviewOfSystem(reviewOfSystems, _orgId, _subscription);

            // Assert
            _reviewOfSystemRepositoryMock.Verify(r => r.AddAsync(reviewOfSystems, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateReviewOfSystem_ShouldUpdateReviewOfSystemInRepository()
        {
            // Arrange
            var reviewOfSystem = new ReviewOfSystem
            {
                ReviewOfSystemId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PcpId = Guid.NewGuid(),
                Decription = "Updated review of systems",
                IsActive = true,
                RunnyNose = true,
                Congestion = false,
                ShortnessOfBreath = true,
                ChestPain = false,
                ItchyEyes = true,
                Subscription = _subscription
            };

            _reviewOfSystemRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<ReviewOfSystem>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _reviewOfSystemCommandHandler.UpdateReviewOfSystem(reviewOfSystem, _orgId, _subscription);

            // Assert
            _reviewOfSystemRepositoryMock.Verify(r => r.UpdateAsync(reviewOfSystem, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateReviewOfSystemList_ShouldUpdateReviewOfSystemsInRepository()
        {
            // Arrange
            var reviewOfSystems = new List<ReviewOfSystem>
            {
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Decription = "Updated review 1",
                    IsActive = true,
                    Subscription = _subscription
                },
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Decription = "Updated review 2",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _reviewOfSystemRepositoryMock.Setup(r => r.UpdateRangeAsync(It.IsAny<List<ReviewOfSystem>>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _reviewOfSystemCommandHandler.UpdateReviewOfSystemList(reviewOfSystems, _orgId, _subscription);

            // Assert
            _reviewOfSystemRepositoryMock.Verify(r => r.UpdateRangeAsync(reviewOfSystems, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteReviewOfSystemById_ShouldDeleteReviewOfSystemFromRepository()
        {
            // Arrange
            var reviewOfSystemId = Guid.NewGuid();

            _reviewOfSystemRepositoryMock.Setup(r => r.DeleteByIdAsync(reviewOfSystemId, _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _reviewOfSystemCommandHandler.DeleteReviewOfSystemById(reviewOfSystemId, _orgId, _subscription);

            // Assert
            _reviewOfSystemRepositoryMock.Verify(r => r.DeleteByIdAsync(reviewOfSystemId, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteReviewOfSystemByEntity_ShouldDeleteReviewOfSystemFromRepository()
        {
            // Arrange
            var reviewOfSystem = new ReviewOfSystem
            {
                ReviewOfSystemId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PcpId = Guid.NewGuid(),
                Decription = "To be deleted",
                IsActive = false,
                Subscription = _subscription
            };

            _reviewOfSystemRepositoryMock.Setup(r => r.DeleteByEntityAsync(reviewOfSystem, _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _reviewOfSystemCommandHandler.DeleteReviewOfSystemByEntity(reviewOfSystem, _orgId, _subscription);

            // Assert
            _reviewOfSystemRepositoryMock.Verify(r => r.DeleteByEntityAsync(reviewOfSystem, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }




    }
}

