﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using System;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using EncounterNotesService.EncounterNotesServiceResources;

namespace EncounterNotesService.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BillingController : ControllerBase
    {
        private readonly IBillingQueryHandler<Billing> _billingQueryHandler;
        private readonly IBillingCommandHandler<Billing> _billingCommandHandler;
        private readonly ILogger<BillingController> _logger;
        private readonly IStringLocalizer<BillingController> _localizer;

        public BillingController(
            IBillingQueryHandler<Billing> billingQuery<PERSON>and<PERSON>,
            IBillingCommandHandler<Billing> billing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
            ILogger<BillingController> logger,
            IStringLocalizer<BillingController> localizer)
        {
            _billingQueryHandler = billingQueryHandler ?? throw new ArgumentNullException(nameof(billingQueryHandler));
            _billingCommandHandler = billingCommandHandler ?? throw new ArgumentNullException(nameof(billingCommandHandler));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBillingById(Guid id)
        {
            IActionResult response;
            try
            {
                _logger.LogInformation(_localizer["FetchingBillingWithID"], id);
                var billing = await _billingQueryHandler.GetBillingByIdAsync(id);
                if (billing == null)
                {
                    _logger.LogWarning(_localizer["BillingNotFound"], id);
                    response = NotFound(_localizer["BillingNotFoundMessage"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["BillingFetchedSuccessfully"], id);
                    response = Ok(billing);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingBilling"], id);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }

        [HttpGet("patient/{patientId}")]
        public async Task<IActionResult> GetBillingByPatientId(Guid patientId)
        {
            IActionResult response;
            try
            {
                _logger.LogInformation(_localizer["FetchingBillingWithPatientID"], patientId);
                var billings = await _billingQueryHandler.GetBillingByPatientIdAsync(patientId);
                if (billings == null || billings.Count == 0)
                {
                    _logger.LogWarning(_localizer["BillingNotFound"], patientId);
                    response = NotFound(_localizer["BillingNotFoundMessage"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["BillingFetchedSuccessfully"], patientId);
                    response = Ok(billings);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingBilling"], patientId);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }

        [HttpPost]
        public async Task<IActionResult> AddBilling([FromBody] Billing billing)
        {
            IActionResult response;
            if (billing == null)
            {
                _logger.LogWarning(_localizer["InvalidBillingData"]);
                response = BadRequest(_localizer["BillingInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["AddingNewBilling"]);
                    await _billingCommandHandler.AddBillingAsync(billing);
                    _logger.LogInformation(_localizer["BillingAddedSuccessfully"], billing.BillingId);
                    response = CreatedAtAction(nameof(GetBillingById), new { id = billing.BillingId }, billing);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorAddingBilling"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateBilling(Guid id, [FromBody] Billing billing)
        {
            IActionResult response;
            if (billing == null || id != billing.BillingId)
            {
                _logger.LogWarning(_localizer["InvalidDataForUpdate"], id);
                response = BadRequest(_localizer["BillingInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["UpdatingBillingWithID"], id);
                    await _billingCommandHandler.UpdateBillingAsync(billing);
                    _logger.LogInformation(_localizer["BillingUpdatedSuccessfully"], id);
                    response = NoContent();
                }
                catch (KeyNotFoundException)
                {
                    _logger.LogWarning(_localizer["BillingNotFoundForUpdate"], id);
                    response = NotFound(_localizer["BillingNotFoundMessage"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorUpdatingBilling"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteBilling(Guid id)
        {
            IActionResult response;
            try
            {
                _logger.LogInformation(_localizer["DeletingBillingWithID"], id);
                await _billingCommandHandler.DeleteBillingAsync(id);
                _logger.LogInformation(_localizer["BillingDeletedSuccessfully"], id);
                response = NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning(_localizer["BillingNotFoundForDeletion"], id);
                response = NotFound(_localizer["BillingNotFoundMessage"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingBilling"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllBillings()
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingAllBillings"]);
            try
            {
                var billings = await _billingQueryHandler.GetAllBillingsAsync();
                if (billings == null || billings.Count == 0)
                {
                    _logger.LogWarning(_localizer["NoBillingsFound"]);
                    response = NotFound(_localizer["BillingsNotFoundMessage"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["AllBillingsFetchedSuccessfully"]);
                    response = Ok(billings);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingBillings"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }
    }
}
