using NUnit.Framework;
using Moq;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class SocialHistoryQueryHandlerTests
    {
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ISocialHistoryRepository> _socialHistoryRepositoryMock;
        private SocialHistoryQueryHandler _socialHistoryQueryHandler;
        private Guid _orgId;
        private bool _subscription;
        private Guid _patientId;

        [SetUp]
        public void Setup()
        {
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _socialHistoryRepositoryMock = new Mock<ISocialHistoryRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;
            _patientId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.SocialHistoryRepository).Returns(_socialHistoryRepositoryMock.Object);
            _socialHistoryQueryHandler = new SocialHistoryQueryHandler(_unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetHistoryByIdAndIsActive_ShouldReturnActiveSocialHistoriesForPatient()
        {
            // Arrange
            var socialHistories = new List<PatientSocialHistory>
            {
                new PatientSocialHistory
                {
                    SocialHistoryId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Occupation = "Engineer",
                    LivingSituation = "Lives with spouse",
                    MaritalStatus = "Married",
                    LifestyleHabits = "Non-smoker, occasional alcohol",
                    Educationlevel = "Bachelor's degree",
                    isActive = true,
                    Subscription = _subscription
                },
                new PatientSocialHistory
                {
                    SocialHistoryId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdatedDate = DateTime.Now.AddDays(-55),
                    Occupation = "Teacher",
                    LivingSituation = "Lives alone",
                    MaritalStatus = "Single",
                    LifestyleHabits = "Non-smoker, no alcohol",
                    Educationlevel = "Master's degree",
                    isActive = false, // Inactive
                    Subscription = _subscription
                },
                new PatientSocialHistory
                {
                    SocialHistoryId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-45),
                    UpdatedDate = DateTime.Now.AddDays(-40),
                    Occupation = "Doctor",
                    LivingSituation = "Lives with family",
                    MaritalStatus = "Married",
                    LifestyleHabits = "Non-smoker, social drinker",
                    Educationlevel = "Doctorate",
                    isActive = true,
                    Subscription = _subscription
                }
            };

            _socialHistoryRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(socialHistories);

            // Act
            var result = await _socialHistoryQueryHandler.GetHistoryByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.All(h => h.PatientId == _patientId && h.isActive).Should().BeTrue();
        }

        [Test]
        public async Task GetHistoryByIdAndIsActive_WithNoActiveSocialHistories_ShouldReturnEmptyCollection()
        {
            // Arrange
            var socialHistories = new List<PatientSocialHistory>
            {
                new PatientSocialHistory
                {
                    SocialHistoryId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Occupation = "Teacher",
                    LivingSituation = "Lives alone",
                    MaritalStatus = "Single",
                    isActive = false, // Inactive
                    Subscription = _subscription
                }
            };

            _socialHistoryRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(socialHistories);

            // Act
            var result = await _socialHistoryQueryHandler.GetHistoryByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

    }
}
