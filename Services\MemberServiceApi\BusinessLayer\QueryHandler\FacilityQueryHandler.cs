﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using MemberServiceDataAccessLayer.Implementation;
using System.Security.Cryptography;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class FacilityQueryHandler : IFacilityQueryHandler<Facility>
    {
        private readonly IUnitOfWork _unitOfWork;

        public FacilityQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<Facility> GetFacilityByIdAsync(bool Subscription, Guid id, Guid OrgID)
        {
            var facility = await _unitOfWork.FacilityRepository.GetByIdAsync(id, OrgID,Subscription);
            return facility;
        }

        public async Task<IEnumerable<Facility>> GetFacilitiesByNameAsync(bool Subscription, string name, Guid OrgID)
        {
            return await _unitOfWork.FacilityRepository.GetFacilitiesByNameAsync(name, OrgID, Subscription);
        }

        public async Task<List<Facility>> GetAllFacilitiesAsync(Guid orgId, bool Subscription)
        {
            var facilities = await _unitOfWork.FacilityRepository.GetAllAsync(orgId, Subscription);
            return facilities.ToList();
        }
        public async Task<IEnumerable<Facility>> GetFacilitiesByOrgAsync(bool subscription, Guid orgId)
        {
            return await _unitOfWork.FacilityRepository.GetByOrgIdAsync(orgId, subscription);
        }
    }
}
