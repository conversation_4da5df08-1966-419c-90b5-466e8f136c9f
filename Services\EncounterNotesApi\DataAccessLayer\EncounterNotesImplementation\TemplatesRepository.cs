﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Interfaces.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class TemplatesRepository : ShardGenericRepository<Templates>, ITemplatesRepository
    {
        private readonly ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> _shardMapManagerService;
        private readonly IStringLocalizer<EncounterDataAccessLayer> _localizer;
        private readonly ILogger<RecordDatabaseContext> _logger;
        private readonly string _shardMapName;
        public TemplatesRepository(
            RecordDatabaseContext context,
            ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> shardMapManagerService,
            IStringLocalizer<EncounterDataAccessLayer> localizer,
            ILogger<RecordDatabaseContext> logger)
            : base(context, shardMapManagerService, localizer, logger)
        {
            _shardMapManagerService = shardMapManagerService;
            _localizer = localizer;
            _logger = logger;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }

        public async Task<List<Templates>> GetTemplatesByPCPId(Guid PCPId, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return new List<Templates>();
            var templatedata = await context.Templates
            .Where(t => t.PCPId == PCPId)
            .ToListAsync();
            return templatedata;
        }

        public async Task<List<Templates>> GetTemplatesByPCPIdAndVisitType(Guid PCPId,String VisitType, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return new List<Templates>();
            var templatedata = await context.Templates
            .Where(t => t.PCPId == PCPId && t.VisitType==VisitType)
            .ToListAsync();
            return templatedata;
        }

    }
}