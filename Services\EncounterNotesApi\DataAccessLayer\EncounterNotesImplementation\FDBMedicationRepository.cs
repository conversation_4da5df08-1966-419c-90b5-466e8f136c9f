﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class FDBMedicationRepository : GenericRepository<FDBMedication>, IFDBMedicationRepository
    {
        private readonly RecordDatabaseContext _context;

        public FDBMedicationRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
        }
        public async Task<List<FDBMedication>> GetMedicationByROUTED_DOSAGE_FORM_MED_ID(string ROUTED_DOSAGE_FORM_MED_ID)
        {
            var medication = await _context.FDBMedications
            .Where(t => t.ROUTED_DOSAGE_FORM_MED_ID == ROUTED_DOSAGE_FORM_MED_ID)
            .ToListAsync();
            return medication;
        }
    }
}
