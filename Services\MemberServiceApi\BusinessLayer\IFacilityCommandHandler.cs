﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IFacilityCommandHandler<T>
    {
        Task AddFacilityAsync(List<T> facilities, bool Subscription, Guid orgId);
        Task UpdateFacilityAsync(T facility, bool Subscription, Guid orgId);
        Task DeleteFacilityAsync( bool Subscription,Guid id, Guid orgId);
    }
}
