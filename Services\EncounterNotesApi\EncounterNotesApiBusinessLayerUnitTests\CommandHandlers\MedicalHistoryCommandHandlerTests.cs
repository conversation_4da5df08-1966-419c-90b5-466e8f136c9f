using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class MedicalHistoryCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<MedicalHistoryCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, MedicalHistory, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IMedicalHistoryRepository> _medicalHistoryRepositoryMock;
        private MedicalHistoryCommandHandler _medicalHistoryCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<MedicalHistoryCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, MedicalHistory, EncounterDataAccessLayer>>();
            _medicalHistoryRepositoryMock = new Mock<IMedicalHistoryRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;

            _unitOfWorkMock.Setup(u => u.MedicalHistoryRepository).Returns(_medicalHistoryRepositoryMock.Object);
            _medicalHistoryCommandHandler = new MedicalHistoryCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object);
        }

        [Test]
        public async Task AddMedicalHistory_ShouldAddMedicalHistoriesToRepository()
        {
            // Arrange
            var medicalHistories = new List<MedicalHistory>
            {
                new MedicalHistory
                {
                    MedicalHistoryID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    History = "Hypertension diagnosed 5 years ago, well-controlled with medication",
                    IsActive = true,
                    Subscription = _subscription
                },
                new MedicalHistory
                {
                    MedicalHistoryID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdatedDate = DateTime.Now.AddDays(-55),
                    History = "Type 2 diabetes diagnosed 3 years ago, managed with diet and exercise",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _medicalHistoryRepositoryMock.Setup(r => r.AddAsync(It.IsAny<List<MedicalHistory>>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _medicalHistoryCommandHandler.AddMedicalHistory(medicalHistories, _orgId, _subscription);

            // Assert
            _medicalHistoryRepositoryMock.Verify(r => r.AddAsync(medicalHistories, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateMedicalHistory_ShouldUpdateMedicalHistoryInRepository()
        {
            // Arrange
            var medicalHistory = new MedicalHistory
            {
                MedicalHistoryID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-30),
                UpdatedDate = DateTime.Now,
                History = "Hypertension diagnosed 5 years ago, now controlled with increased medication dosage",
                IsActive = true,
                Subscription = _subscription
            };

            _medicalHistoryRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<MedicalHistory>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _medicalHistoryCommandHandler.UpdateMedicalHistory(medicalHistory, _orgId, _subscription);

            // Assert
            _medicalHistoryRepositoryMock.Verify(r => r.UpdateAsync(medicalHistory, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateMedicalHistoryList_ShouldUpdateMedicalHistoriesInRepository()
        {
            // Arrange
            var medicalHistories = new List<MedicalHistory>
            {
                new MedicalHistory
                {
                    MedicalHistoryID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    History = "Hypertension diagnosed 5 years ago",
                    IsActive = true,
                    Subscription = _subscription
                },
                new MedicalHistory
                {
                    MedicalHistoryID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    History = "Type 2 diabetes diagnosed 3 years ago",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _medicalHistoryRepositoryMock.Setup(r => r.UpdateRangeAsync(It.IsAny<List<MedicalHistory>>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _medicalHistoryCommandHandler.UpdateMedicalHistoryList(medicalHistories, _orgId, _subscription);

            // Assert
            _medicalHistoryRepositoryMock.Verify(r => r.UpdateRangeAsync(medicalHistories, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteMedicalHistoryById_ShouldDeleteMedicalHistoryFromRepository()
        {
            // Arrange
            var medicalHistoryId = Guid.NewGuid();

            _medicalHistoryRepositoryMock.Setup(r => r.DeleteByIdAsync(medicalHistoryId, _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _medicalHistoryCommandHandler.DeleteMedicalHistoryById(medicalHistoryId, _orgId, _subscription);

            // Assert
            _medicalHistoryRepositoryMock.Verify(r => r.DeleteByIdAsync(medicalHistoryId, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteMedicalHistoryByEntity_ShouldDeleteMedicalHistoryFromRepository()
        {
            // Arrange
            var medicalHistory = new MedicalHistory
            {
                MedicalHistoryID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                History = "To be deleted",
                IsActive = false,
                Subscription = _subscription
            };

            _medicalHistoryRepositoryMock.Setup(r => r.DeleteByEntityAsync(medicalHistory, _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _medicalHistoryCommandHandler.DeleteMedicalHistoryByEntity(medicalHistory, _orgId, _subscription);

            // Assert
            _medicalHistoryRepositoryMock.Verify(r => r.DeleteByEntityAsync(medicalHistory, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}
