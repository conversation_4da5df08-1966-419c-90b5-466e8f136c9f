﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IFDBMedicationRepository : IGenericRepository<FDBMedication>
    {
        Task<List<FDBMedication>> GetMedicationByROUTED_DOSAGE_FORM_MED_ID(string ROUTED_DOSAGE_FORM_MED_ID);
    }
}
