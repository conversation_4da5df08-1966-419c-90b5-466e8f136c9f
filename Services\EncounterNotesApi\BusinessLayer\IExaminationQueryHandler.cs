﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IExaminationQueryHandler
    {
        Task<IEnumerable<Examination>> GetExaminationByPatientIdAndIsActive(Guid PatientId, Guid OrgId, bool Subscription);
        Task<IEnumerable<Examination>> GetExaminationByPatientId(Guid PatientId, Guid OrgId, bool Subscription);
    }
}