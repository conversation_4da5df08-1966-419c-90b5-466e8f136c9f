using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class ReviewOfSystemControllerTests
    {
        private Mock<IReviewOfSystemCommandHandler<ReviewOfSystem>> _reviewCommandHandlerMock;
        private Mock<IReviewOfSystemQueryHandler<ReviewOfSystem>> _reviewQueryHandlerMock;
        private Mock<ILogger<ReviewOfSystemController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private ReviewOfSystemController _controller;
        private List<ReviewOfSystem> _testReviews;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _reviewCommandHandlerMock = new Mock<IReviewOfSystemCommandHandler<ReviewOfSystem>>();
            _reviewQueryHandlerMock = new Mock<IReviewOfSystemQueryHandler<ReviewOfSystem>>();
            _loggerMock = new Mock<ILogger<ReviewOfSystemController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No license available."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error occurred."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error posting data."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting data."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Delete successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testReviews = new List<ReviewOfSystem>
            {
                new ReviewOfSystem {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Decription = "Regular checkup",
                    IsActive = true,
                    RunnyNose = true,
                    Congestion = true,
                    ShortnessOfBreath = false,
                    ChestPain = false,
                    ItchyEyes = true,
                    Subscription = _isSubscription
                },
                new ReviewOfSystem {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-15),
                    Decription = "Follow-up visit",
                    IsActive = true,
                    RunnyNose = false,
                    Congestion = false,
                    ShortnessOfBreath = true,
                    ChestPain = true,
                    ItchyEyes = false,
                    Subscription = _isSubscription
                }
            };

            _controller = new ReviewOfSystemController(
                _reviewCommandHandlerMock.Object,
                _reviewQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetAllById_ReturnsReviews_WhenReviewsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientReviews = _testReviews.Select(r => { r.PatientId = patientId; return r; }).ToList();
            _reviewQueryHandlerMock.Setup(x => x.GetAllReviewOfSystemsById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientReviews);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedReviews = okResult.Value as IEnumerable<ReviewOfSystem>;
            returnedReviews.Should().NotBeNull();
            returnedReviews.Should().BeEquivalentTo(patientReviews);
        }

        [Test]
        public async Task GetAllById_ReturnsNotFound_WhenNoReviewsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _reviewQueryHandlerMock.Setup(x => x.GetAllReviewOfSystemsById(patientId, _orgId, _isSubscription))
                .ReturnsAsync((IEnumerable<ReviewOfSystem>)null);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task AddReviewOfSystem_ValidReviews_ReturnsOk()
        {
            // Arrange
            var reviews = _testReviews;

            // Act
            var result = await _controller.AddReviewOfSystem(reviews, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _reviewCommandHandlerMock.Verify(x => x.AddReviewOfSystem(reviews, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task AddReviewOfSystem_EmptyList_ReturnsBadRequest()
        {
            // Arrange
            var emptyReviews = new List<ReviewOfSystem>();

            // Act
            var result = await _controller.AddReviewOfSystem(emptyReviews, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);
        }

        [Test]
        public async Task UpdateReviewOfSystemById_ValidReview_ReturnsOk()
        {
            // Arrange
            var review = _testReviews[0];

            // Act
            var result = await _controller.UpdateReviewOfSystemById(review.PatientId, review, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _reviewCommandHandlerMock.Verify(x => x.UpdateReviewOfSystem(review, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task DeleteByEntity_ValidReview_ReturnsOk()
        {
            // Arrange
            var review = _testReviews[0];

            // Act
            var result = await _controller.DeleteByEntity(review, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _reviewCommandHandlerMock.Verify(x => x.DeleteReviewOfSystemByEntity(review, _orgId, _isSubscription), Times.Once);
        }
    }
}
