﻿using System;
using System.Threading.Tasks;
using Contracts;

namespace MemberServiceBusinessLayer
{
    public interface IVisitTypeCommandHandler<T>
    {
        Task<bool> UpdateCptCodeAsync(Guid orgId, string visitName, string newCptCode, bool Subscription);
        Task AddVisitTypeAsync(VisitType visitType, Guid orgId, bool Subscription);
        Task<bool> DeleteVisitTypeAsync(Guid orgId, string visitName, bool Subscription);
    }
}
