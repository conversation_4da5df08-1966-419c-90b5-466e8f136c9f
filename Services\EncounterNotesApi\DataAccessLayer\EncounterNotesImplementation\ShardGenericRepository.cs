﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DotNetEnv;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Interfaces.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class ShardGenericRepository<T> : IShardGenericRepository<T>
        where T : class
    {
        private readonly ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> _shardMapManagerService;
        private readonly IStringLocalizer<EncounterDataAccessLayer> _localizer;
        private readonly ILogger<RecordDatabaseContext> _logger;
        private readonly RecordDatabaseContext _context;
        private readonly DbSet<T> _dbSet;
        private readonly string _shardMapName;
        const int two = 2, one = 1, zero = 0;
      
        public ShardGenericRepository(RecordDatabaseContext context, ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> shardMapManagerService, IStringLocalizer<EncounterDataAccessLayer> localizer, ILogger<RecordDatabaseContext> logger)
        {
            Env.Load();
            _shardMapManagerService = shardMapManagerService ?? throw new ArgumentNullException(nameof(shardMapManagerService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _context = context;
            _dbSet = context.Set<T>();
            _localizer = localizer;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");

        }

        // Add
        public async Task AddAsync(IEnumerable<T> entities, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            await context.Set<T>().AddRangeAsync(entities);
            await context.SaveChangesAsync();
        }  

        // Get All (Ordered)
        public async Task<IEnumerable<T>> GetAllAsync(Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return Enumerable.Empty<T>();

            var records = await context.Set<T>().ToListAsync();
            return records.OrderByDescending(record =>
                (DateTime)typeof(T).GetProperty(_localizer["DateTime"]).GetValue(record)).ToList();
        }

        // Get (Simple List)
        public async Task<IEnumerable<T>> GetAsync(Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return Enumerable.Empty<T>();
            return await context.Set<T>().ToListAsync();
        }
    
        // Get by ID
        public async Task<T> GetByIdAsync(Guid id, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return null;
            return await context.Set<T>().FindAsync(id);
        }

        // Update single entity
        public async Task UpdateAsync(T entity, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            context.Set<T>().Update(entity);
            await context.SaveChangesAsync();
        }

        // Update range
        public async Task UpdateRangeAsync(List<T> entities, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            context.Set<T>().UpdateRange(entities);
            await context.SaveChangesAsync();
        }
       
        // Delete by entity
        public async Task DeleteByEntityAsync(T entity, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            context.Set<T>().Remove(entity);
            await context.SaveChangesAsync();
        }
       
        // Delete by ID
        public async Task DeleteByIdAsync(Guid id, Guid OrgId, bool Subscription)
        {
            var entity = await GetByIdAsync(id, OrgId, Subscription);
            if (entity != null)
            {
                await DeleteByEntityAsync(entity, OrgId, Subscription);
            }
        }
    }
}
