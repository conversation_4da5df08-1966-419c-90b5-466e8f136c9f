using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class AllergyQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IAllergyRepository> _allergyRepositoryMock;
        private AllergyQueryHandler _allergyQueryHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _allergyRepositoryMock = new Mock<IAllergyRepository>();

            _unitOfWorkMock.Setup(u => u.AllergyRepository).Returns(_allergyRepositoryMock.Object);

            _allergyQueryHandler = new AllergyQueryHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task GetAllergyByIdAndIsActive_ShouldReturnActiveAllergiesForPatient()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var allergies = new List<Allergy>
            {
                new Allergy
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = patientId,
                    AllergyInfo = "Peanut Allergy",
                    Classification = "Food",
                    Agent = "Peanuts",
                    Reaction = "Anaphylaxis",
                    isActive = true
                },
                new Allergy
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = patientId,
                    AllergyInfo = "Penicillin Allergy",
                    Classification = "Drug",
                    Agent = "Penicillin",
                    Reaction = "Rash",
                    isActive = true
                },
                new Allergy
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = patientId,
                    AllergyInfo = "Inactive Allergy",
                    Classification = "Other",
                    Agent = "Unknown",
                    Reaction = "None",
                    isActive = false
                },
                new Allergy
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    AllergyInfo = "Other Patient Allergy",
                    Classification = "Food",
                    Agent = "Shellfish",
                    Reaction = "Hives",
                    isActive = true
                }
            };

            _allergyRepositoryMock.Setup(r => r.GetAllAllergyAsync(_orgId, _subscription))
                .ReturnsAsync(allergies);

            // Act
            var result = await _allergyQueryHandler.GetAllergyByIdAndIsActive(patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(a => a.PatientId == patientId && a.isActive).Should().BeTrue();
            result.Should().Contain(a => a.AllergyInfo == "Peanut Allergy");
            result.Should().Contain(a => a.AllergyInfo == "Penicillin Allergy");
            result.Should().NotContain(a => a.AllergyInfo == "Inactive Allergy");
            result.Should().NotContain(a => a.AllergyInfo == "Other Patient Allergy");
        }

        [Test]
        public async Task GetAllAllergyById_ShouldReturnAllAllergiesForPatient()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var allergies = new List<Allergy>
            {
                new Allergy
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = patientId,
                    AllergyInfo = "Peanut Allergy",
                    Classification = "Food",
                    Agent = "Peanuts",
                    Reaction = "Anaphylaxis",
                    isActive = true
                },
                new Allergy
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = patientId,
                    AllergyInfo = "Penicillin Allergy",
                    Classification = "Drug",
                    Agent = "Penicillin",
                    Reaction = "Rash",
                    isActive = true
                },
                new Allergy
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = patientId,
                    AllergyInfo = "Inactive Allergy",
                    Classification = "Other",
                    Agent = "Unknown",
                    Reaction = "None",
                    isActive = false
                },
                new Allergy
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    AllergyInfo = "Other Patient Allergy",
                    Classification = "Food",
                    Agent = "Shellfish",
                    Reaction = "Hives",
                    isActive = true
                }
            };

            _allergyRepositoryMock.Setup(r => r.GetAllAllergyAsync(_orgId, _subscription))
                .ReturnsAsync(allergies);

            // Act
            var result = await _allergyQueryHandler.GetAllAllergyById(patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(3);
            result.All(a => a.PatientId == patientId).Should().BeTrue();
            result.Should().Contain(a => a.AllergyInfo == "Peanut Allergy");
            result.Should().Contain(a => a.AllergyInfo == "Penicillin Allergy");
            result.Should().Contain(a => a.AllergyInfo == "Inactive Allergy");
            result.Should().NotContain(a => a.AllergyInfo == "Other Patient Allergy");
        }

        [Test]
        public async Task GetAllergyByIdAndIsActive_WithNoAllergies_ShouldReturnEmptyCollection()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var allergies = new List<Allergy>();

            _allergyRepositoryMock.Setup(r => r.GetAllAllergyAsync(_orgId, _subscription))
                .ReturnsAsync(allergies);

            // Act
            var result = await _allergyQueryHandler.GetAllergyByIdAndIsActive(patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetAllAllergyById_WithNoAllergies_ShouldReturnEmptyCollection()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var allergies = new List<Allergy>();

            _allergyRepositoryMock.Setup(r => r.GetAllAllergyAsync(_orgId, _subscription))
                .ReturnsAsync(allergies);

            // Act
            var result = await _allergyQueryHandler.GetAllAllergyById(patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
