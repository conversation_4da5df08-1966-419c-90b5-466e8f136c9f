using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class TherapeuticInterventionsListQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ITherapeuticInterventionsListRepository> _therapeuticInterventionsListRepositoryMock;
        private TherapeuticInterventionsListQueryHandler _therapeuticInterventionsListQueryHandler;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _therapeuticInterventionsListRepositoryMock = new Mock<ITherapeuticInterventionsListRepository>();

            _unitOfWorkMock.Setup(u => u.TherapeuticInterventionsListRepository).Returns(_therapeuticInterventionsListRepositoryMock.Object);
            _therapeuticInterventionsListQueryHandler = new TherapeuticInterventionsListQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetTherapeuticInterventionsList_ShouldReturnAllInterventions()
        {
            // Arrange
            var interventions = new List<TherapeuticInterventionsList>
            {
                new TherapeuticInterventionsList
                {
                    ID = Guid.NewGuid(),
                    TherapyType = "Physical Therapy",
                    Description = "Therapeutic exercise"
                },
                new TherapeuticInterventionsList
                {
                    ID = Guid.NewGuid(),
                    TherapyType = "Physical Therapy",
                    Description = "Manual therapy techniques"
                },
                new TherapeuticInterventionsList
                {
                    ID = Guid.NewGuid(),
                    TherapyType = "Occupational Therapy",
                    Description = "Activities of daily living training"
                },
                new TherapeuticInterventionsList
                {
                    ID = Guid.NewGuid(),
                    TherapyType = "Speech Therapy",
                    Description = "Speech and language exercises"
                }
            };

            _therapeuticInterventionsListRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(interventions);

            // Act
            var result = await _therapeuticInterventionsListQueryHandler.GetTherapeuticInterventionsList();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(4);
            result.Should().BeEquivalentTo(interventions);
        }

        [Test]
        public async Task GetTherapeuticInterventionsList_WithNoInterventions_ShouldReturnEmptyCollection()
        {
            // Arrange
            var interventions = new List<TherapeuticInterventionsList>();

            _therapeuticInterventionsListRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(interventions);

            // Act
            var result = await _therapeuticInterventionsListQueryHandler.GetTherapeuticInterventionsList();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetTherapeuticInterventionsList_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            _therapeuticInterventionsListRepositoryMock.Setup(r => r.GetAsync())
                .ThrowsAsync(new Exception("Database connection error"));

            // Act & Assert
            await FluentActions.Invoking(async () => await _therapeuticInterventionsListQueryHandler.GetTherapeuticInterventionsList())
                .Should().ThrowAsync<Exception>()
                .WithMessage("Database connection error");
        }
    }
}
