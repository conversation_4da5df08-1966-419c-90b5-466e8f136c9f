using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class VitalCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<VitalCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, Vitals, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IVitalRepository> _vitalRepositoryMock;
        private VitalCommandHandler _vitalCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<VitalCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, Vitals, EncounterDataAccessLayer>>();
            _vitalRepositoryMock = new Mock<IVitalRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;

            _unitOfWorkMock.Setup(u => u.VitalRepository).Returns(_vitalRepositoryMock.Object);
            _vitalCommandHandler = new VitalCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object);
        }

        [Test]
        public async Task AddVital_ShouldAddVitalsToRepository()
        {
            // Arrange
            var vitals = new List<Vitals>
            {
                new Vitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Temperature = "98.6",
                    BP = "120/80",
                    Weight = "70 kg",
                    Height = "175 cm",
                    Pulse = "72",
                    isActive = true,
                    Subscription = _subscription
                },
                new Vitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdatedDate = DateTime.Now.AddDays(-55),
                    Temperature = "99.1",
                    BP = "125/85",
                    Weight = "71 kg",
                    Height = "175 cm",
                    Pulse = "75",
                    isActive = true,
                    Subscription = _subscription
                }
            };

            _vitalRepositoryMock.Setup(r => r.AddAsync(It.IsAny<List<Vitals>>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _vitalCommandHandler.AddVital(vitals, _orgId, _subscription);

            // Assert
            _vitalRepositoryMock.Verify(r => r.AddAsync(vitals, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateVital_ShouldUpdateVitalInRepository()
        {
            // Arrange
            var vital = new Vitals
            {
                VitalId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-30),
                UpdatedDate = DateTime.Now,
                Temperature = "98.6",
                BP = "118/78", // Updated BP
                Weight = "69 kg", // Updated weight
                Height = "175 cm",
                Pulse = "70", // Updated pulse
                isActive = true,
                Subscription = _subscription
            };

            _vitalRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Vitals>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _vitalCommandHandler.UpdateVital(vital, _orgId, _subscription);

            // Assert
            _vitalRepositoryMock.Verify(r => r.UpdateAsync(vital, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateVitalList_ShouldUpdateVitalsInRepository()
        {
            // Arrange
            var vitals = new List<Vitals>
            {
                new Vitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Temperature = "98.6",
                    BP = "120/80",
                    isActive = true,
                    Subscription = _subscription
                },
                new Vitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Temperature = "99.1",
                    BP = "125/85",
                    isActive = true,
                    Subscription = _subscription
                }
            };

            _vitalRepositoryMock.Setup(r => r.UpdateRangeAsync(It.IsAny<List<Vitals>>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _vitalCommandHandler.UpdateVitalList(vitals, _orgId, _subscription);

            // Assert
            _vitalRepositoryMock.Verify(r => r.UpdateRangeAsync(vitals, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteVitalById_ShouldDeleteVitalFromRepository()
        {
            // Arrange
            var vitalId = Guid.NewGuid();

            _vitalRepositoryMock.Setup(r => r.DeleteByIdAsync(vitalId, _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _vitalCommandHandler.DeleteVitalById(vitalId, _orgId, _subscription);

            // Assert
            _vitalRepositoryMock.Verify(r => r.DeleteByIdAsync(vitalId, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteVitalByEntity_ShouldDeleteVitalFromRepository()
        {
            // Arrange
            var vital = new Vitals
            {
                VitalId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                Temperature = "To be deleted",
                isActive = false,
                Subscription = _subscription
            };

            _vitalRepositoryMock.Setup(r => r.DeleteByEntityAsync(vital, _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _vitalCommandHandler.DeleteVitalByEntity(vital, _orgId, _subscription);

            // Assert
            _vitalRepositoryMock.Verify(r => r.DeleteByEntityAsync(vital, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}

