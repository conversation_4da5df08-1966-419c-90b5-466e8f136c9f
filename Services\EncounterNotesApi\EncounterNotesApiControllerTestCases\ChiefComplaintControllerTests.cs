﻿﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using NUnit.Framework;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesService.EncounterNotesServiceResources;
using EncounterNotesApi.Controllers;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class ChiefComplaintControllerTests
    {
        private ChiefComplaintController _controller;
        private Mock<IChiefComplaintCommandHandler<ChiefComplaint>> _chiefComplaintCommandHandlerMock;
        private Mock<IChiefComplaintQueryHandler<ChiefComplaint>> _chiefComplaintQueryHandlerMock;
        private Mock<ILogger<ChiefComplaintController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private List<ChiefComplaint> _testChiefComplaints;
        private readonly Guid _orgId = Guid.NewGuid();
        private readonly bool _isSubscription = false;

        [SetUp]
        public void Setup()
        {
            _chiefComplaintCommandHandlerMock = new Mock<IChiefComplaintCommandHandler<ChiefComplaint>>();
            _chiefComplaintQueryHandlerMock = new Mock<IChiefComplaintQueryHandler<ChiefComplaint>>();
            _loggerMock = new Mock<ILogger<ChiefComplaintController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();

            // Setup localizer mock
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Chief complaint registered successfully."));
            _localizerMock.Setup(x => x["ErrorCreatingComplaint"]).Returns(new LocalizedString("ErrorCreatingComplaint", "Error creating chief complaint."));
            _localizerMock.Setup(x => x["ComplaintNotFound"]).Returns(new LocalizedString("ComplaintNotFound", "Chief complaint not found."));
            _localizerMock.Setup(x => x["ErrorFetchingComplaint"]).Returns(new LocalizedString("ErrorFetchingComplaint", "Error fetching chief complaint."));
            _localizerMock.Setup(x => x["ErrorFetchingComplaints"]).Returns(new LocalizedString("ErrorFetchingComplaints", "Error fetching chief complaints."));
            _localizerMock.Setup(x => x["ErrorDeletingComplaint"]).Returns(new LocalizedString("ErrorDeletingComplaint", "Error deleting chief complaint."));
            _localizerMock.Setup(x => x["ErrorUpdatingComplaint"]).Returns(new LocalizedString("ErrorUpdatingComplaint", "Error updating chief complaint."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));

            _testChiefComplaints = new List<ChiefComplaint>
            {
                new ChiefComplaint {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    Description = "Headache",
                    DateOfComplaint = DateTime.Now.AddDays(-5),
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false,
                    Subscription = _isSubscription
                },
                new ChiefComplaint {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    Description = "Fever",
                    DateOfComplaint = DateTime.Now.AddDays(-3),
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false,
                    Subscription = _isSubscription
                }
            };

            _controller = new ChiefComplaintController(
                _chiefComplaintQueryHandlerMock.Object,
                _chiefComplaintCommandHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetByPatientIdAsync_ReturnsChiefComplaints_WhenComplaintsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientComplaints = _testChiefComplaints.Select(c => { c.PatientId = patientId; return c; }).ToList();
            _chiefComplaintQueryHandlerMock.Setup(x => x.GetChiefComplaintsByPatientId(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientComplaints);

            // Act
            var result = await _controller.GetByPatientIdAsync(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedComplaints = okResult.Value as IEnumerable<ChiefComplaint>;
            returnedComplaints.Should().NotBeNull();
            returnedComplaints.Should().BeEquivalentTo(patientComplaints);
        }

        [Test]
        public async Task GetByPatientIdAsync_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _chiefComplaintQueryHandlerMock.Setup(x => x.GetChiefComplaintsByPatientId(patientId, _orgId, _isSubscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetByPatientIdAsync(patientId, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["ErrorFetchingComplaints"]);
        }

        [Test]
        public async Task GetChiefComplaintById_ReturnsComplaint_WhenComplaintExists()
        {
            // Arrange
            var complaintId = _testChiefComplaints[0].Id;
            _chiefComplaintQueryHandlerMock.Setup(x => x.GetChiefComplaintById(complaintId, _orgId, _isSubscription))
                .ReturnsAsync(_testChiefComplaints[0]);

            // Act
            var result = await _controller.GetChiefComplaintById(complaintId, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedComplaint = okResult.Value as ChiefComplaint;
            returnedComplaint.Should().NotBeNull();
            returnedComplaint.Should().BeEquivalentTo(_testChiefComplaints[0]);
        }

        [Test]
        public async Task GetChiefComplaintById_ReturnsNotFound_WhenComplaintDoesNotExist()
        {
            // Arrange
            var complaintId = Guid.NewGuid();
            _chiefComplaintQueryHandlerMock.Setup(x => x.GetChiefComplaintById(complaintId, _orgId, _isSubscription))
                .ReturnsAsync((ChiefComplaint)null);

            // Act
            var result = await _controller.GetChiefComplaintById(complaintId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["ComplaintNotFound"]);
        }

        [Test]
        public async Task CreateChiefComplaint_ReturnsOk_WhenCreationSucceeds()
        {
            // Arrange
            var newComplaint = new ChiefComplaint
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Description = "Sore throat",
                DateOfComplaint = DateTime.Now,
                OrganizationId = _orgId,
                PcpId = Guid.NewGuid()
            };

            // Act
            var result = await _controller.CreateChiefComplaint(newComplaint, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _chiefComplaintCommandHandlerMock.Verify(x => x.AddChiefComplaint(It.IsAny<List<ChiefComplaint>>(), _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateChiefComplaint_ReturnsOk_WhenUpdateSucceeds()
        {
            // Arrange
            var existingComplaint = _testChiefComplaints[0];
            var complaintToUpdate = new ChiefComplaint
            {
                Id = existingComplaint.Id,
                PatientId = existingComplaint.PatientId,
                Description = "Updated description",
                DateOfComplaint = existingComplaint.DateOfComplaint,
                OrganizationId = existingComplaint.OrganizationId,
                PcpId = existingComplaint.PcpId
            };

            _chiefComplaintQueryHandlerMock.Setup(x => x.GetChiefComplaintById(existingComplaint.Id, _orgId, _isSubscription))
                .ReturnsAsync(existingComplaint);

            // Act
            var result = await _controller.UpdateChiefComplaint(existingComplaint.Id, complaintToUpdate, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            // Verify the command handler was called
            _chiefComplaintCommandHandlerMock.Verify(x => x.UpdateChiefComplaint(It.IsAny<ChiefComplaint>(), _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task DeleteChiefComplaint_ReturnsNoContent_WhenDeletionSucceeds()
        {
            // Arrange
            var complaintId = _testChiefComplaints[0].Id;
            _chiefComplaintQueryHandlerMock.Setup(x => x.GetChiefComplaintById(complaintId, _orgId, _isSubscription))
                .ReturnsAsync(_testChiefComplaints[0]);

            // Act
            var result = await _controller.DeleteChiefComplaint(complaintId, _orgId, _isSubscription);

            // Assert
            var noContentResult = result as NoContentResult;
            noContentResult.Should().NotBeNull();
            noContentResult.StatusCode.Should().Be(204);

            // Verify the command handler was called
            _chiefComplaintCommandHandlerMock.Verify(x => x.DeleteChiefComplaintById(complaintId, _orgId, _isSubscription), Times.Once);
        }


    }
}
