﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface IReferralOutgoingCommandHandler<TText>
    {
        Task DeleteReferralOutgoingByEntity(PatientReferralOutgoing ReferralOutgoing, Guid OrgId, bool Subscription);
        Task DeleteReferralOutgoingById(Guid id, Guid OrgId, bool Subscription);
        Task AddReferralOutgoing(List<TText> texts, Guid OrgId, bool Subscription);
        Task UpdateReferralOutgoing(PatientReferralOutgoing ReferralOutgoing, Guid OrgId, bool Subscription);
        Task UpdateReferralOutgoingList(List<PatientReferralOutgoing> PatientReferralOutgoing, Guid OrgId, bool Subscription);
    }
}
