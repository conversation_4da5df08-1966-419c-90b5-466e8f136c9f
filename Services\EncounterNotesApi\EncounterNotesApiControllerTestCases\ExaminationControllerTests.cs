using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class ExaminationControllerTests
    {
        private Mock<IExaminationCommandHandler> _examinationCommandHandlerMock;
        private Mock<IExaminationQueryHandler> _examinationQueryHandlerMock;
        private Mock<ILogger<ExaminationController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private ExaminationController _controller;
        private List<Examination> _testExaminations;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _examinationCommandHandlerMock = new Mock<IExaminationCommandHandler>();
            _examinationQueryHandlerMock = new Mock<IExaminationQueryHandler>();
            _loggerMock = new Mock<ILogger<ExaminationController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["InternalServerError"]).Returns(new LocalizedString("InternalServerError", "Internal server error."));
            _localizerMock.Setup(x => x["ExaminationListIsEmpty"]).Returns(new LocalizedString("ExaminationListIsEmpty", "Examination list is empty."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Examinations added successfully."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Examinations updated successfully."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating examinations."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));

            _testExaminations = new List<Examination>
            {
                new Examination {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdateDate = DateTime.Now.AddDays(-25),
                    GeneralDescription = "Normal appearance",
                    HEENT = "Normal",
                    Lungs = "Clear",
                    Abdomen = "Soft, non-tender",
                    PeripheralPulses = "Normal",
                    Skin = "Normal",
                    Others = "None",
                    Subscription = _isSubscription
                },
                new Examination {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdateDate = DateTime.Now.AddDays(-15),
                    GeneralDescription = "Appears well",
                    HEENT = "Normal",
                    Lungs = "Wheezing noted",
                    Abdomen = "Soft, non-tender",
                    PeripheralPulses = "Normal",
                    Skin = "Dry",
                    Others = "None",
                    Subscription = _isSubscription
                }
            };

            _controller = new ExaminationController(
                _examinationCommandHandlerMock.Object,
                _examinationQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetAllExaminationByPatientId_ReturnsExaminations_WhenExaminationsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientExaminations = _testExaminations.Select(e => { e.PatientId = patientId; return e; }).ToList();
            _examinationQueryHandlerMock.Setup(x => x.GetExaminationByPatientId(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientExaminations);

            // Act
            var result = await _controller.GetAllExaminationByPatientId(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedExaminations = okResult.Value as IEnumerable<Examination>;
            returnedExaminations.Should().NotBeNull();
            returnedExaminations.Should().BeEquivalentTo(patientExaminations);
        }

        [Test]
        public async Task GetAllExaminationByPatientId_ReturnsEmptyList_WhenNoExaminationsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _examinationQueryHandlerMock.Setup(x => x.GetExaminationByPatientId(patientId, _orgId, _isSubscription))
                .ReturnsAsync((IEnumerable<Examination>)null);

            // Act
            var result = await _controller.GetAllExaminationByPatientId(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedExaminations = okResult.Value as IEnumerable<Examination>;
            returnedExaminations.Should().NotBeNull();
            returnedExaminations.Should().BeEmpty();
        }

        [Test]
        public async Task GetAllExaminationByPatientId_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _examinationQueryHandlerMock.Setup(x => x.GetExaminationByPatientId(patientId, _orgId, _isSubscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllExaminationByPatientId(patientId, _orgId, _isSubscription);

            // Assert
            var statusCodeResult = result.Result as ObjectResult;
            statusCodeResult.Should().NotBeNull();
            statusCodeResult.StatusCode.Should().Be(500);
            statusCodeResult.Value.Should().Be(_localizerMock.Object["InternalServerError"]);
        }

        [Test]
        public async Task GetActiveExaminationByPatientId_ReturnsActiveExaminations_WhenExaminationsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activeExaminations = _testExaminations.Select(e => { e.PatientId = patientId; e.IsActive = true; return e; }).ToList();
            _examinationQueryHandlerMock.Setup(x => x.GetExaminationByPatientIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activeExaminations);

            // Act
            var result = await _controller.GetActiveExaminationByPatientId(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedExaminations = okResult.Value as IEnumerable<Examination>;
            returnedExaminations.Should().NotBeNull();
            returnedExaminations.Should().BeEquivalentTo(activeExaminations);
        }

        [Test]
        public async Task AddExaminationinBulk_ValidExaminations_ReturnsOk()
        {
            // Arrange
            var examinationsToAdd = _testExaminations;

            // Act
            var result = await _controller.AddExaminationinBulk(examinationsToAdd, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _examinationCommandHandlerMock.Verify(x => x.AddExaminationList(examinationsToAdd, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task AddExaminationinBulk_EmptyExaminations_ReturnsBadRequest()
        {
            // Arrange
            var emptyExaminations = new List<Examination>();

            // Act
            var result = await _controller.AddExaminationinBulk(emptyExaminations, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["ExaminationListIsEmpty"]);

            // Verify the command handler was not called
            _examinationCommandHandlerMock.Verify(x => x.AddExaminationList(It.IsAny<List<Examination>>(), _orgId, _isSubscription), Times.Never);
        }

        [Test]
        public async Task UpdateExaminationsList_ValidExaminations_ReturnsOk()
        {
            // Arrange
            var examinationsToUpdate = _testExaminations;

            // Act
            var result = await _controller.UpdateExaminationsList(examinationsToUpdate, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _examinationCommandHandlerMock.Verify(x => x.UpdateExaminationList(examinationsToUpdate, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateExaminationsList_EmptyExaminations_ReturnsBadRequest()
        {
            // Arrange
            var emptyExaminations = new List<Examination>();

            // Act
            var result = await _controller.UpdateExaminationsList(emptyExaminations, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["ExaminationListIsEmpty"]);

            // Verify the command handler was not called
            _examinationCommandHandlerMock.Verify(x => x.UpdateExaminationList(It.IsAny<List<Examination>>(), _orgId, _isSubscription), Times.Never);
        }


    }
}
