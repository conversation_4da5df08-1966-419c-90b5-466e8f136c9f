﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IVitalQueryHandler<TText>
    {
        Task<IEnumerable<Vitals>> GetVitalsByIdAndIsActive(Guid id, Guid OrgId, bool Subscription);
        Task<IEnumerable<Vitals>> GetAllVitalsById(Guid id, Guid OrgId, bool Subscription);
    }
}
