﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class PhysicalTherapyQueryHandler : IPhysicalTherapyQueryHandler<PhysicalTherapyData>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public PhysicalTherapyQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<PhysicalTherapyData>> GetAllPhysicalTherapyById(Guid id, Guid OrgId, bool Subscription)
        {
            var physicalTherapies = await _unitOfWork.PhysicalTherapyRepository.GetAllPhysicalTherapyAsync(OrgId, Subscription);
            return physicalTherapies.Where(therapy => therapy.PatientId == id);
        }

        public async Task<IEnumerable<PhysicalTherapyData>> GetPhysicalTherapyByIdAndIsActive(Guid id, Guid OrgId, bool Subscription)
        {
            var physicalTherapies = await _unitOfWork.PhysicalTherapyRepository.GetAsync(OrgId, Subscription);
            return physicalTherapies.Where(therapy => therapy.PatientId == id && therapy.IsActive == true);
        }
    }
}
