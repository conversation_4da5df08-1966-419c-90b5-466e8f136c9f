﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class FDBVaccine_CPT_CVXRepository : GenericRepository<FDBVaccine_CPT_CVX>, IFDBVaccine_CPT_CVXRepository
    {
        private readonly RecordDatabaseContext _context;

        public FDBVaccine_CPT_CVXRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
        }
        public async Task<FDBVaccine_CPT_CVX> GetCPTforCVX(string CVX)
        {
            var CPT_Code = await _context.FDBVaccines_CPT_CVX
                .FirstOrDefaultAsync(t => t.EVD_CVX_CD == CVX);

            return CPT_Code;
        }
    }
}
