using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class SymptomsControllerTests
    {
        private Mock<ISymptomsCommandHandler<Symptoms>> _symptomsCommandHandlerMock;
        private Mock<ISymptomsQueryHandler<Symptoms>> _symptomsQueryHandlerMock;
        private Mock<ILogger<SymptomsController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private SymptomsController _controller;
        private List<Symptoms> _testSymptoms;

        [SetUp]
        public void Setup()
        {
            _symptomsCommandHandlerMock = new Mock<ISymptomsCommandHandler<Symptoms>>();
            _symptomsQueryHandlerMock = new Mock<ISymptomsQueryHandler<Symptoms>>();
            _loggerMock = new Mock<ILogger<SymptomsController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();

            // Mock localizer strings
            _localizerMock.Setup(x => x["GetSymptomsLogError"]).Returns(new LocalizedString("GetSymptomsLogError", "Error fetching symptoms."));
            _localizerMock.Setup(x => x["NoSymptomsProvided"]).Returns(new LocalizedString("NoSymptomsProvided", "No symptoms provided."));
            _localizerMock.Setup(x => x["SuccessfulSymptomAddition"]).Returns(new LocalizedString("SuccessfulSymptomAddition", "Symptoms added successfully."));
            _localizerMock.Setup(x => x["PostSymptomsLogError"]).Returns(new LocalizedString("PostSymptomsLogError", "Error adding symptoms."));

            _testSymptoms = new List<Symptoms>
            {
                new Symptoms { SymptomId = Guid.NewGuid(), Symptom = "Fever" },
                new Symptoms { SymptomId = Guid.NewGuid(), Symptom = "Cough" },
                new Symptoms { SymptomId = Guid.NewGuid(), Symptom = "Headache" },
                new Symptoms { SymptomId = Guid.NewGuid(), Symptom = "Fatigue" }
            };

            _controller = new SymptomsController(
                _symptomsCommandHandlerMock.Object,
                _symptomsQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task Get_ReturnsListOfSymptoms()
        {
            // Arrange
            _symptomsQueryHandlerMock.Setup(x => x.GetAllSymptoms()).ReturnsAsync(_testSymptoms);

            // Act
            var result = await _controller.Get();

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var symptoms = okResult.Value as IEnumerable<Symptoms>;
            symptoms.Should().NotBeNull();
            symptoms.Should().BeEquivalentTo(_testSymptoms);
        }

        [Test]
        public async Task Get_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            _symptomsQueryHandlerMock.Setup(x => x.GetAllSymptoms()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Get();

            // Assert
            var statusCodeResult = result.Result as ObjectResult;
            statusCodeResult.Should().NotBeNull();
            statusCodeResult.StatusCode.Should().Be(500);
            statusCodeResult.Value.Should().Be(_localizerMock.Object["GetSymptomsLogError"]);
        }

        [Test]
        public async Task Post_ValidSymptoms_ReturnsOk()
        {
            // Arrange
            var symptomsToAdd = _testSymptoms;

            // Act
            var result = await _controller.Post(symptomsToAdd);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            // Use reflection to access the anonymous object properties
            var response = okResult.Value;
            response.Should().NotBeNull();

            var messageProperty = response.GetType().GetProperty("Message");
            messageProperty.Should().NotBeNull();

            var message = messageProperty.GetValue(response);
            message.Should().Be(_localizerMock.Object["SuccessfulSymptomAddition"]);

            // Verify the command handler was called with the correct parameters
            _symptomsCommandHandlerMock.Verify(x => x.AddSymptom(It.Is<IEnumerable<Symptoms>>(
                s => s.Count() == symptomsToAdd.Count &&
                s.All(item => symptomsToAdd.Any(original =>
                    original.SymptomId == item.SymptomId &&
                    original.Symptom == item.Symptom)))),
                Times.Once);
        }

        [Test]
        public async Task Post_EmptySymptoms_ReturnsBadRequest()
        {
            // Arrange
            var emptySymptoms = new List<Symptoms>();

            // Act
            var result = await _controller.Post(emptySymptoms);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoSymptomsProvided"]);

            // Verify the command handler was not called
            _symptomsCommandHandlerMock.Verify(x => x.AddSymptom(It.IsAny<IEnumerable<Symptoms>>()), Times.Never);
        }

        [Test]
        public async Task Post_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var symptomsToAdd = _testSymptoms;
            _symptomsCommandHandlerMock.Setup(x => x.AddSymptom(It.IsAny<IEnumerable<Symptoms>>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Post(symptomsToAdd);

            // Assert
            var statusCodeResult = result as ObjectResult;
            statusCodeResult.Should().NotBeNull();
            statusCodeResult.StatusCode.Should().Be(500);
            statusCodeResult.Value.Should().Be(_localizerMock.Object["PostSymptomsLogError"]);
        }
    }
}
