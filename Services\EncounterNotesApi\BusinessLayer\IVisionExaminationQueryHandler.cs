﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IVisionExaminationQueryHandler<T>
    {
        Task<IEnumerable<VisionRx>> GetVisionExaminationByIdAndIsActive(Guid id,Guid OrgID, bool Subscription);
        Task<List<T>> GetVisionExaminationById(Guid id, Guid OrgID, bool Subscription);
        Task<IEnumerable<VisionRx>> GetAllVisionRecords(Guid OrgID, bool Subscription);
    }
}
