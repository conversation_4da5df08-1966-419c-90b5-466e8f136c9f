﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IVisionExaminationRepository : IShardGenericRepository<VisionRx>
    {
        Task<List<VisionRx>> GetVisionExaminationById(Guid recordId, Guid OrgId, bool Subscription);
        Task DeleteVisionExaminationByIdAsync(Guid examinationId, Guid orgId, bool Subscription);
    }
}
