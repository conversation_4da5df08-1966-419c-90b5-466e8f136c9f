﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class PhysicalExaminationQueryHandler : IPhysicalExaminationQueryHandler<PhysicalExamination>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public PhysicalExaminationQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<PhysicalExamination>> GetAllExaminationsById(Guid id, Guid OrgId, bool Subscription)
        {
            var examinations = await _unitOfWork.PhysicalExaminationRepository.GetAllExaminationsAsync(OrgId, Subscription);

            return examinations.Where(PhysExamination => PhysExamination.PatientId == id);
        }

        public async Task<IEnumerable<PhysicalExamination>> GetExaminationByIdAndIsActive(Guid id, Guid OrgId, bool Subscription)
        {
            var examinations = await _unitOfWork.PhysicalExaminationRepository.GetAsync(OrgId, Subscription);

            return examinations.Where(PhysExamination => PhysExamination.PatientId == id && PhysExamination.IsActive == true);
        }
    }
}
