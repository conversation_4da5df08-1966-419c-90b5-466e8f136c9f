﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using System;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class MedicalHistoryController : ControllerBase
    {
        private readonly IMedicalHistoryCommandHandler<MedicalHistory> _medicalDataHandler;
        private readonly IMedicalHistoryQueryHandler<MedicalHistory> _medicalQueryHandler;
        private readonly ILogger<MedicalHistoryController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;

        public MedicalHistoryController(
            IMedicalHistoryCommandHandler<MedicalHistory> medicalDataHandler,
            IMedicalHistoryQueryHandler<MedicalHistory> medicalQueryHandler,
            ILogger<MedicalHistoryController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
        )
        {
            _medicalDataHandler = medicalDataHandler;
            _medicalQueryHandler = medicalQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        [HttpGet("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<MedicalHistory>>> GetAllById(Guid id, [FromQuery] Guid orgId, [FromQuery] bool isSubscription)
        {
            try
            {
                var data = await _medicalQueryHandler.GetAllMedicalHistoriesById(id, orgId, isSubscription);
                return data != null ? Ok(data) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500, _localizer["GetLogError"]);
            }
        }

        [HttpGet("{id:guid}/IsActive/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<MedicalHistory>>> GetAllByIdAndIsActive(Guid id, [FromQuery] Guid orgId, [FromQuery] bool isSubscription)
        {
            try
            {
                var history = await _medicalQueryHandler.GetMedicalHistoryByIdAndIsActive(id, orgId, isSubscription);
                return history != null ? Ok(history) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500, _localizer["GetLogError"]);
            }
        }

        [HttpPost]
        [Route("AddMedicalHistory/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> AddMedicalHistory([FromBody] List<MedicalHistory> medicalHistories, [FromQuery] Guid orgId, [FromQuery] bool isSubscription)
        {
            if (medicalHistories == null || medicalHistories.Count == 0)
            {
                return BadRequest(_localizer["NoLicense"]);
            }

            try
            {
                await _medicalDataHandler.AddMedicalHistory(medicalHistories, orgId, isSubscription);
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                return StatusCode(500);
            }
        }

        [HttpDelete]
        [Route("DeleteMedicalHistory/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> DeleteByEntity([FromBody] MedicalHistory medicalHistory, [FromQuery] Guid orgId, [FromQuery] bool isSubscription)
        {
            if (medicalHistory == null)
            {
                return BadRequest(_localizer["InvalidRecord"]);
            }

            try
            {
                await _medicalDataHandler.DeleteMedicalHistoryByEntity(medicalHistory, orgId, isSubscription);
                return Ok(_localizer["DeleteSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                return StatusCode(500, _localizer["DeleteLogError"]);
            }
        }

        [HttpPut("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateMedicalHistoryById(Guid id, [FromBody] MedicalHistory medicalHistory, [FromQuery] Guid orgId, [FromQuery] bool isSubscription)
        {
            if (medicalHistory == null || medicalHistory.PatientId != id)
            {
                return BadRequest(_localizer["InvalidRecord"]);
            }

            try
            {
                await _medicalDataHandler.UpdateMedicalHistory(medicalHistory, orgId, isSubscription);
                return Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                return StatusCode(500, _localizer["UpdateLogError"]);
            }
        }

        [HttpPut]
        [Route("UpdateMedicalHistoryList/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateMedicalHistoryList([FromBody] List<MedicalHistory> medicalHistories, [FromQuery] Guid orgId, [FromQuery] bool isSubscription)
        {
            try
            {
                await _medicalDataHandler.UpdateMedicalHistoryList(medicalHistories, orgId, isSubscription);
                return Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                return StatusCode(500, _localizer["UpdateLogError"]);
            }
        }

    }
}