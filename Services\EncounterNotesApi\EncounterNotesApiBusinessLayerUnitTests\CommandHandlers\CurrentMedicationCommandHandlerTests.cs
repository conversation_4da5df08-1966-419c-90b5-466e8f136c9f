using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class CurrentMedicationCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<CurrentMedicationCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, CurrentMedication, EncounterDataAccessLayer>> _migrationMock;
        private Mock<ICurrentMedicationRepository> _currentMedicationRepositoryMock;
        private CurrentMedicationCommandHandler _currentMedicationCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<CurrentMedicationCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, CurrentMedication, EncounterDataAccessLayer>>();
            _currentMedicationRepositoryMock = new Mock<ICurrentMedicationRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;

            _unitOfWorkMock.Setup(u => u.CurrentMedicationRepository).Returns(_currentMedicationRepositoryMock.Object);
            _currentMedicationCommandHandler = new CurrentMedicationCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object);
        }

        [Test]
        public async Task AddMedication_ShouldAddMedicationsToRepository()
        {
            // Arrange
            var medications = new List<CurrentMedication>
            {
                new CurrentMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    BrandName = "Lipitor",
                    DrugDetails = "Atorvastatin 20mg",
                    Quantity = "30 tablets",
                    Frequency = "Once daily",
                    isActive = true,
                    StartDate = DateTime.Now.AddDays(-30),
                    EndDate = DateTime.Now.AddDays(30),
                    CheifComplaint = "High cholesterol",
                    CheifComplaintId = Guid.NewGuid(),
                    Subscription = _subscription
                },
                new CurrentMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdatedDate = DateTime.Now.AddDays(-55),
                    BrandName = "Aspirin",
                    DrugDetails = "81mg",
                    Quantity = "90 tablets",
                    Frequency = "Once daily",
                    isActive = true,
                    StartDate = DateTime.Now.AddDays(-60),
                    EndDate = DateTime.Now.AddDays(30),
                    CheifComplaint = "Heart disease prevention",
                    CheifComplaintId = Guid.NewGuid(),
                    Subscription = _subscription
                }
            };

            _currentMedicationRepositoryMock.Setup(r => r.AddAsync(It.IsAny<List<CurrentMedication>>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _currentMedicationCommandHandler.AddMedication(medications, _orgId, _subscription);

            // Assert
            _currentMedicationRepositoryMock.Verify(r => r.AddAsync(medications, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateMedication_ShouldUpdateMedicationInRepository()
        {
            // Arrange
            var medication = new CurrentMedication
            {
                MedicineId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-30),
                UpdatedDate = DateTime.Now,
                BrandName = "Lipitor",
                DrugDetails = "Atorvastatin 40mg", // Updated dosage
                Quantity = "30 tablets",
                Frequency = "Once daily",
                isActive = true,
                StartDate = DateTime.Now.AddDays(-30),
                EndDate = DateTime.Now.AddDays(60), // Updated end date
                CheifComplaint = "High cholesterol",
                CheifComplaintId = Guid.NewGuid(),
                Subscription = _subscription
            };

            _currentMedicationRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<CurrentMedication>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _currentMedicationCommandHandler.UpdateMedication(medication, _orgId, _subscription);

            // Assert
            _currentMedicationRepositoryMock.Verify(r => r.UpdateAsync(medication, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateMedicationList_ShouldUpdateMedicationsInRepository()
        {
            // Arrange
            var medications = new List<CurrentMedication>
            {
                new CurrentMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    BrandName = "Lipitor",
                    DrugDetails = "Atorvastatin 40mg",
                    isActive = true,
                    Subscription = _subscription
                },
                new CurrentMedication
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    BrandName = "Aspirin",
                    DrugDetails = "81mg",
                    isActive = true,
                    Subscription = _subscription
                }
            };

            _currentMedicationRepositoryMock.Setup(r => r.UpdateRangeAsync(It.IsAny<List<CurrentMedication>>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _currentMedicationCommandHandler.UpdateMedicationList(medications, _orgId, _subscription);

            // Assert
            _currentMedicationRepositoryMock.Verify(r => r.UpdateRangeAsync(medications, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteMedicationById_ShouldDeleteMedicationFromRepository()
        {
            // Arrange
            var medicationId = Guid.NewGuid();

            _currentMedicationRepositoryMock.Setup(r => r.DeleteByIdAsync(medicationId, _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _currentMedicationCommandHandler.DeleteMedicationById(medicationId, _orgId, _subscription);

            // Assert
            _currentMedicationRepositoryMock.Verify(r => r.DeleteByIdAsync(medicationId, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteMedicationByEntity_ShouldDeleteMedicationFromRepository()
        {
            // Arrange
            var medication = new CurrentMedication
            {
                MedicineId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                BrandName = "To be deleted",
                isActive = false,
                Subscription = _subscription
            };

            _currentMedicationRepositoryMock.Setup(r => r.DeleteByEntityAsync(medication, _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _currentMedicationCommandHandler.DeleteMedicationByEntity(medication, _orgId, _subscription);

            // Assert
            _currentMedicationRepositoryMock.Verify(r => r.DeleteByEntityAsync(medication, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}

