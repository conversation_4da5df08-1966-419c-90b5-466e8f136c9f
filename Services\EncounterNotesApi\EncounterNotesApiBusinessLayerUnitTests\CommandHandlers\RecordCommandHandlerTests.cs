using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class RecordCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<RecordCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, Record, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IRecordRepository> _recordRepositoryMock;
        private RecordCommandHandler _recordCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<RecordCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, Record, EncounterDataAccessLayer>>();
            _recordRepositoryMock = new Mock<IRecordRepository>();

            _unitOfWorkMock.Setup(u => u.RecordRepository).Returns(_recordRepositoryMock.Object);

            _recordCommandHandler = new RecordCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task AddRecord_ValidRecords_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var wordTimings = new List<WordTiming>
            {
                new WordTiming
                {
                    Id = 1,
                    Word = "test",
                    StartTime = 0.5f,
                    EndTime = 0.8f
                }
            };

            var records = new List<Record>
            {
                new Record
                {
                    Id = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    PatientName = "John Doe",
                    DateTime = DateTime.Now,
                    Notes = "Patient visit notes",
                    Transcription = "Transcription of the visit",
                    isEditable = true,
                    Subscription = _subscription,
                    WordTimings = wordTimings
                }
            };

            // Act
            await _recordCommandHandler.AddRecord(records, _orgId, _subscription);

            // Assert
            _recordRepositoryMock.Verify(r => r.AddRecordsAsync(records, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateRecord_ValidRecord_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var record = new Record
            {
                Id = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                PatientName = "Updated Name",
                DateTime = DateTime.Now,
                Notes = "Updated notes",
                Transcription = "Updated transcription",
                isEditable = true,
                Subscription = _subscription
            };

            // Act
            await _recordCommandHandler.UpdateRecord(record, _orgId, _subscription);

            // Assert
            _recordRepositoryMock.Verify(r => r.UpdateAsync(record, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteRecordById_ValidId_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var id = Guid.NewGuid();

            // Act
            await _recordCommandHandler.DeleteRecordById(id, _orgId, _subscription);

            // Assert
            _recordRepositoryMock.Verify(r => r.DeleteByIdAsync(id, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteRecordByEntity_ValidEntity_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var record = new Record
            {
                Id = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                PatientName = "John Doe",
                DateTime = DateTime.Now,
                Subscription = _subscription
            };

            // Act
            await _recordCommandHandler.DeleteRecordByEntity(record, _orgId, _subscription);

            // Assert
            _recordRepositoryMock.Verify(r => r.DeleteByEntityAsync(record, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }




    }
}
