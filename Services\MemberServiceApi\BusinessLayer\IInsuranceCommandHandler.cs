﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IInsuranceCommandHandler<T>
    {
        Task AddInsuranceAsync(List<Insurance> insurances, Guid OrgID, bool Subscription);
        Task UpdateInsuranceAsync(Insurance insurance, Guid OrgID, bool Subscription);
        Task DeleteInsuranceAsync(Guid id, Guid OrgID, bool Subscription);
    }
}
