using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class AssessmentsCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<AssessmentsCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, AssessmentsData, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IAssessmentsRepository> _assessmentsRepositoryMock;
        private AssessmentsCommandHandler _assessmentsCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<AssessmentsCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, AssessmentsData, EncounterDataAccessLayer>>();
            _assessmentsRepositoryMock = new Mock<IAssessmentsRepository>();

            _unitOfWorkMock.Setup(u => u.AssessmentsRepository).Returns(_assessmentsRepositoryMock.Object);

            _assessmentsCommandHandler = new AssessmentsCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task AddAssessments_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var assessments = new List<AssessmentsData>
            {
                new AssessmentsData
                {
                    AssessmentsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Diagnosis = "Hypertension",
                    ICDCode = "I10",
                    Specify = "Essential hypertension",
                    Notes = "Patient has high blood pressure",
                    IsActive = true
                }
            };

            _assessmentsRepositoryMock.Setup(r => r.AddAsync(assessments, _orgId, _subscription))
                .Returns(Task.CompletedTask);
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _assessmentsCommandHandler.AddAssessments(assessments, _orgId, _subscription);

            // Assert
            _assessmentsRepositoryMock.Verify(r => r.AddAsync(assessments, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateAssessments_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var assessment = new AssessmentsData
            {
                AssessmentsID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Diagnosis = "Hypertension",
                ICDCode = "I10",
                Specify = "Essential hypertension",
                Notes = "Patient has high blood pressure",
                IsActive = true
            };

            _assessmentsRepositoryMock.Setup(r => r.UpdateAsync(assessment, _orgId, _subscription))
                .Returns(Task.CompletedTask);
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _assessmentsCommandHandler.UpdateAssessments(assessment, _orgId, _subscription);

            // Assert
            _assessmentsRepositoryMock.Verify(r => r.UpdateAsync(assessment, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteAssessmentsById_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var assessmentId = Guid.NewGuid();

            _assessmentsRepositoryMock.Setup(r => r.DeleteByIdAsync(assessmentId, _orgId, _subscription))
                .Returns(Task.CompletedTask);
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _assessmentsCommandHandler.DeleteAssessmentsById(assessmentId, _orgId, _subscription);

            // Assert
            _assessmentsRepositoryMock.Verify(r => r.DeleteByIdAsync(assessmentId, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteAssessmentsByEntity_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var assessment = new AssessmentsData
            {
                AssessmentsID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Diagnosis = "Hypertension",
                ICDCode = "I10",
                IsActive = true
            };

            _assessmentsRepositoryMock.Setup(r => r.DeleteByEntityAsync(assessment, _orgId, _subscription))
                .Returns(Task.CompletedTask);
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _assessmentsCommandHandler.DeleteAssessmentsByEntity(assessment, _orgId, _subscription);

            // Assert
            _assessmentsRepositoryMock.Verify(r => r.DeleteByEntityAsync(assessment, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateAssessmentsList_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var assessments = new List<AssessmentsData>
            {
                new AssessmentsData
                {
                    AssessmentsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Diagnosis = "Hypertension",
                    ICDCode = "I10",
                    IsActive = true
                },
                new AssessmentsData
                {
                    AssessmentsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Diagnosis = "Diabetes",
                    ICDCode = "E11",
                    IsActive = true
                }
            };

            _assessmentsRepositoryMock.Setup(r => r.UpdateRangeAsync(assessments, _orgId, _subscription))
                .Returns(Task.CompletedTask);
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _assessmentsCommandHandler.UpdateAssessmentsList(assessments, _orgId, _subscription);

            // Assert
            _assessmentsRepositoryMock.Verify(r => r.UpdateRangeAsync(assessments, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}
