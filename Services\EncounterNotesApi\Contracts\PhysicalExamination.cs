﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesContracts
{
    public class PhysicalExamination : IContract
    {
        public Guid ExaminationId { get; set; }
        public Guid PatientId { get; set; }
        public Guid? OrganizationId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid UpdatedBy { get; set; }
        public string? Skin { get; set; }
        public string? Rash { get; set; }
        public string? Tester { get; set; }
        public string? Moles { get; set; }
        public string? Hemangioma { get; set; }
        public string? VascularMalformation { get; set; }
        public Guid PCPId { get; set; }
        public bool IsActive { get; set; }
        public bool Subscription { get; set; }
    }
}