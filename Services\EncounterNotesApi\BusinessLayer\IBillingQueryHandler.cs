﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IBillingQueryHandler<T>
    {
        Task<T> GetBillingByIdAsync(Guid id);
        Task<List<T>> GetBillingByPatientIdAsync(Guid patientId);
        Task<List<T>> GetAllBillingsAsync();
    }
}
