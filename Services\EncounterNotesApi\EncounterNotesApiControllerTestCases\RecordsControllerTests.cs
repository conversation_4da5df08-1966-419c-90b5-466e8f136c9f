using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class RecordsControllerTests
    {
        private Mock<IRecordCommandHandler<Record>> _recordCommandHandlerMock;
        private Mock<IRecordQueryHandler<Record>> _recordQueryHandlerMock;
        private Mock<ILogger<RecordsController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private RecordsController _controller;
        private List<Record> _testRecords;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _recordCommandHandlerMock = new Mock<IRecordCommandHandler<Record>>();
            _recordQueryHandlerMock = new Mock<IRecordQueryHandler<Record>>();
            _loggerMock = new Mock<ILogger<RecordsController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No license available."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error occurred."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error posting data."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting data."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Delete successful."));
            _localizerMock.Setup(x => x["SuccessfulDeletion"]).Returns(new LocalizedString("SuccessfulDeletion", "Deletion successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["SuccessfulEncounterNote"]).Returns(new LocalizedString("SuccessfulEncounterNote", "Encounter note created successfully."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testRecords = new List<Record>
            {
                new Record {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    PatientName = "John Doe",
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    DateTime = DateTime.Now.AddDays(-30),
                    Notes = "Patient presented with fever and cough. Prescribed antibiotics.",
                    Transcription = "Patient reports fever and cough for 3 days. Temperature 101.2F. Lungs with crackles in right lower lobe.",
                    isEditable = true,
                    Subscription = _isSubscription,
                    WordTimings = new List<WordTiming>
                    {
                        new WordTiming { Word = "Patient", StartTime = 0.0, EndTime = 0.5 },
                        new WordTiming { Word = "reports", StartTime = 0.6, EndTime = 1.0 }
                    }
                },
                new Record {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    PatientName = "Jane Smith",
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    DateTime = DateTime.Now.AddDays(-20),
                    Notes = "Follow-up visit for hypertension. Blood pressure well-controlled.",
                    Transcription = "Patient's blood pressure is 120/80. Medication is working well. No side effects reported.",
                    isEditable = true,
                    Subscription = _isSubscription,
                    WordTimings = new List<WordTiming>
                    {
                        new WordTiming { Word = "Patient's", StartTime = 0.0, EndTime = 0.5 },
                        new WordTiming { Word = "blood", StartTime = 0.6, EndTime = 1.0 }
                    }
                }
            };

            _controller = new RecordsController(
                _recordCommandHandlerMock.Object,
                _recordQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task Get_ReturnsRecords_WhenRecordsExist()
        {
            // Arrange
            _recordQueryHandlerMock.Setup(x => x.GetRecord(_orgId, _isSubscription))
                .ReturnsAsync(_testRecords);

            // Act
            var result = await _controller.Get(_orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedRecords = okResult.Value as IEnumerable<Record>;
            returnedRecords.Should().NotBeNull();
            returnedRecords.Should().BeEquivalentTo(_testRecords);
        }

        [Test]
        public async Task GetById_ReturnsRecord_WhenRecordExists()
        {
            // Arrange
            var recordId = Guid.NewGuid();
            var record = _testRecords[0];
            record.Id = recordId;

            _recordQueryHandlerMock.Setup(x => x.GetRecordById(recordId, _orgId, _isSubscription))
                .ReturnsAsync(record);

            // Act
            var result = await _controller.GetById(recordId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedRecord = okResult.Value as Record;
            returnedRecord.Should().NotBeNull();
            returnedRecord.Should().BeEquivalentTo(record);
        }

        [Test]
        public async Task GetById_ReturnsNotFound_WhenNoRecordExists()
        {
            // Arrange
            var recordId = Guid.NewGuid();
            _recordQueryHandlerMock.Setup(x => x.GetRecordById(recordId, _orgId, _isSubscription))
                .ReturnsAsync((Record)null);

            // Act
            var result = await _controller.GetById(recordId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task GetByPCPId_ReturnsRecords_WhenRecordsExist()
        {
            // Arrange
            var pcpId = Guid.NewGuid();
            var pcpRecords = _testRecords.Select(r => { r.PCPId = pcpId; return r; }).ToList();

            _recordQueryHandlerMock.Setup(x => x.GetRecordByPCPId(pcpId, _orgId, _isSubscription))
                .ReturnsAsync(pcpRecords);

            // Act
            var result = await _controller.GetByPCPId(pcpId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedRecords = okResult.Value as IEnumerable<Record>;
            returnedRecords.Should().NotBeNull();
            returnedRecords.Should().BeEquivalentTo(pcpRecords);
        }

        [Test]
        public async Task GetByPatientId_ReturnsRecords_WhenRecordsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientRecords = _testRecords.Select(r => { r.PatientId = patientId; return r; }).ToList();

            _recordQueryHandlerMock.Setup(x => x.GetRecordByPatientId(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientRecords);

            // Act
            var result = await _controller.GetByPatientId(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedRecords = okResult.Value as IEnumerable<Record>;
            returnedRecords.Should().NotBeNull();
            returnedRecords.Should().BeEquivalentTo(patientRecords);
        }

        [Test]
        public async Task UpdateById_ValidRecord_ReturnsOk()
        {
            // Arrange
            var record = _testRecords[0];

            // Act
            var result = await _controller.UpdateById(record.Id, record, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _recordCommandHandlerMock.Verify(x => x.UpdateRecord(record, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task DeleteById_ValidId_ReturnsOk()
        {
            // Arrange
            var recordId = Guid.NewGuid();

            // Act
            var result = await _controller.DeleteById(recordId, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulDeletion"]);

            // Verify the command handler was called
            _recordCommandHandlerMock.Verify(x => x.DeleteRecordById(recordId, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task DeleteByEntity_ValidRecord_ReturnsOk()
        {
            // Arrange
            var record = _testRecords[0];

            // Act
            var result = await _controller.DeleteByEntity(record, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _recordCommandHandlerMock.Verify(x => x.DeleteRecordByEntity(record, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task EncounterNote_ValidRecordDTOs_ReturnsOk()
        {
            // Arrange
            var recordDTOs = new List<RecordDTO>
            {
                new RecordDTO
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    PatientName = "John Doe",
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    DateTime = DateTime.Now,
                    Notes = "Patient presented with fever and cough.",
                    Transcription = "Patient reports fever and cough for 3 days.",
                    isEditable = true,
                    WordTimings = new List<WordTimingDTO>
                    {
                        new WordTimingDTO { Word = "Patient", StartTime = 0.0, EndTime = 0.5 },
                        new WordTimingDTO { Word = "reports", StartTime = 0.6, EndTime = 1.0 }
                    }
                }
            };

            // Act
            var result = await _controller.EncounterNote(recordDTOs, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            // Check that the value is not null
            okResult.Value.Should().NotBeNull();

            // Verify the command handler was called
            _recordCommandHandlerMock.Verify(x => x.AddRecord(It.IsAny<List<Record>>(), _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task EncounterNote_EmptyList_ReturnsBadRequest()
        {
            // Arrange
            var emptyRecordDTOs = new List<RecordDTO>();

            // Act
            var result = await _controller.EncounterNote(emptyRecordDTOs, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);
        }


    }
}
