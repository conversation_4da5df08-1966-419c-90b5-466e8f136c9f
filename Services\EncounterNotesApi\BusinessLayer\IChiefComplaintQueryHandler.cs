﻿namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public interface IChiefComplaintQueryHandler<T> where T : class
    {
        Task<IEnumerable<T>> GetAllChiefComplaints(Guid OrgId, bool Subscription);
        Task<T> GetChiefComplaintById(Guid id, Guid OrgId, bool Subscription);
        Task<IEnumerable<T>> GetChiefComplaintsByPatientId(Guid patientId, Guid OrgId, bool Subscription);
    }
}
