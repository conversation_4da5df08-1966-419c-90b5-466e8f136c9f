﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesContracts
{
    public class ReviewOfSystem
    {
        public Guid PatientId { get; set; }
        public Guid ReviewOfSystemId { get; set; }
        public Guid OrganizationId { get; set; }
        public Guid PcpId { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string? Decription { get; set; }
        public bool? IsActive { get; set; }
        public bool? RunnyNose { get; set; }
        public bool? Congestion { get; set; }
        public bool? ShortnessOfBreath { get; set; }
        public bool? ChestPain { get; set; }
        public bool? ItchyEyes { get; set; }
        public bool Subscription { get; set; }

    }
}
