using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IPageRoleMappingQueryHandler<T>
    {
        Task<T> GetPageRoleMappingByIdAsync(Guid id, Guid OrganizationId, bool Subscription);
        Task<List<T>> GetPageRoleMappingsByPagePathAsync(string pagePath, Guid OrganizationId, bool Subscription);
        Task<List<T>> GetAllPageRoleMappingsAsync();
        Task<List<T>> GetPagesByRoleIdAsync(Guid roleId, Guid OrganizationId, bool Subscription);
        Task<List<string>> GetRolesByPagePathAsync(string pagePath, Guid OrganizationId, bool Subscription);
    }
}
