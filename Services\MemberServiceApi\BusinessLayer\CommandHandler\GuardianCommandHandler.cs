﻿using Contracts;
using MemberServiceBusinessLayer;
using MemberServiceDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class GuardianCommandHandler : IGuardianCommandHandler<Guardian>
    {
        private readonly IUnitOfWork _unitOfWork;

        public GuardianCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddGuardianAsync(List<Guardian> Guardian, Guid OrgID, bool Subscription)
        {
            await _unitOfWork.GuardianRepository.AddAsync(Guardian, OrgID, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateGuardianAsync(Guardian Guardian, Guid OrgID, bool Subscription)
        {
            await _unitOfWork.GuardianRepository.UpdateAsync(Guardian, OrgID, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteGuardianAsync(Guid id, Guid OrgID, bool Subscription)
        {
            await _unitOfWork.GuardianRepository.DeleteByIdAsync(id, OrgID, Subscription);
            await _unitOfWork.SaveAsync();
        }
    }
}
