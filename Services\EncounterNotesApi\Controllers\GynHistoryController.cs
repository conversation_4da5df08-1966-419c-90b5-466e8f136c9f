﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using Microsoft.AspNetCore.Authorization;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.Identity.Web.Resource;
using System.Text.Json;
using System.Text;
using System.Text.Json.Serialization;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using ShardModels;

namespace EncounterNotesApi.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class GynHistoryController : ControllerBase
    {
        private readonly IGynHistoryCommandHandler<GynHistory> _gynHistoryCommandHandler;
        private readonly IGynHistoryQueryHandler<GynHistory> _gynHistoryQueryHandler;
        private readonly ILogger<GynHistoryController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;
        private readonly JsonSerializerOptions _jsonOptions;

        private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(10);
        private readonly TimeSpan _suggestionCacheExpiration = TimeSpan.FromMinutes(5);
        private JsonDocument content;

        /// <summary>
        /// Initializes a new instance of the <see cref="GynHistoryController"/> class.
        /// </summary>
        /// <param name="gynHistoryQueryHandler">The GYN history query handler.</param>
        /// <param name="gynHistoryCommandHandler">The GYN history command handler.</param>
        /// <param name="logger">The logger instance.</param>
        /// <param name="localizer">The localizer for the application strings.</param>
        public GynHistoryController(
            IGynHistoryQueryHandler<GynHistory> gynHistoryQueryHandler,
            IGynHistoryCommandHandler<GynHistory> gynHistoryCommandHandler,
            ILogger<GynHistoryController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer)
        {
            _gynHistoryCommandHandler = gynHistoryCommandHandler;
            _gynHistoryQueryHandler = gynHistoryQueryHandler;
            _logger = logger;
            _localizer = localizer;
            _jsonOptions = new JsonSerializerOptions
            {
                ReferenceHandler = ReferenceHandler.IgnoreCycles,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
        }

        /// <summary>
        /// Creates a new GYN history record.
        /// </summary>
        /// <param name="GynHistoryDto">The GYN history data transfer object.</param>
        /// <returns>The action result indicating success or failure.</returns>

        [HttpPost]
        [Route("{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> CreateGynHistory([FromBody] GynHistory gynHistoryDto, Guid orgId, bool isSubscription)
        {
            try
            {
                var gynHistory = new GynHistory
                {
                    gynId = Guid.NewGuid(),
                    PatientId = gynHistoryDto.PatientId,
                    Notes = gynHistoryDto.Notes,
                    Symptoms = gynHistoryDto.Symptoms,
                    DateOfHistory = gynHistoryDto.DateOfHistory, // Added line
                    OrganizationId = gynHistoryDto.OrganizationId,
                    PcpId = gynHistoryDto.PcpId
                };

                await _gynHistoryCommandHandler.AddAsync(new List<GynHistory> { gynHistory }, orgId, isSubscription);
                //return CreatedAtAction(nameof(GetGynHistoryById), new { id = gynHistory.gynId }, gynHistory);
                return CreatedAtAction(
                    nameof(GetGynHistoryById),
                    new { id = gynHistory.gynId, orgId = orgId, isSubscription = isSubscription },
                    gynHistory
                );

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorCreatingGynHistory"]);
                return StatusCode(500, _localizer["ErrorCreatingGynHistory"]);
            }
        }

        /// <summary>
        /// Retrieves an GYN history by its ID.
        /// </summary>
        /// <param name="id">The GYN history ID.</param>
        /// <returns>The action result containing the GYN history or an error message.</returns>
        [HttpGet("{id}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> GetGynHistoryById(Guid id, Guid orgId, bool isSubscription)
        {
            try
            {
                var gynHistory = await _gynHistoryQueryHandler.GetByIdAsync(id, orgId, isSubscription);
                if (gynHistory == null)
                {
                    return NotFound(_localizer["GynHistoryNotFound"]);
                }

                return Ok(gynHistory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingGynHistory"]);
                return BadRequest(_localizer["ErrorFetchingGynHistory"]);
            }
        }

        /// <summary>
        /// Retrieves all GYN histories for a specific patient.
        /// </summary>
        /// <param name="patientId">The patient ID.</param>
        /// <returns>The action result containing a list of GYN histories.</returns>
        [HttpGet("patient/{patientId}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> GetByPatientIdAsync(Guid patientId, Guid orgId, bool isSubscription)
        {
            try
            {
                var gynHistories = await _gynHistoryQueryHandler.GetByPatientIdAsync(patientId, orgId, isSubscription);
                return Ok(gynHistories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingGynHistories"]);
                return BadRequest(_localizer["ErrorFetchingGynHistories"]);
            }
        }

        /// <summary>
        /// Deletes an GYN history record by its ID.
        /// </summary>
        /// <param name="id">The GYN history ID.</param>
        /// <returns>The action result indicating success or failure.</returns>
        [HttpDelete("{id}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> DeleteGynHistory(Guid id, Guid orgId, bool isSubscription)
        {
            try
            {
                var gynHistory = await _gynHistoryQueryHandler.GetByIdAsync(id, orgId, isSubscription);
                if (gynHistory == null)
                {
                    return NotFound(_localizer["GynHistoryNotFound"]);
                }

                await _gynHistoryCommandHandler.DeleteByIdAsync(id, orgId, isSubscription);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingGynHistory"]);
                return BadRequest(_localizer["ErrorDeletingGynHistory"]);
            }
        }

        /// <summary>
        /// Bulk updates a list of GYN history records.
        /// </summary>
        /// <param name="gynHistories">The list of GYN history records to update.</param>
        /// <returns>The action result indicating success or failure.</returns>
        [HttpPut("bulk-update/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateGynHistoryList([FromBody] List<GynHistory> gynHistories, Guid orgId, bool isSubscription)
        {
            if (gynHistories == null || !gynHistories.Any())
            {
                return BadRequest("Invalid records provided.");
            }

            try
            {
                await _gynHistoryCommandHandler.UpdateGynHistoryListAsync(gynHistories, orgId, isSubscription);
                return Ok("Bulk update successful");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating GynHistory list");
                return StatusCode(500, "An error occurred while updating the GynHistory list.");
            }
        }

        /// <summary>
        /// Updates an existing GYN history record by its ID.
        /// </summary>
        /// <param name="id">The GYN history ID.</param>
        /// <param name="gynHistoryDto">The GYN history data transfer object.</param>
        /// <returns>The action result containing the updated GYN history or an error message.</returns>
        [HttpPut("{id}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateGynHistory(Guid id, [FromBody] GynHistory gynHistoryDto, Guid orgId, bool isSubscription)
        {
            try
            {
                var existingGynHistory = await _gynHistoryQueryHandler.GetByIdAsync(id, orgId, isSubscription);
                if (existingGynHistory == null)
                {
                    return NotFound(_localizer["GynHistoryNotFound"]);
                }

                existingGynHistory.Notes = gynHistoryDto.Notes;
                existingGynHistory.Symptoms = gynHistoryDto.Symptoms;
                existingGynHistory.DateOfHistory = gynHistoryDto.DateOfHistory; // Added line
                existingGynHistory.OrganizationId = gynHistoryDto.OrganizationId;
                existingGynHistory.PcpId = gynHistoryDto.PcpId;
                await _gynHistoryCommandHandler.UpdateAsync(existingGynHistory, orgId, isSubscription);

                return Ok(existingGynHistory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingGynHistory"]);
                return StatusCode(500, _localizer["ErrorUpdatingGynHistory"]);
            }
        }

    }
}
