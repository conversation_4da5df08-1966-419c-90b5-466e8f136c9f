﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IDiagnosticImagingPageRepository : IShardGenericRepository<DiagnosticImagingDTO>
    {
        Task<List<DiagnosticImagingDTO>> GetDiagnosticImagingById(Guid recordId, Guid OrgId, bool Subscription);
        Task<List<DiagnosticImagingDTO>> GetDiagnosticImagingByIdAndIsActive(Guid recordId, Guid OrgId, bool Subscription);

    }
}
