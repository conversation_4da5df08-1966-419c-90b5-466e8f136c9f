﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Azure.Messaging.ServiceBus;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class HospitalizationRecordQueryHandler : IHospitalizationRecordQueryHandler<HospitalizationRecord>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public HospitalizationRecordQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get All HospitalizationRecord by Id
        /// </summary>
        public async Task<List<HospitalizationRecord>> GetHospitalizationRecordById(Guid id, Guid OrgId, bool Subscription)
        {
            try
            {
                return await _unitOfWork.HospitalizationRecordRepository.GetHospitalizationRecordById(id, OrgId, Subscription);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return new List<HospitalizationRecord>();
            }
        }

        /// <summary>
        /// Get Active HospitalizationRecord by Id
        /// </summary>
        public async Task<IEnumerable<HospitalizationRecord>> GetHospitalizationRecordByIdAndIsActive(Guid id, Guid OrgId, bool Subscription)
        {
            var hospitalizationRecords = await _unitOfWork.HospitalizationRecordRepository.GetHospitalizationRecordById(id, OrgId, Subscription);

            return hospitalizationRecords.Where(hospitalization => hospitalization.PatientId == id && hospitalization.IsActive == true);
        }
    }
}
