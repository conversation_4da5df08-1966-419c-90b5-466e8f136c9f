using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class LabTestsQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILabTestsRepository> _labTestsRepositoryMock;
        private LabTestsQueryHandler _labTestsQueryHandler;
        private Guid _patientId;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _labTestsRepositoryMock = new Mock<ILabTestsRepository>();
            _patientId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.LabTestsRepository).Returns(_labTestsRepositoryMock.Object);
            _labTestsQueryHandler = new LabTestsQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetAllLabTestsById_ShouldReturnAllLabTestsForPatient()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var pcpId = Guid.NewGuid();
            var assessmentId = Guid.NewGuid();
            
            var labTests = new List<LabTests>
            {
                new LabTests
                {
                    LabTestsId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = orgId,
                    PcpId = pcpId,
                    CreatedDate = DateTime.Now.AddDays(-1),
                    LabTest1 = "Complete Blood Count",
                    LabTest2 = "Lipid Panel",
                    TestOrganization = "Test Lab",
                    IsActive = true,
                    AssessmentData = "Assessment data",
                    AssessmentId = assessmentId
                },
                new LabTests
                {
                    LabTestsId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = orgId,
                    PcpId = pcpId,
                    CreatedDate = DateTime.Now.AddDays(-2),
                    LabTest1 = "Urinalysis",
                    LabTest2 = "Thyroid Panel",
                    TestOrganization = "Test Lab",
                    IsActive = true,
                    AssessmentData = "Assessment data",
                    AssessmentId = assessmentId
                },
                new LabTests
                {
                    LabTestsId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = orgId,
                    PcpId = pcpId,
                    CreatedDate = DateTime.Now.AddDays(-3),
                    LabTest1 = "Glucose Test",
                    LabTest2 = "HbA1c",
                    TestOrganization = "Test Lab",
                    IsActive = true,
                    AssessmentData = "Assessment data",
                    AssessmentId = assessmentId
                }
            };

            _labTestsRepositoryMock.Setup(r => r.GetAllLabTestsAsync())
                .ReturnsAsync(labTests);

            // Act
            var result = await _labTestsQueryHandler.GetAllLabTestsById(_patientId);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(lt => lt.PatientId == _patientId).Should().BeTrue();
        }

        [Test]
        public async Task GetAllLabTestsById_WithNoLabTests_ShouldReturnEmptyCollection()
        {
            // Arrange
            var labTests = new List<LabTests>();

            _labTestsRepositoryMock.Setup(r => r.GetAllLabTestsAsync())
                .ReturnsAsync(labTests);

            // Act
            var result = await _labTestsQueryHandler.GetAllLabTestsById(_patientId);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetAllLabTestsById_WithNoMatchingPatient_ShouldReturnEmptyCollection()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var pcpId = Guid.NewGuid();
            var assessmentId = Guid.NewGuid();
            var differentPatientId = Guid.NewGuid();
            
            var labTests = new List<LabTests>
            {
                new LabTests
                {
                    LabTestsId = Guid.NewGuid(),
                    PatientId = differentPatientId,
                    OrganizationId = orgId,
                    PcpId = pcpId,
                    CreatedDate = DateTime.Now.AddDays(-1),
                    LabTest1 = "Complete Blood Count",
                    LabTest2 = "Lipid Panel",
                    TestOrganization = "Test Lab",
                    IsActive = true,
                    AssessmentData = "Assessment data",
                    AssessmentId = assessmentId
                }
            };

            _labTestsRepositoryMock.Setup(r => r.GetAllLabTestsAsync())
                .ReturnsAsync(labTests);

            // Act
            var result = await _labTestsQueryHandler.GetAllLabTestsById(_patientId);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetAllLabTestsById_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            _labTestsRepositoryMock.Setup(r => r.GetAllLabTestsAsync())
                .ThrowsAsync(new Exception("Database connection error"));

            // Act & Assert
            await FluentActions.Invoking(async () => await _labTestsQueryHandler.GetAllLabTestsById(_patientId))
                .Should().ThrowAsync<Exception>()
                .WithMessage("Database connection error");
        }
    }
}
