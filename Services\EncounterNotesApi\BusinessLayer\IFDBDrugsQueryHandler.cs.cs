﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IFDBDrugsQueryHandler
    {

        Task<IEnumerable<FDBMedication>> GetAllMedications(string ROUTED_DOSAGE_FORM_MED_ID);
        Task<IEnumerable<FDBMedicationName>> GetAllMedicationNames();
        Task<IEnumerable<FDBRoutedMedication>> GetAllRoutedMedication(string MED_NAME_ID);
        Task<IEnumerable<FDBRoutedDosageFormMedication>> GetAllRoutedDosageFormMedication(string ROUTED_MED_ID);
        Task<IEnumerable<FDBRouteLookUp>> GetAllRouteForms();
        Task<IEnumerable<FDBTakeLookUp>> GetAllTakeForms();
        Task<IEnumerable<FDB_ICD>> GetAllICD();
        Task<IEnumerable<FDBAllergies>> GetAllAllergyNames();
        Task<IEnumerable<FDBVaccines>> GetAllVaccineNames();
        Task<FDBVaccine_CPT_CVX> GetCPT_CVX_Vaccine(string CVX);
        Task<IEnumerable<RxNormConcept>> GetAllRxConceptMedication();
        Task<IEnumerable<RxNormConcept>> GetAllRxConceptMedicationSBDC(string str);
    }
}
