﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IVisionExaminationCommandHandler<TText>
    {
        Task AddVisionExamination(List<TText> visionExaminationList, Guid OrgID, bool Subscription);
        Task DeleteVisionRecordById(Guid id, Guid OrgID, bool Subscription);
        Task UpdateVisionRecord(VisionRx VisionExaminationList, Guid OrgID, bool Subscription);

    }
}

