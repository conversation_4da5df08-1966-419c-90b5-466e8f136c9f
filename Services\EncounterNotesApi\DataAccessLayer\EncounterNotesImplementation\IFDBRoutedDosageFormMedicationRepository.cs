﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IFDBRoutedDosageFormMedicationRepository : IGenericRepository<FDBRoutedDosageFormMedication>
    {
        Task<List<FDBRoutedDosageFormMedication>> GetRoutedDosageFormMedicationByROUTED_MED_ID(string ROUTED_MED_ID);
    }
}
