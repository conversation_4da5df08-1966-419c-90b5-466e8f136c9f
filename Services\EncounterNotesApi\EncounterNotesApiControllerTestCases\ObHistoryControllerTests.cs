using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesApi.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using System.Text.Json;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class ObHistoryControllerTests
    {
        private Mock<IObHistoryCommandHandler<ObHistory>> _obHistoryCommandHandlerMock;
        private Mock<IObHistoryQueryHandler<ObHistory>> _obHistoryQueryHandlerMock;
        private Mock<ILogger<ObHistoryController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private ObHistoryController _controller;
        private List<ObHistory> _testObHistories;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _obHistoryCommandHandlerMock = new Mock<IObHistoryCommandHandler<ObHistory>>();
            _obHistoryQueryHandlerMock = new Mock<IObHistoryQueryHandler<ObHistory>>();
            _loggerMock = new Mock<ILogger<ObHistoryController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["ErrorFetchingObHistories"]).Returns(new LocalizedString("ErrorFetchingObHistories", "Error fetching OB histories."));
            _localizerMock.Setup(x => x["ErrorFetchingObHistory"]).Returns(new LocalizedString("ErrorFetchingObHistory", "Error fetching OB history."));
            _localizerMock.Setup(x => x["ObHistoryNotFound"]).Returns(new LocalizedString("ObHistoryNotFound", "OB history not found."));
            _localizerMock.Setup(x => x["ErrorUpdatingObHistory"]).Returns(new LocalizedString("ErrorUpdatingObHistory", "Error updating OB history."));
            _localizerMock.Setup(x => x["ErrorDeletingObHistory"]).Returns(new LocalizedString("ErrorDeletingObHistory", "Error deleting OB history."));

            _testObHistories = new List<ObHistory>
            {
                new ObHistory {
                    obId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Symptoms = "Morning sickness, fatigue",
                    Notes = "First trimester symptoms",
                    DateOfComplaint = DateTime.Now.AddDays(-30),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Subscription = _isSubscription,
                    IsDeleted = false
                },
                new ObHistory {
                    obId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Symptoms = "Back pain, swelling",
                    Notes = "Third trimester symptoms",
                    DateOfComplaint = DateTime.Now.AddDays(-10),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Subscription = _isSubscription,
                    IsDeleted = false
                }
            };

            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            };

            _controller = new ObHistoryController(
                _obHistoryQueryHandlerMock.Object,
                _obHistoryCommandHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetByPatientIdAsync_ReturnsObHistories_WhenObHistoriesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientObHistories = _testObHistories.Select(h => { h.PatientId = patientId; return h; }).ToList();
            _obHistoryQueryHandlerMock.Setup(x => x.GetByPatientIdAsync(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientObHistories);

            // Act
            var result = await _controller.GetByPatientIdAsync(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedObHistories = okResult.Value as IEnumerable<ObHistory>;
            returnedObHistories.Should().NotBeNull();
            returnedObHistories.Should().BeEquivalentTo(patientObHistories);
        }

        [Test]
        public async Task GetByPatientIdAsync_ExceptionThrown_ReturnsBadRequest()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _obHistoryQueryHandlerMock.Setup(x => x.GetByPatientIdAsync(patientId, _orgId, _isSubscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetByPatientIdAsync(patientId, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["ErrorFetchingObHistories"]);
        }

        [Test]
        public async Task GetObHistoryById_ReturnsObHistory_WhenObHistoryExists()
        {
            // Arrange
            var obHistoryId = _testObHistories[0].obId;
            _obHistoryQueryHandlerMock.Setup(x => x.GetByIdAsync(obHistoryId, _orgId, _isSubscription))
                .ReturnsAsync(_testObHistories[0]);

            // Act
            var result = await _controller.GetObHistoryById(obHistoryId, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedObHistory = okResult.Value as ObHistory;
            returnedObHistory.Should().NotBeNull();
            returnedObHistory.Should().BeEquivalentTo(_testObHistories[0]);
        }

        [Test]
        public async Task GetObHistoryById_ObHistoryNotFound_ReturnsNotFound()
        {
            // Arrange
            var obHistoryId = Guid.NewGuid();
            _obHistoryQueryHandlerMock.Setup(x => x.GetByIdAsync(obHistoryId, _orgId, _isSubscription))
                .ReturnsAsync((ObHistory)null);

            // Act
            var result = await _controller.GetObHistoryById(obHistoryId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["ObHistoryNotFound"]);
        }

        [Test]
        public async Task CreateObHistory_ValidObHistory_ReturnsCreatedAtAction()
        {
            // Arrange
            var newObHistory = new ObHistory
            {
                PatientId = Guid.NewGuid(),
                Symptoms = "Nausea, vomiting",
                Notes = "Early pregnancy symptoms",
                DateOfComplaint = DateTime.Now,
                OrganizationId = _orgId,
                PcpId = Guid.NewGuid()
            };

            // Act
            var result = await _controller.CreateObHistory(newObHistory, _orgId, _isSubscription);

            // Assert
            var createdAtActionResult = result as CreatedAtActionResult;
            createdAtActionResult.Should().NotBeNull();
            createdAtActionResult.StatusCode.Should().Be(201);
            createdAtActionResult.ActionName.Should().Be(nameof(ObHistoryController.GetObHistoryById));

            var returnedObHistory = createdAtActionResult.Value as ObHistory;
            returnedObHistory.Should().NotBeNull();
            returnedObHistory.PatientId.Should().Be(newObHistory.PatientId);
            returnedObHistory.Symptoms.Should().Be(newObHistory.Symptoms);
            returnedObHistory.Notes.Should().Be(newObHistory.Notes);

            // Verify the command handler was called
            _obHistoryCommandHandlerMock.Verify(x => x.AddAsync(It.IsAny<IEnumerable<ObHistory>>(), _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateObHistory_ValidObHistory_ReturnsOk()
        {
            // Arrange
            var existingObHistory = _testObHistories[0];
            var obHistoryToUpdate = new ObHistory
            {
                obId = existingObHistory.obId,
                PatientId = existingObHistory.PatientId,
                Symptoms = "Updated symptoms",
                Notes = "Updated notes",
                DateOfComplaint = DateTime.Now,
                OrganizationId = existingObHistory.OrganizationId,
                PcpId = existingObHistory.PcpId
            };

            _obHistoryQueryHandlerMock.Setup(x => x.GetByIdAsync(existingObHistory.obId, _orgId, _isSubscription))
                .ReturnsAsync(existingObHistory);

            // Act
            var result = await _controller.UpdateObHistory(existingObHistory.obId, obHistoryToUpdate, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            // Verify the command handler was called
            _obHistoryCommandHandlerMock.Verify(x => x.UpdateAsync(It.IsAny<ObHistory>(), _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task DeleteObHistory_ValidId_ReturnsNoContent()
        {
            // Arrange
            var obHistoryId = _testObHistories[0].obId;
            _obHistoryQueryHandlerMock.Setup(x => x.GetByIdAsync(obHistoryId, _orgId, _isSubscription))
                .ReturnsAsync(_testObHistories[0]);

            // Act
            var result = await _controller.DeleteObHistory(obHistoryId, _orgId, _isSubscription);

            // Assert
            var noContentResult = result as NoContentResult;
            noContentResult.Should().NotBeNull();
            noContentResult.StatusCode.Should().Be(204);

            // Verify the command handler was called
            _obHistoryCommandHandlerMock.Verify(x => x.DeleteByIdAsync(obHistoryId, _orgId, _isSubscription), Times.Once);
        }
    }
}
