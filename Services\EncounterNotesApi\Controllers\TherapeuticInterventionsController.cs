﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using System;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class TherapeuticInterventionsController : ControllerBase
    {
        private readonly ITherapeuticInterventionsCommandHandler<TherapeuticInterventionsData> _TherapeuticInterventionsDataHandler;
        private readonly ITherapeuticInterventionsQueryHandler<TherapeuticInterventionsData> _TherapeuticInterventionsQueryHandler;
        private readonly ILogger<TherapeuticInterventionsController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;

        public TherapeuticInterventionsController(
            ITherapeuticInterventionsCommandHandler<TherapeuticInterventionsData> medicalDataHandler,
            ITherapeuticInterventionsQueryHandler<TherapeuticInterventionsData> medicalQueryHandler,
            ILogger<TherapeuticInterventionsController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
        )
        {
            _TherapeuticInterventionsDataHandler = medicalDataHandler;
            _TherapeuticInterventionsQueryHandler = medicalQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        [HttpGet("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<TherapeuticInterventionsData>>> GetAllById(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult<IEnumerable<TherapeuticInterventionsData>> result;

            try
            {
                var data = await _TherapeuticInterventionsQueryHandler.GetAllTherapeuticInterventionsById(id, orgId, isSubscription);
                result = data != null ? Ok(data) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(500, _localizer["GetLogError"]);
            }

            return result;
        }

        [HttpGet("{id:guid}/IsActive/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<TherapeuticInterventionsData>>> GetAllByIdAndIsActive(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult<IEnumerable<TherapeuticInterventionsData>> result;

            try
            {
                var diagnosis = await _TherapeuticInterventionsQueryHandler.GetTherapeuticInterventionsByIdAndIsActive(id, orgId, isSubscription);
                result = diagnosis != null ? Ok(diagnosis) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(500, _localizer["GetLogError"]);
            }

            return result;
        }

        [HttpPost]
        [Route("AddTherapeuticInterventions/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> AddTherapeuticInterventions([FromBody] List<TherapeuticInterventionsData> _TherapeuticInterventions, Guid orgId, bool isSubscription)
        {
            IActionResult result;

            if (_TherapeuticInterventions == null || _TherapeuticInterventions.Count == 0)
            {
                result = BadRequest(_localizer["NoLicense"]);
            }
            else
            {
                try
                {
                    await _TherapeuticInterventionsDataHandler.AddTherapeuticInterventions(_TherapeuticInterventions, orgId, isSubscription);
                    result = Ok(_localizer["SuccessfulRegistration"]);
                }
                catch (SqlException)
                {
                    result = StatusCode(500, _localizer["DatabaseError"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["PostLogError"]);
                    result = StatusCode(500);
                }
            }

            return result;
        }

        [HttpDelete]
        [Route("{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> DeleteByEntity([FromBody] TherapeuticInterventionsData _TherapeuticInterventions, Guid orgId, bool isSubscription)
        {
            IActionResult result;

            if (_TherapeuticInterventions == null)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _TherapeuticInterventionsDataHandler.DeleteTherapeuticInterventionsByEntity(_TherapeuticInterventions, orgId, isSubscription);
                    result = Ok(_localizer["DeleteSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["DeleteLogError"]);
                    result = StatusCode(500, _localizer["DeleteLogError"]);
                }
            }

            return result;
        }

        [HttpPut("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateTherapeuticInterventionsById(Guid id, [FromBody] TherapeuticInterventionsData _TherapeuticInterventions, Guid orgId, bool isSubscription)
        {
            IActionResult result;

            if (_TherapeuticInterventions == null || _TherapeuticInterventions.PatientId != id)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _TherapeuticInterventionsDataHandler.UpdateTherapeuticInterventions(_TherapeuticInterventions, orgId, isSubscription);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(500, _localizer["UpdateLogError"]);
                }
            }

            return result;
        }

        [HttpPut]
        [Route("UpdateTherapeuticInterventionsList/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateTherapeuticInterventionsList([FromBody] List<TherapeuticInterventionsData> _TherapeuticInterventions, Guid orgId, bool isSubscription)
        {
            IActionResult result;

            try
            {
                await _TherapeuticInterventionsDataHandler.UpdateTherapeuticInterventionsList(_TherapeuticInterventions, orgId, isSubscription);
                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                result = StatusCode(500, _localizer["UpdateLogError"]);
            }

            return result;
        }

    }
}