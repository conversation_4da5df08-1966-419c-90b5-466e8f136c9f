using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class PhysicalExaminationControllerTests
    {
        private Mock<IPhysicalExaminationCommandHandler<PhysicalExamination>> _physicalExaminationCommandHandlerMock;
        private Mock<IPhysicalExaminationQueryHandler<PhysicalExamination>> _physicalExaminationQueryHandlerMock;
        private Mock<ILogger<PhysicalExaminationController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private PhysicalExaminationController _controller;
        private List<PhysicalExamination> _testPhysicalExaminations;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _physicalExaminationCommandHandlerMock = new Mock<IPhysicalExaminationCommandHandler<PhysicalExamination>>();
            _physicalExaminationQueryHandlerMock = new Mock<IPhysicalExaminationQueryHandler<PhysicalExamination>>();
            _loggerMock = new Mock<ILogger<PhysicalExaminationController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error fetching physical examinations."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Physical examination not found."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No physical examinations provided."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Physical examinations added successfully."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error adding physical examinations."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Physical examination deleted successfully."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting physical examination."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid physical examination record."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Physical examination updated successfully."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating physical examination."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));

            _testPhysicalExaminations = new List<PhysicalExamination>
            {
                new PhysicalExamination {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Skin = "Normal",
                    Rash = "None",
                    Tester = "Dr. Smith",
                    Moles = "Few on back",
                    Hemangioma = "None",
                    VascularMalformation = "None",
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    Subscription = _isSubscription
                },
                new PhysicalExamination {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-15),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Skin = "Dry",
                    Rash = "Small on arm",
                    Tester = "Dr. Johnson",
                    Moles = "None",
                    Hemangioma = "Small on chest",
                    VascularMalformation = "None",
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    Subscription = _isSubscription
                }
            };

            _controller = new PhysicalExaminationController(
                _physicalExaminationCommandHandlerMock.Object,
                _physicalExaminationQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetAllById_ReturnsPhysicalExaminations_WhenExaminationsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientExaminations = _testPhysicalExaminations.Select(e => { e.PatientId = patientId; return e; }).ToList();
            _physicalExaminationQueryHandlerMock.Setup(x => x.GetAllExaminationsById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientExaminations);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedExaminations = okResult.Value as IEnumerable<PhysicalExamination>;
            returnedExaminations.Should().NotBeNull();
            returnedExaminations.Should().BeEquivalentTo(patientExaminations);
        }

        [Test]
        public async Task GetAllById_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _physicalExaminationQueryHandlerMock.Setup(x => x.GetAllExaminationsById(patientId, _orgId, _isSubscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var statusCodeResult = result.Result as ObjectResult;
            statusCodeResult.Should().NotBeNull();
            statusCodeResult.StatusCode.Should().Be(500);
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsActivePhysicalExaminations_WhenExaminationsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activeExaminations = _testPhysicalExaminations.Select(e => { e.PatientId = patientId; e.IsActive = true; return e; }).ToList();
            _physicalExaminationQueryHandlerMock.Setup(x => x.GetExaminationByIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activeExaminations);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedExaminations = okResult.Value as IEnumerable<PhysicalExamination>;
            returnedExaminations.Should().NotBeNull();
            returnedExaminations.Should().BeEquivalentTo(activeExaminations);
        }

        [Test]
        public async Task Registration_ValidExaminations_ReturnsOk()
        {
            // Arrange
            var examinationsToAdd = _testPhysicalExaminations;

            // Act
            var result = await _controller.Registration(examinationsToAdd, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _physicalExaminationCommandHandlerMock.Verify(x => x.AddExamination(examinationsToAdd, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task Registration_EmptyExaminations_ReturnsOk()
        {
            // Arrange
            var emptyExaminations = new List<PhysicalExamination>();

            // Act
            var result = await _controller.Registration(emptyExaminations, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);

            // Verify the command handler was not called
            _physicalExaminationCommandHandlerMock.Verify(x => x.AddExamination(It.IsAny<List<PhysicalExamination>>(), _orgId, _isSubscription), Times.Never);
        }

        [Test]
        public async Task DeleteByEntity_ValidExamination_ReturnsOk()
        {
            // Arrange
            var examinationToDelete = _testPhysicalExaminations[0];

            // Act
            var result = await _controller.DeleteByEntity(examinationToDelete, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _physicalExaminationCommandHandlerMock.Verify(x => x.DeleteExaminationByEntity(examinationToDelete, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateExaminationById_ValidExamination_ReturnsOk()
        {
            // Arrange
            var examinationToUpdate = _testPhysicalExaminations[0];
            var patientId = examinationToUpdate.PatientId; // Use PatientId instead of ExaminationId

            // Act
            var result = await _controller.UpdateExaminationById(patientId, examinationToUpdate, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _physicalExaminationCommandHandlerMock.Verify(x => x.UpdateExamination(examinationToUpdate, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateExaminationById_IdMismatch_ReturnsBadRequest()
        {
            // Arrange
            var examinationToUpdate = _testPhysicalExaminations[0];
            var differentId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdateExaminationById(differentId, examinationToUpdate, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);

            // Verify the command handler was not called
            _physicalExaminationCommandHandlerMock.Verify(x => x.UpdateExamination(It.IsAny<PhysicalExamination>(), _orgId, _isSubscription), Times.Never);
        }


    }
}
