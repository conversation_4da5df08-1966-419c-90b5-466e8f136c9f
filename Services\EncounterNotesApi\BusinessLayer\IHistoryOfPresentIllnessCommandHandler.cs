﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface IHistoryOfPresentIllnessCommandHandler<T>
    {
        Task AddHistoryOfPresentIllness(List<T> records, Guid OrgId, bool Subscription);
        Task UpdateHistoryOfPresentIllness(T record, Guid OrgId, bool Subscription);
        Task DeleteHistoryOfPresentIllnessById(Guid id, Guid OrgId, bool Subscription);
        Task DeleteHistoryOfPresentIllnessByEntity(T record, Guid OrgId, bool Subscription);
        Task UpdateHistoryOfPresentIllnessBulk(List<T> records, Guid OrgId, bool Subscription);
    }
}
