using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class ExaminationQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IExaminationRepository> _examinationRepositoryMock;
        private ExaminationQueryHandler _examinationQueryHandler;
        private Guid _orgId;
        private bool _subscription;
        private Guid _patientId;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _examinationRepositoryMock = new Mock<IExaminationRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;
            _patientId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.ExaminationRepository).Returns(_examinationRepositoryMock.Object);
            _examinationQueryHandler = new ExaminationQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetExaminationByPatientId_ShouldReturnAllExaminationsForPatient()
        {
            // Arrange
            var examinations = new List<Examination>
            {
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdateDate = DateTime.Now.AddDays(-25),
                    GeneralDescription = "Normal appearance",
                    HEENT = "Normal",
                    Lungs = "Clear",
                    Abdomen = "Soft, non-tender",
                    PeripheralPulses = "Normal",
                    Skin = "Normal",
                    Others = "None",
                    Subscription = _subscription
                },
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    IsActive = false,
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdateDate = DateTime.Now.AddDays(-55),
                    GeneralDescription = "Appears well",
                    HEENT = "Normal",
                    Lungs = "Wheezing noted",
                    Abdomen = "Soft, non-tender",
                    PeripheralPulses = "Normal",
                    Skin = "Dry",
                    Others = "None",
                    Subscription = _subscription
                },
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-45),
                    UpdateDate = DateTime.Now.AddDays(-40),
                    GeneralDescription = "Normal appearance",
                    HEENT = "Normal",
                    Lungs = "Clear",
                    Abdomen = "Soft, non-tender",
                    PeripheralPulses = "Normal",
                    Skin = "Normal",
                    Others = "None",
                    Subscription = _subscription
                }
            };

            _examinationRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(examinations);

            // Act
            var result = await _examinationQueryHandler.GetExaminationByPatientId(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(e => e.PatientId == _patientId).Should().BeTrue();
        }

        [Test]
        public async Task GetExaminationByPatientId_WithNoExaminations_ShouldReturnEmptyCollection()
        {
            // Arrange
            var examinations = new List<Examination>();

            _examinationRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(examinations);

            // Act
            var result = await _examinationQueryHandler.GetExaminationByPatientId(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetExaminationByPatientIdAndIsActive_ShouldReturnActiveExaminationsForPatient()
        {
            // Arrange
            var examinations = new List<Examination>
            {
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdateDate = DateTime.Now.AddDays(-25),
                    GeneralDescription = "Normal appearance",
                    HEENT = "Normal",
                    Lungs = "Clear",
                    Abdomen = "Soft, non-tender",
                    PeripheralPulses = "Normal",
                    Skin = "Normal",
                    Others = "None",
                    Subscription = _subscription
                },
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    IsActive = false, // Inactive
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdateDate = DateTime.Now.AddDays(-55),
                    GeneralDescription = "Appears well",
                    HEENT = "Normal",
                    Lungs = "Wheezing noted",
                    Abdomen = "Soft, non-tender",
                    PeripheralPulses = "Normal",
                    Skin = "Dry",
                    Others = "None",
                    Subscription = _subscription
                },
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-45),
                    UpdateDate = DateTime.Now.AddDays(-40),
                    GeneralDescription = "Normal appearance",
                    HEENT = "Normal",
                    Lungs = "Clear",
                    Abdomen = "Soft, non-tender",
                    PeripheralPulses = "Normal",
                    Skin = "Normal",
                    Others = "None",
                    Subscription = _subscription
                }
            };

            _examinationRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(examinations);

            // Act
            var result = await _examinationQueryHandler.GetExaminationByPatientIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.All(e => e.PatientId == _patientId && e.IsActive).Should().BeTrue();
        }

        [Test]
        public async Task GetExaminationByPatientIdAndIsActive_WithNoActiveExaminations_ShouldReturnEmptyCollection()
        {
            // Arrange
            var examinations = new List<Examination>
            {
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    IsActive = false, // Inactive
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdateDate = DateTime.Now.AddDays(-55),
                    GeneralDescription = "Appears well",
                    HEENT = "Normal",
                    Lungs = "Wheezing noted",
                    Abdomen = "Soft, non-tender",
                    PeripheralPulses = "Normal",
                    Skin = "Dry",
                    Others = "None",
                    Subscription = _subscription
                }
            };

            _examinationRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(examinations);

            // Act
            var result = await _examinationQueryHandler.GetExaminationByPatientIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
