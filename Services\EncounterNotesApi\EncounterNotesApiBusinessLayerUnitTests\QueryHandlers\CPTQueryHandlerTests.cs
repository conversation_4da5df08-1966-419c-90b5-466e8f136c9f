using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class CPTQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ICPTRepository> _cptRepositoryMock;
        private CPTQueryHandler _cptQueryHandler;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _cptRepositoryMock = new Mock<ICPTRepository>();

            _unitOfWorkMock.Setup(u => u.CPTRepository).Returns(_cptRepositoryMock.Object);
            _cptQueryHandler = new CPTQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetCPT_ShouldReturnAllCPTCodes()
        {
            // Arrange
            var cptCodes = new List<CPT>
            {
                new CPT
                {
                    CPTCode = "99201",
                    Description = "Office or other outpatient visit for the evaluation and management of a new patient"
                },
                new CPT
                {
                    CPTCode = "99202",
                    Description = "Office or other outpatient visit for the evaluation and management of a new patient"
                }
            };

            _cptRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(cptCodes);

            // Act
            var result = await _cptQueryHandler.GetCPT();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.Should().BeEquivalentTo(cptCodes);
        }

        [Test]
        public async Task GetCPT_WithNoCPTCodes_ShouldReturnEmptyCollection()
        {
            // Arrange
            var cptCodes = new List<CPT>();

            _cptRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(cptCodes);

            // Act
            var result = await _cptQueryHandler.GetCPT();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetCPT_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            _cptRepositoryMock.Setup(r => r.GetAsync())
                .ThrowsAsync(new Exception("Database connection error"));

            // Act & Assert
            await FluentActions.Invoking(async () => await _cptQueryHandler.GetCPT())
                .Should().ThrowAsync<Exception>()
                .WithMessage("Database connection error");
        }
    }
}
