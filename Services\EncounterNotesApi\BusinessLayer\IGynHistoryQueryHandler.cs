﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public interface IGynHistoryQueryHandler<T> where T : class
    {
        Task<IEnumerable<T>> GetAllAsync(Guid OrgId, bool Subscription);
        Task<T> GetByIdAsync(Guid id, Guid OrgId, bool Subscription);
        Task<IEnumerable<T>> GetByPatientIdAsync(Guid patientId, Guid OrgId, bool Subscription);
    }
}
