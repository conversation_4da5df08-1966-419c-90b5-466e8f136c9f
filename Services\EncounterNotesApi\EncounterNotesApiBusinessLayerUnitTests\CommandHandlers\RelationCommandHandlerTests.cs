using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class RelationCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<RelationCommandHandler>> _loggerMock;
        private Mock<IRelationRepository> _relationRepositoryMock;
        private RelationCommandHandler _relationCommandHandler;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<RelationCommandHandler>>();
            _relationRepositoryMock = new Mock<IRelationRepository>();

            _unitOfWorkMock.Setup(u => u.RelationRepository).Returns(_relationRepositoryMock.Object);
            _relationCommandHandler = new RelationCommandHandler(_configurationMock.Object, _unitOfWorkMock.Object, _loggerMock.Object);
        }

        [Test]
        public async Task AddRelation_ShouldAddRelationsToRepository()
        {
            // Arrange
            var relations = new List<Relations>
            {
                new Relations
                {
                    RelationId = Guid.NewGuid(),
                    Relation = "Spouse"
                },
                new Relations
                {
                    RelationId = Guid.NewGuid(),
                    Relation = "Parent"
                }
            };

            _relationRepositoryMock.Setup(r => r.AddAsync(It.IsAny<IEnumerable<Relations>>()))
                .Returns(Task.CompletedTask);
            
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _relationCommandHandler.AddRelation(relations);

            // Assert
            _relationRepositoryMock.Verify(r => r.AddAsync(It.Is<IEnumerable<Relations>>(s => 
                s.Count() == relations.Count() && 
                s.All(relation => relations.Any(x => x.RelationId == relation.RelationId && x.Relation == relation.Relation)))), 
                Times.Once);
            
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task AddRelation_WithEmptyList_ShouldStillCallRepositoryMethods()
        {
            // Arrange
            var relations = new List<Relations>();

            _relationRepositoryMock.Setup(r => r.AddAsync(It.IsAny<IEnumerable<Relations>>()))
                .Returns(Task.CompletedTask);
            
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _relationCommandHandler.AddRelation(relations);

            // Assert
            _relationRepositoryMock.Verify(r => r.AddAsync(It.Is<IEnumerable<Relations>>(s => !s.Any())), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task AddRelation_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            var relations = new List<Relations>
            {
                new Relations
                {
                    RelationId = Guid.NewGuid(),
                    Relation = "Spouse"
                }
            };

            _relationRepositoryMock.Setup(r => r.AddAsync(It.IsAny<IEnumerable<Relations>>()))
                .ThrowsAsync(new Exception("Database connection error"));

            // Act & Assert
            await FluentActions.Invoking(async () => await _relationCommandHandler.AddRelation(relations))
                .Should().ThrowAsync<Exception>()
                .WithMessage("Database connection error");
            
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Test]
        public async Task AddRelation_WhenSaveThrowsException_ShouldPropagateException()
        {
            // Arrange
            var relations = new List<Relations>
            {
                new Relations
                {
                    RelationId = Guid.NewGuid(),
                    Relation = "Spouse"
                }
            };

            _relationRepositoryMock.Setup(r => r.AddAsync(It.IsAny<IEnumerable<Relations>>()))
                .Returns(Task.CompletedTask);
            
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ThrowsAsync(new Exception("Save operation failed"));

            // Act & Assert
            await FluentActions.Invoking(async () => await _relationCommandHandler.AddRelation(relations))
                .Should().ThrowAsync<Exception>()
                .WithMessage("Save operation failed");
            
            _relationRepositoryMock.Verify(r => r.AddAsync(It.IsAny<IEnumerable<Relations>>()), Times.Once);
        }
    }
}

