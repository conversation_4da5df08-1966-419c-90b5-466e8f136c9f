﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface ISocialHistoryCommandHandler<TText>
    {
        Task AddHistory(List<PatientSocialHistory> histories, Guid orgId, bool isSubscription);
        Task UpdateHistoryList(List<PatientSocialHistory> histories, Guid orgId, bool isSubscription);
    }
}
