using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class ICDQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IICDRepository> _icdRepositoryMock;
        private ICDQueryHandler _icdQueryHandler;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _icdRepositoryMock = new Mock<IICDRepository>();

            _unitOfWorkMock.Setup(u => u.ICDRepository).Returns(_icdRepositoryMock.Object);
            _icdQueryHandler = new ICDQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetICD_ShouldReturnAllICDCodes()
        {
            // Arrange
            var icdCodes = new List<ICD>
            {
                new ICD
                {
                    Code = "J45.909",
                    Description = "Unspecified asthma, uncomplicated"
                },
                new ICD
                {
                    Code = "I10",
                    Description = "Essential (primary) hypertension"
                },
                new ICD
                {
                    Code = "E11.9",
                    Description = "Type 2 diabetes mellitus without complications"
                }
            };

            _icdRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(icdCodes);

            // Act
            var result = await _icdQueryHandler.GetICD();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(3);
            result.Should().BeEquivalentTo(icdCodes);
        }

        [Test]
        public async Task GetICD_WithNoICDCodes_ShouldReturnEmptyCollection()
        {
            // Arrange
            var icdCodes = new List<ICD>();

            _icdRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(icdCodes);

            // Act
            var result = await _icdQueryHandler.GetICD();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetICD_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            _icdRepositoryMock.Setup(r => r.GetAsync())
                .ThrowsAsync(new Exception("Database connection error"));

            // Act & Assert
            await FluentActions.Invoking(async () => await _icdQueryHandler.GetICD())
                .Should().ThrowAsync<Exception>()
                .WithMessage("Database connection error");
        }
    }
}
