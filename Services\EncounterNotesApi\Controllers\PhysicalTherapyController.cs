﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using System;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PhysicalTherapyController : ControllerBase
    {
        private readonly IPhysicalTherapyCommandHandler<PhysicalTherapyData> _PhysicalTherapyDataHandler;
        private readonly IPhysicalTherapyQueryHandler<PhysicalTherapyData> _PhysicalTherapyQueryHandler;
        private readonly ILogger<PhysicalTherapyController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;

        public PhysicalTherapyController(
            IPhysicalTherapyCommandHandler<PhysicalTherapyData> medicalDataHandler,
            IPhysicalTherapyQueryHandler<PhysicalTherapyData> medicalQueryHandler,
            ILogger<PhysicalTherapyController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
        )
        {
            _PhysicalTherapyDataHandler = medicalDataHandler;
            _PhysicalTherapyQueryHandler = medicalQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        [HttpGet("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<PhysicalTherapyData>>> GetAllById(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult<IEnumerable<PhysicalTherapyData>> result;

            try
            {
                var data = await _PhysicalTherapyQueryHandler.GetAllPhysicalTherapyById(id, orgId, isSubscription);
                result = data != null ? Ok(data) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(500, _localizer["GetLogError"]);
            }

            return result;
        }

        [HttpGet("{id:guid}/IsActive/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<PhysicalTherapyData>>> GetAllByIdAndIsActive(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult<IEnumerable<PhysicalTherapyData>> result;

            try
            {
                var diagnosis = await _PhysicalTherapyQueryHandler.GetPhysicalTherapyByIdAndIsActive(id, orgId, isSubscription);
                result = diagnosis != null ? Ok(diagnosis) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(500, _localizer["GetLogError"]);
            }

            return result;
        }

        [HttpPost]
        [Route("AddPhysicalTherapy/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> AddPhysicalTherapy([FromBody] List<PhysicalTherapyData> _PhysicalTherapy, Guid orgId, bool isSubscription)
        {
            IActionResult result;

            if (_PhysicalTherapy == null || _PhysicalTherapy.Count == 0)
            {
                result = BadRequest(_localizer["NoLicense"]);
            }
            else
            {
                try
                {
                    await _PhysicalTherapyDataHandler.AddPhysicalTherapy(_PhysicalTherapy, orgId, isSubscription);
                    result = Ok(_localizer["SuccessfulRegistration"]);
                }
                catch (SqlException)
                {
                    result = StatusCode(500, _localizer["DatabaseError"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["PostLogError"]);
                    result = StatusCode(500);
                }
            }

            return result;
        }

        [HttpDelete]
        [Route("{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> DeleteByEntity([FromBody] PhysicalTherapyData _PhysicalTherapy, Guid orgId, bool isSubscription)
        {
            IActionResult result;

            if (_PhysicalTherapy == null)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _PhysicalTherapyDataHandler.DeletePhysicalTherapyByEntity(_PhysicalTherapy, orgId, isSubscription);
                    result = Ok(_localizer["DeleteSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["DeleteLogError"]);
                    result = StatusCode(500, _localizer["DeleteLogError"]);
                }
            }

            return result;
        }

        [HttpPut("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdatePhysicalTherapyById(Guid id, [FromBody] PhysicalTherapyData _PhysicalTherapy, Guid orgId, bool isSubscription)
        {
            IActionResult result;

            if (_PhysicalTherapy == null || _PhysicalTherapy.PatientId != id)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _PhysicalTherapyDataHandler.UpdatePhysicalTherapy(_PhysicalTherapy, orgId, isSubscription);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(500, _localizer["UpdateLogError"]);
                }
            }

            return result;
        }

        [HttpPut]
        [Route("UpdatePhysicalTherapyList/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdatePhysicalTherapyList([FromBody] List<PhysicalTherapyData> _PhysicalTherapy, Guid orgId, bool isSubscription)
        {
            IActionResult result;

            try
            {
                await _PhysicalTherapyDataHandler.UpdatePhysicalTherapyList(_PhysicalTherapy, orgId, isSubscription);
                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                result = StatusCode(500, _localizer["UpdateLogError"]);
            }
            return result;
        }

    }
}