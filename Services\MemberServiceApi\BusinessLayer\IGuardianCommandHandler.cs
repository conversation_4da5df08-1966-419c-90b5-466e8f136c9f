﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IGuardianCommandHandler<T>
    {

        Task AddGuardianAsync(List<T> guardiansGuid, Guid OrgID, bool Subscription);
        Task UpdateGuardianAsync(T guardian, Guid OrgID, bool Subscription);
        Task DeleteGuardianAsync(Guid id, Guid OrgID, bool Subscription);
    }
}
