using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class FDBDrugsControllerTests
    {
        private Mock<IFDBDrugsQueryHandler> _fdbDrugsQueryHandlerMock;
        private Mock<ILogger<FDBDrugsController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private FDBDrugsController _controller;

        [SetUp]
        public void Setup()
        {
            _fdbDrugsQueryHandlerMock = new Mock<IFDBDrugsQueryHandler>();
            _loggerMock = new Mock<ILogger<FDBDrugsController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();

            // Mock localizer strings
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error fetching data."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));

            _controller = new FDBDrugsController(
                _fdbDrugsQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetMedicationNameString_ReturnsOkWithMedicationNames_WhenDataExists()
        {
            // Arrange
            var medicationNames = new List<FDBMedicationName>
            {
                new FDBMedicationName { MED_NAME_ID = "MED1", MED_NAME = "Medication 1" },
                new FDBMedicationName { MED_NAME_ID = "MED2", MED_NAME = "Medication 2" }
            };

            _fdbDrugsQueryHandlerMock.Setup(x => x.GetAllMedicationNames())
                .ReturnsAsync(medicationNames);

            // Act
            var result = await _controller.GetMedicationNameString();

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedMedicationNames = okResult.Value as IEnumerable<FDBMedicationName>;
            returnedMedicationNames.Should().NotBeNull();
            returnedMedicationNames.Should().BeEquivalentTo(medicationNames);
        }

        [Test]
        public async Task GetMedicationNameString_ReturnsNotFound_WhenNoDataExists()
        {
            // Arrange
            _fdbDrugsQueryHandlerMock.Setup(x => x.GetAllMedicationNames())
                .ReturnsAsync((IEnumerable<FDBMedicationName>)null);

            // Act
            var result = await _controller.GetMedicationNameString();

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task GetMedicationNameString_ReturnsInternalServerError_WhenExceptionOccurs()
        {
            // Arrange
            _fdbDrugsQueryHandlerMock.Setup(x => x.GetAllMedicationNames())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetMedicationNameString();

            // Assert
            var statusCodeResult = result.Result as ObjectResult;
            statusCodeResult.Should().NotBeNull();
            statusCodeResult.StatusCode.Should().Be(500);
            statusCodeResult.Value.Should().Be(_localizerMock.Object["GetLogError"]);
        }

        [Test]
        public async Task GetMedicationString_ReturnsOkWithMedications_WhenDataExists()
        {
            // Arrange
            var routedDosageFormMedId = "RDFM123";
            var medications = new List<FDBMedication>
            {
                new FDBMedication { MEDID = "MED1", ROUTED_DOSAGE_FORM_MED_ID = routedDosageFormMedId, MED_MEDID_DESC = "Medication 1" },
                new FDBMedication { MEDID = "MED2", ROUTED_DOSAGE_FORM_MED_ID = routedDosageFormMedId, MED_MEDID_DESC = "Medication 2" }
            };

            _fdbDrugsQueryHandlerMock.Setup(x => x.GetAllMedications(routedDosageFormMedId))
                .ReturnsAsync(medications);

            // Act
            var result = await _controller.GetMedicationString(routedDosageFormMedId);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedMedications = okResult.Value as IEnumerable<FDBMedication>;
            returnedMedications.Should().NotBeNull();
            returnedMedications.Should().BeEquivalentTo(medications);
        }

        [Test]
        public async Task GetRoutedMedication_ReturnsOkWithRoutedMedications_WhenDataExists()
        {
            // Arrange
            var medNameId = "MN123";
            var routedMedications = new List<FDBRoutedMedication>
            {
                new FDBRoutedMedication { ROUTED_MED_ID = "RM1", MED_NAME_ID = medNameId, MED_ROUTED_MED_ID_DESC = "Routed Med 1" },
                new FDBRoutedMedication { ROUTED_MED_ID = "RM2", MED_NAME_ID = medNameId, MED_ROUTED_MED_ID_DESC = "Routed Med 2" }
            };

            _fdbDrugsQueryHandlerMock.Setup(x => x.GetAllRoutedMedication(medNameId))
                .ReturnsAsync(routedMedications);

            // Act
            var result = await _controller.GetRoutedMedication(medNameId);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedRoutedMedications = okResult.Value as IEnumerable<FDBRoutedMedication>;
            returnedRoutedMedications.Should().NotBeNull();
            returnedRoutedMedications.Should().BeEquivalentTo(routedMedications);
        }

        [Test]
        public async Task GetRoutedDosageFormMedication_ReturnsOkWithRoutedDosageForms_WhenDataExists()
        {
            // Arrange
            var routedMedId = "RM123";
            var routedDosageForms = new List<FDBRoutedDosageFormMedication>
            {
                new FDBRoutedDosageFormMedication { ROUTED_DOSAGE_FORM_MED_ID = "RDF1", ROUTED_MED_ID = routedMedId },
                new FDBRoutedDosageFormMedication { ROUTED_DOSAGE_FORM_MED_ID = "RDF2", ROUTED_MED_ID = routedMedId }
            };

            _fdbDrugsQueryHandlerMock.Setup(x => x.GetAllRoutedDosageFormMedication(routedMedId))
                .ReturnsAsync(routedDosageForms);

            // Act
            var result = await _controller.GetRoutedDosageFormMedication(routedMedId);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedRoutedDosageForms = okResult.Value as IEnumerable<FDBRoutedDosageFormMedication>;
            returnedRoutedDosageForms.Should().NotBeNull();
            returnedRoutedDosageForms.Should().BeEquivalentTo(routedDosageForms);
        }

        [Test]
        public async Task GetRouteForms_ReturnsOkWithRouteForms_WhenDataExists()
        {
            // Arrange
            var routeForms = new List<FDBRouteLookUp>
            {
                new FDBRouteLookUp { MED_ROUTE_ID = "ROUTE1", Route_Name = "Oral" },
                new FDBRouteLookUp { MED_ROUTE_ID = "ROUTE2", Route_Name = "Intravenous" }
            };

            _fdbDrugsQueryHandlerMock.Setup(x => x.GetAllRouteForms())
                .ReturnsAsync(routeForms);

            // Act
            var result = await _controller.GetRouteForms();

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedRouteForms = okResult.Value as IEnumerable<FDBRouteLookUp>;
            returnedRouteForms.Should().NotBeNull();
            returnedRouteForms.Should().BeEquivalentTo(routeForms);
        }

        [Test]
        public async Task GetTakeForms_ReturnsOkWithTakeForms_WhenDataExists()
        {
            // Arrange
            var takeForms = new List<FDBTakeLookUp>
            {
                new FDBTakeLookUp { MED_DOSAGE_FORM_ID = "TAKE1", Take_Name = "Tablet" },
                new FDBTakeLookUp { MED_DOSAGE_FORM_ID = "TAKE2", Take_Name = "Capsule" }
            };

            _fdbDrugsQueryHandlerMock.Setup(x => x.GetAllTakeForms())
                .ReturnsAsync(takeForms);

            // Act
            var result = await _controller.GetTakeForms();

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedTakeForms = okResult.Value as IEnumerable<FDBTakeLookUp>;
            returnedTakeForms.Should().NotBeNull();
            returnedTakeForms.Should().BeEquivalentTo(takeForms);
        }

        [Test]
        public async Task GetICD_ReturnsOkWithICDCodes_WhenDataExists()
        {
            // Arrange
            var icdCodes = new List<FDB_ICD>
            {
                new FDB_ICD { ICD_CD = "ICD1", ICD_DESC = "ICD Description 1" },
                new FDB_ICD { ICD_CD = "ICD2", ICD_DESC = "ICD Description 2" }
            };

            _fdbDrugsQueryHandlerMock.Setup(x => x.GetAllICD())
                .ReturnsAsync(icdCodes);

            // Act
            var result = await _controller.GetICD();

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedICDCodes = okResult.Value as IEnumerable<FDB_ICD>;
            returnedICDCodes.Should().NotBeNull();
            returnedICDCodes.Should().BeEquivalentTo(icdCodes);
        }

        [Test]
        public async Task GetAllergy_ReturnsOkWithAllergies_WhenDataExists()
        {
            // Arrange
            var allergies = new List<FDBAllergies>
            {
                new FDBAllergies { DAM_CONCEPT_ID = "ALLERGY1", DAM_CONCEPT_ID_DESC = "Allergy 1" },
                new FDBAllergies { DAM_CONCEPT_ID = "ALLERGY2", DAM_CONCEPT_ID_DESC = "Allergy 2" }
            };

            _fdbDrugsQueryHandlerMock.Setup(x => x.GetAllAllergyNames())
                .ReturnsAsync(allergies);

            // Act
            var result = await _controller.GetAllergy();

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedAllergies = okResult.Value as IEnumerable<FDBAllergies>;
            returnedAllergies.Should().NotBeNull();
            returnedAllergies.Should().BeEquivalentTo(allergies);
        }

        [Test]
        public async Task GetVaccine_ReturnsOkWithVaccines_WhenDataExists()
        {
            // Arrange
            var vaccines = new List<FDBVaccines>
            {
                new FDBVaccines { EVD_CVX_CD = "VAX1", EVD_CVX_CD_DESC_SHORT = "Vaccine 1" },
                new FDBVaccines { EVD_CVX_CD = "VAX2", EVD_CVX_CD_DESC_SHORT = "Vaccine 2" }
            };

            _fdbDrugsQueryHandlerMock.Setup(x => x.GetAllVaccineNames())
                .ReturnsAsync(vaccines);

            // Act
            var result = await _controller.GetVaccine();

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedVaccines = okResult.Value as IEnumerable<FDBVaccines>;
            returnedVaccines.Should().NotBeNull();
            returnedVaccines.Should().BeEquivalentTo(vaccines);
        }

        [Test]
        public async Task GetCPTforVaccine_ReturnsOkWithVaccineCPT_WhenDataExists()
        {
            // Arrange
            var cvxCode = "VAX123";
            var vaccineCPT = new FDBVaccine_CPT_CVX
            {
                EVD_CVX_CD = cvxCode,
                EVD_CPT_CD = "CPT123",
                EVD_VACCINE_NAME = "Vaccine Name"
            };

            _fdbDrugsQueryHandlerMock.Setup(x => x.GetCPT_CVX_Vaccine(cvxCode))
                .ReturnsAsync(vaccineCPT);

            // Act
            var result = await _controller.GetCPTforVaccine(cvxCode);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedVaccineCPT = okResult.Value as FDBVaccine_CPT_CVX;
            returnedVaccineCPT.Should().NotBeNull();
            returnedVaccineCPT.Should().BeEquivalentTo(vaccineCPT);
        }

        [Test]
        public async Task GetCPTforVaccine_ReturnsOkWithNull_WhenNoDataExists()
        {
            // Arrange
            var cvxCode = "NONEXISTENT";

            _fdbDrugsQueryHandlerMock.Setup(x => x.GetCPT_CVX_Vaccine(cvxCode))
                .ReturnsAsync((FDBVaccine_CPT_CVX)null);

            // Act
            var result = await _controller.GetCPTforVaccine(cvxCode);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().BeNull();
        }

        [Test]
        public async Task GetRXnormConcMedication_ReturnsOkWithMedications_WhenDataExists()
        {
            // Arrange
            var medications = new List<RxNormConcept>
            {
                new RxNormConcept { RXAUI = "RX1", TTY = "BN", STR = "Brand Name Med" },
                new RxNormConcept { RXAUI = "RX2", TTY = "IN", STR = "Ingredient Med" }
            };

            _fdbDrugsQueryHandlerMock.Setup(x => x.GetAllRxConceptMedication())
                .ReturnsAsync(medications);

            // Act
            var result = await _controller.GetRXnormConcMedication();

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedMedications = okResult.Value as IEnumerable<RxNormConcept>;
            returnedMedications.Should().NotBeNull();
            returnedMedications.Should().BeEquivalentTo(medications);
        }

        [Test]
        public async Task GetRXnormConcMedication_WithString_ReturnsOkWithFilteredMedications_WhenDataExists()
        {
            // Arrange
            var searchString = "aspirin";
            var medications = new List<RxNormConcept>
            {
                new RxNormConcept { RXAUI = "RX1", TTY = "SBDC", STR = "Aspirin 325 MG" },
                new RxNormConcept { RXAUI = "RX2", TTY = "SBDC", STR = "Aspirin 500 MG" }
            };

            _fdbDrugsQueryHandlerMock.Setup(x => x.GetAllRxConceptMedicationSBDC(searchString))
                .ReturnsAsync(medications);

            // Act
            var result = await _controller.GetRXnormConcMedication(searchString);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedMedications = okResult.Value as IEnumerable<RxNormConcept>;
            returnedMedications.Should().NotBeNull();
            returnedMedications.Should().BeEquivalentTo(medications);
        }

        [Test]
        public async Task GetRXnormConcMedication_WithString_ReturnsNotFound_WhenNoDataExists()
        {
            // Arrange
            var searchString = "nonexistent";

            _fdbDrugsQueryHandlerMock.Setup(x => x.GetAllRxConceptMedicationSBDC(searchString))
                .ReturnsAsync((IEnumerable<RxNormConcept>)null);

            // Act
            var result = await _controller.GetRXnormConcMedication(searchString);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }
    }
}
