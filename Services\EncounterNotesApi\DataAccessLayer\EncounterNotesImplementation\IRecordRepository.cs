﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IRecordRepository : IShardGenericRepository<Record>
    {
        Task AddRecordsAsync(List<Record> records, Guid OrgId, bool Subscription);
        Task<List<WordTiming>> GetWordTimings(Guid id, Guid OrgId, bool Subscription);
        Task<IEnumerable<Record>> GetByPCPIdAsync(Guid PCPId, Guid OrgId, bool Subscription);
        Task<IEnumerable<Record>> GetByPatientIdAsync(Guid PatientId, Guid OrgId, bool Subscription);
    }
}
