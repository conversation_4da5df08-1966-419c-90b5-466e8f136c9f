using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class ImmunizationCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<ImmunizationCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, Immunization, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IImmunizationRepository> _immunizationRepositoryMock;
        private ImmunizationCommandHandler _immunizationCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<ImmunizationCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, Immunization, EncounterDataAccessLayer>>();
            _immunizationRepositoryMock = new Mock<IImmunizationRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;

            _unitOfWorkMock.Setup(u => u.ImmunizationRepository).Returns(_immunizationRepositoryMock.Object);
            _immunizationCommandHandler = new ImmunizationCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object);
        }

        [Test]
        public async Task AddImmunization_ShouldAddImmunizationsToRepository()
        {
            // Arrange
            var immunizations = new List<Immunization>
            {
                new Immunization
                {
                    ImmunizationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Immunizations = "Influenza",
                    CPTCode = "90658",
                    CVXCode = "88",
                    Comments = "Annual flu shot",
                    CPTDescription = "Influenza virus vaccine, trivalent",
                    GivenDate = DateTime.Now.AddDays(-30),
                    IsActive = true,
                    Subscription = _subscription
                },
                new Immunization
                {
                    ImmunizationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Immunizations = "COVID-19",
                    CPTCode = "91300",
                    CVXCode = "213",
                    Comments = "First dose",
                    CPTDescription = "SARS-CoV-2 (COVID-19) vaccine",
                    GivenDate = DateTime.Now.AddDays(-60),
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _immunizationRepositoryMock.Setup(r => r.AddAsync(It.IsAny<List<Immunization>>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _immunizationCommandHandler.AddImmunization(immunizations, _orgId, _subscription);

            // Assert
            _immunizationRepositoryMock.Verify(r => r.AddAsync(immunizations, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateImmunization_ShouldUpdateImmunizationInRepository()
        {
            // Arrange
            var immunization = new Immunization
            {
                ImmunizationId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                Immunizations = "Influenza",
                CPTCode = "90658",
                CVXCode = "88",
                Comments = "Updated comments",
                CPTDescription = "Influenza virus vaccine, trivalent",
                GivenDate = DateTime.Now.AddDays(-30),
                IsActive = true,
                Subscription = _subscription
            };

            _immunizationRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Immunization>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _immunizationCommandHandler.UpdateImmunization(immunization, _orgId, _subscription);

            // Assert
            _immunizationRepositoryMock.Verify(r => r.UpdateAsync(immunization, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateImmunizationList_ShouldUpdateImmunizationsInRepository()
        {
            // Arrange
            var immunizations = new List<Immunization>
            {
                new Immunization
                {
                    ImmunizationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Immunizations = "Influenza",
                    IsActive = true,
                    Subscription = _subscription
                },
                new Immunization
                {
                    ImmunizationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Immunizations = "COVID-19",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _immunizationRepositoryMock.Setup(r => r.UpdateRangeAsync(It.IsAny<List<Immunization>>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _immunizationCommandHandler.UpdateImmunizationList(immunizations, _orgId, _subscription);

            // Assert
            _immunizationRepositoryMock.Verify(r => r.UpdateRangeAsync(immunizations, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteImmunizationById_ShouldDeleteImmunizationFromRepository()
        {
            // Arrange
            var immunizationId = Guid.NewGuid();

            _immunizationRepositoryMock.Setup(r => r.DeleteByIdAsync(immunizationId, _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _immunizationCommandHandler.DeleteImmunizationById(immunizationId, _orgId, _subscription);

            // Assert
            _immunizationRepositoryMock.Verify(r => r.DeleteByIdAsync(immunizationId, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteImmunizationByEntity_ShouldDeleteImmunizationFromRepository()
        {
            // Arrange
            var immunization = new Immunization
            {
                ImmunizationId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                Immunizations = "To be deleted",
                IsActive = false,
                Subscription = _subscription
            };

            _immunizationRepositoryMock.Setup(r => r.DeleteByEntityAsync(immunization, _orgId, _subscription))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _immunizationCommandHandler.DeleteImmunizationByEntity(immunization, _orgId, _subscription);

            // Assert
            _immunizationRepositoryMock.Verify(r => r.DeleteByEntityAsync(immunization, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }




    }
}

