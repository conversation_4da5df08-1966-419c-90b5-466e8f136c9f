﻿using DotNetEnv;
using EncounterNotesBusinessLayer;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Interfaces.ShardManagement;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.HttpsPolicy;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Web;
using Microsoft.OpenApi.Models;
using Microsoft.SemanticKernel;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace EncounterNotesService
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
            var builder = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddEnvironmentVariables();
            Configuration = builder.Build();
            DotNetEnv.Env.Load();
        }

        public IConfiguration Configuration { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllers();
            services.AddCors(options =>
            {
                options.AddPolicy("AllowAllOrigins",
                    builder =>
                    {
                        builder.AllowAnyOrigin()
                               .AllowAnyMethod()
                               .AllowAnyHeader();
                    });
            });

            services.AddScoped(typeof(IMigration<,,>), typeof(Migration<,,>));
            services.AddScoped<IShardMapManagerService<RecordDatabaseContext>>(provider =>
            {
                var connectionString = Environment.GetEnvironmentVariable("ShardMapManagerConnectionString");
                var logger = provider.GetRequiredService<ILogger<RecordDatabaseContext>>();
                var localizer = provider.GetRequiredService<IStringLocalizer<EncounterDataAccessLayer>>();
                return new ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer>(connectionString, logger, localizer);
            });

            services.AddScoped<ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer>>(provider =>
                (ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer>)provider.GetRequiredService<IShardMapManagerService<RecordDatabaseContext>>());

            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Swagger Azure AD for EncounterNotesService api", Version = "v1" });
                c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
                {
                    Description = "Oauth2.0 which uses AuthorizationCode flow",
                    Name = "oauth2.0",
                    Type = SecuritySchemeType.OAuth2,
                    Flows = new OpenApiOAuthFlows
                    {
                        AuthorizationCode = new OpenApiOAuthFlow
                        {
                            AuthorizationUrl = new Uri(Environment.GetEnvironmentVariable("SwaggerAzureAd__AuthorizationUrl")),
                            TokenUrl = new Uri(Environment.GetEnvironmentVariable("SwaggerAzureAd__TokenUrl")),
                            Scopes = new Dictionary<string, string>
                            {
                                { Environment.GetEnvironmentVariable("SwaggerAzureAd__Scope"), "Access API as User" }
                            }
                        }
                    }
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "oauth2"
                            }
                        },
                         new[] { Environment.GetEnvironmentVariable("SwaggerAzureAd__Scope") }
                    }
                });
            });
            services.AddLocalization();
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                            .AddJwtBearer(options =>
                            {
                                options.Authority = Environment.GetEnvironmentVariable("AzureAd__Authority");
                                options.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
                                {
                                    ValidateIssuer = true,
                                    ValidateAudience = true,
                                    ValidateLifetime = true,
                                    ValidIssuer = Environment.GetEnvironmentVariable("AzureAd__Authority"),
                                    ValidAudience = Environment.GetEnvironmentVariable("AzureAd__ClientId")
                                };
                            });
                        
            var redisConfiguration = ConfigurationOptions.Parse(Environment.GetEnvironmentVariable("RedisConnectionString"));
            redisConfiguration.AbortOnConnectFail = false;
            redisConfiguration.ConnectRetry = 3;
            redisConfiguration.ConnectTimeout = 5000;
            redisConfiguration.SyncTimeout = 3000;
            redisConfiguration.AsyncTimeout = 3000;

            services.AddSingleton<IConnectionMultiplexer>(_ =>
                ConnectionMultiplexer.Connect(redisConfiguration));
            services.AddSingleton<ICacheService, CacheService>();
            services.AddScoped<IChiefComplaintCommandHandler<ChiefComplaint>, ChiefComplaintCommandHandler>();
            services.AddScoped<IChiefComplaintQueryHandler<ChiefComplaint>, ChiefComplaintQueryHandler>();
            services.AddScoped<IRecordQueryHandler<Record>, RecordQueryHandler>();
            services.AddScoped<IMedicalHistoryQueryHandler<MedicalHistory>, MedicalHistoryQueryHandler>();
            services.AddScoped<IMedicalHistoryCommandHandler<MedicalHistory>, MedicalHistoryCommandHandler>();
            services.AddSingleton<IBlobStorageService, BlobStorageService>();
            services.AddScoped<IKernelService, KernelService>();
            services.AddScoped<IRecordCommandHandler<Record>, RecordCommandHandler>();
            services.AddDbContext<RecordDatabaseContext>(options =>
             options.UseSqlServer(Environment.GetEnvironmentVariable("DatabaseConnectionString")).EnableSensitiveDataLogging().LogTo(Console.WriteLine, LogLevel.Information));
            services.AddScoped<ISpeechDataHandler<Speech>, SpeechDataHandler>();
            services.AddScoped<IFamilyMemberQueryHandler<FamilyMember>, FamilyMemberQueryHandler>();
            services.AddScoped<IFamilyMemberCommandHandler<FamilyMember>, FamilyMemberCommandHandler>();
            services.AddScoped<IVisionExaminationCommandHandler<VisionRx>, VisionExaminationCommandHandler>();
            services.AddScoped<IVisionExaminationQueryHandler<VisionRx>, VisionExaminationQueryHandler>();
            services.AddScoped<IDiagnosticImagingQueryHandler<DiagnosticImage>, DiagnosticImagingQueryHandler>();
            services.AddScoped<IDiagnosticImagingCommandHandler<DiagnosticImage>, DiagnosticImagingCommandHandler>();
            services.AddScoped<IRelationQueryHandler<Relations>, RelationQueryHandler>();
            services.AddScoped<IRelationCommandHandler<Relations>, RelationCommandHandler>();
            services.AddScoped<IHospitalizationRecordQueryHandler<HospitalizationRecord>, HospitalizationRecordQueryHandler>();
            services.AddScoped<IHospitalizationRecordCommandHandler<HospitalizationRecord>, HospitalizationRecordCommandHandler>();
            services.AddScoped<IDiagnosticImagingPageQueryHandler<DiagnosticImagingDTO>, DiagnosticImagingPageQueryHandler>();
            services.AddScoped<IDiagnosticImagingPageCommandHandler<DiagnosticImagingDTO>, DiagnosticImagingPageCommandHandler>();
            services.AddScoped<ITemplatesQueryHandler<Templates>, TemplatesQueryHandler>();
            services.AddScoped<ITemplatesCommandHandler<Templates>, TemplatesCommandHandler>();
            services.AddScoped<IReviewOfSystemCommandHandler<ReviewOfSystem>,ReviewOfSystemCommandHandler>();
            services.AddScoped<IReviewOfSystemQueryHandler<ReviewOfSystem>, ReviewOfSystemQueryHandler>();
            services.AddScoped<ISurgicalHistoryQueryHandler<SurgicalHistory>, SurgicalHistoryQueryHandler>();
            services.AddScoped<ISurgicalHistoryCommandHandler<SurgicalHistory>, SurgicalHistoryCommandHandler>();
            services.AddScoped<IImmunizationQueryHandler<Immunization>, ImmunizationQueryHandler>();
            services.AddScoped<IImmunizationCommandHandler<Immunization>, ImmunizationCommandHandler>();
            services.AddScoped<IPhysicalExaminationQueryHandler<PhysicalExamination>, PhysicalExaminationQueryHandler>();
            services.AddScoped<IPhysicalExaminationCommandHandler<PhysicalExamination>, PhysicalExaminationCommandHandler>();
            services.AddScoped<IPastResultsQueryHandler<PastResults>, PastResultsQueryHandler>();
            services.AddScoped<IPastResultsCommandHandler<PastResults>, PastResultsCommandHandler>();
            services.AddScoped<IPredefinedTemplatesCommandHandler<PredefinedTemplates>, PredefinedTemplatesCommandHandler>();
            services.AddScoped<IPredefinedTemplatesQueryHandler<PredefinedTemplates>, PredefinedTemplatesQueryHandler>();
            services.AddScoped<ICurrentMedicationQueryHandler<CurrentMedication>, CurrentMedicationQueryHandler>();
            services.AddScoped<ICurrentMedicationCommandHandler<CurrentMedication>, CurrentMedicationCommandHandler>();
            services.AddScoped<ISocialHistoryQueryHandler<PatientSocialHistory>, SocialHistoryQueryHandler>();
            services.AddScoped<ISocialHistoryCommandHandler<PatientSocialHistory>, SocialHistoryCommandHandler>();
            services.AddScoped<IICDQueryHandler<ICD>, ICDQueryHandler>();
            services.AddScoped<IVaccinesQueryHandler<Vaccines>, VaccinesQueryHandler>();
            services.AddScoped<ITherapeuticInterventionsListQueryHandler<TherapeuticInterventionsList>, TherapeuticInterventionsListQueryHandler>();
            services.AddScoped<ISoapNotesComponentsQueryHandler<SoapNotesComponents>, SoapNotesComponentsQueryHandler>();
            services.AddScoped<IHistoryOfPresentIllnessCommandHandler<HistoryOfPresentIllness>, HistoryOfPresentIllnessCommandHandler>();
            services.AddScoped<IHistoryOfPresentIllnessQueryHandler<HistoryOfPresentIllness>,HistoryOfPresentIllnessQueryHandler>();
            services.AddScoped<ISymptomsQueryHandler<Symptoms>, SymptomsQueryHandler>();
            services.AddScoped<ISymptomsCommandHandler<Symptoms>, SymptomsCommandHandler>();    
            services.AddScoped<IAllergyRepository, AllergyRepository>();
            services.AddScoped<IAllergyQueryHandler<Allergy>, AllergyQueryHandler>();
            services.AddScoped<IAllergyCommandHandler<Allergy>, AllergyCommandHandler>();
            services.AddScoped<IPrescriptionMedicationQueryHandler<PrescriptionMedication>, PrescriptionMedicationQueryHandler>();
            services.AddScoped<IPrescriptionMedicationCommandHandler<PrescriptionMedication>, PrescriptionMedicationCommandHandler>();
            services.AddScoped<ITherapeuticInterventionsCommandHandler<TherapeuticInterventionsData>, TherapeuticInterventionsCommandHandler>();
            services.AddScoped<ITherapeuticInterventionsQueryHandler<TherapeuticInterventionsData>, TherapeuticInterventionsQueryHandler>();
            services.AddScoped<IAssessmentsCommandHandler<AssessmentsData>,AssessmentsCommandHandler>();
            services.AddScoped<IAssessmentsQueryHandler<AssessmentsData>, AssessmentsQueryHandler>();
            services.AddScoped<IPhysicalTherapyCommandHandler<PhysicalTherapyData>, PhysicalTherapyCommandHandler>();
            services.AddScoped<IPhysicalTherapyQueryHandler<PhysicalTherapyData>, PhysicalTherapyQueryHandler>();
            services.AddScoped<IVitalQueryHandler<Vitals>, VitalQueryHandler>();
            services.AddScoped<IVitalCommandHandler<Vitals>, VitalCommandHandler>();
            services.AddScoped<IReferralOutgoingQueryHandler<PatientReferralOutgoing>, ReferralOutgoingQueryHandler>();
            services.AddScoped<IReferralOutgoingCommandHandler<PatientReferralOutgoing>, ReferralOutgoingCommandHandler>();
            services.AddTransient<IUnitOfWork, UnitOfWork>();
            services.AddScoped<IObHistoryRepository<ObHistory>, ObHistoryRepository>();
            services.AddScoped<IObHistoryCommandHandler<ObHistory>, ObHistoryCommandHandler>();
            services.AddScoped<IObHistoryQueryHandler<ObHistory>, ObHistoryQueryHandler>();
            services.AddScoped<IGynHistoryRepository<GynHistory>, GynHistoryRepository>();
            services.AddScoped<IGynHistoryCommandHandler<GynHistory>, GynHistoryCommandHandler>();
            services.AddScoped<IGynHistoryQueryHandler<GynHistory>, GynHistoryQueryHandler>();
            services.AddScoped<IExaminationCommandHandler, ExaminationCommandHandler>();
            services.AddScoped<IExaminationQueryHandler, ExaminationQueryHandler>();
            services.AddScoped<ILabTestsCommandHandler<LabTests>, LabTestsCommandHandler>();
            services.AddScoped<ILabTestsQueryHandler<LabTests>, LabTestsQueryHandler>();

            services.AddHttpClient();
            services.AddTransient<RecordDTO>();
            services.AddScoped<IProcedureQueryHandler<Procedure>, ProcedureQueryHandler>();
            services.AddScoped<IProcedureCommandHandler<Procedure>, ProcedureCommandHandler>();
            services.AddScoped<IProcedureRepository, ProcedureRepository>();
            services.AddScoped<ICPTQueryHandler<CPT>, CPTQueryHandler>();
            services.AddScoped<ICPTRepository, CPTRepository>();
            services.AddScoped<IBillingCommandHandler<Billing>, BillingCommandHandler>();
            services.AddScoped<IBillingQueryHandler<Billing>, BillingQueryHandler>();

            services.AddScoped<IFDBDrugsQueryHandler, FDBDrugsQueryHandler>();

            services.AddControllers().AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
            });
            services.AddControllers().AddNewtonsoftJson(options =>
            {
                options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
            });

            services.AddSingleton<Kernel>(provider =>
            {
                var kernelBuilder = Kernel.CreateBuilder();
                return kernelBuilder.Build();
            });


            services.AddScoped<IKernelService, KernelService>();

            services.Configure<IISServerOptions>(options =>
            {
                options.MaxRequestBodySize = 100 * 1024 * 1024; 
            });
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment() || env.IsProduction())
            {
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.OAuthClientId(Environment.GetEnvironmentVariable("SwaggerAzureAd__ClientId"));
                    c.OAuthUsePkce();
                    c.OAuthScopeSeparator(" ");
                    string swaggerJsonBasePath = string.IsNullOrWhiteSpace(c.RoutePrefix) ? "." : "..";
                    c.SwaggerEndpoint($"{swaggerJsonBasePath}/swagger/v1/swagger.json", "EncounterNotesService v1");
                });
            }
            app.UseHttpsRedirection();
            app.UseCors("AllowAllOrigins");
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
