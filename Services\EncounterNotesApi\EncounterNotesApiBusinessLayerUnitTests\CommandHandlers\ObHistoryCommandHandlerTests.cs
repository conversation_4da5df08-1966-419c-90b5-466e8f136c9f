using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class ObHistoryCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<ObHistoryCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, ObHistory, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IObHistoryRepository<ObHistory>> _obHistoryRepositoryMock;
        private ObHistoryCommandHandler _obHistoryCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<ObHistoryCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, ObHistory, EncounterDataAccessLayer>>();
            _obHistoryRepositoryMock = new Mock<IObHistoryRepository<ObHistory>>();

            _unitOfWorkMock.Setup(u => u.ObHistoryRepository).Returns(_obHistoryRepositoryMock.Object);

            _obHistoryCommandHandler = new ObHistoryCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task AddAsync_ValidObHistories_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var obHistories = new List<ObHistory>
            {
                new ObHistory
                {
                    obId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Symptoms = "Morning sickness",
                    Notes = "Patient reports severe nausea in the mornings",
                    DateOfComplaint = DateTime.Now.AddDays(-10),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Subscription = _subscription,
                    IsDeleted = false
                }
            };

            // Act
            await _obHistoryCommandHandler.AddAsync(obHistories, _orgId, _subscription);

            // Assert
            _obHistoryRepositoryMock.Verify(r => r.AddAsync(obHistories, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateAsync_ValidObHistory_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var obHistory = new ObHistory
            {
                obId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Symptoms = "Updated symptoms",
                Notes = "Updated notes",
                DateOfComplaint = DateTime.Now.AddDays(-5),
                OrganizationId = _orgId,
                PcpId = Guid.NewGuid(),
                Subscription = _subscription,
                IsDeleted = false
            };

            // Act
            await _obHistoryCommandHandler.UpdateAsync(obHistory, _orgId, _subscription);

            // Assert
            _obHistoryRepositoryMock.Verify(r => r.UpdateAsync(obHistory, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateObHistoryListAsync_ValidObHistories_ShouldCallRepositoryForEachItemAndSaveOnce()
        {
            // Arrange
            var obHistories = new List<ObHistory>
            {
                new ObHistory
                {
                    obId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Symptoms = "Updated symptoms 1",
                    Notes = "Updated notes 1",
                    DateOfComplaint = DateTime.Now.AddDays(-5),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Subscription = _subscription,
                    IsDeleted = false
                },
                new ObHistory
                {
                    obId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Symptoms = "Updated symptoms 2",
                    Notes = "Updated notes 2",
                    DateOfComplaint = DateTime.Now.AddDays(-3),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Subscription = _subscription,
                    IsDeleted = false
                }
            };

            // Act
            await _obHistoryCommandHandler.UpdateObHistoryListAsync(obHistories, _orgId, _subscription);

            // Assert
            foreach (var obHistory in obHistories)
            {
                _obHistoryRepositoryMock.Verify(r => r.UpdateAsync(obHistory, _orgId, _subscription), Times.Once);
            }
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteByIdAsync_ValidId_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var obId = Guid.NewGuid();

            // Act
            await _obHistoryCommandHandler.DeleteByIdAsync(obId, _orgId, _subscription);

            // Assert
            _obHistoryRepositoryMock.Verify(r => r.DeleteByIdAsync(obId, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteByEntity_ValidEntity_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var obHistory = new ObHistory
            {
                obId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Symptoms = "Symptoms",
                Notes = "Notes",
                DateOfComplaint = DateTime.Now.AddDays(-10),
                OrganizationId = _orgId,
                PcpId = Guid.NewGuid(),
                Subscription = _subscription,
                IsDeleted = false
            };

            // Act
            await _obHistoryCommandHandler.DeleteByEntity(obHistory, _orgId, _subscription);

            // Assert
            _obHistoryRepositoryMock.Verify(r => r.DeleteByEntityAsync(obHistory, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}
