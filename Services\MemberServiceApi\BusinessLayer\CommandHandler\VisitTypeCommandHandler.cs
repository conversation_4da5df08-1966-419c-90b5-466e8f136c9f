﻿using Contracts;
using DataAccessLayer.Implementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MemberServiceDataAccessLayer.Implementation;
using MemberServiceDataAccessLayer;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class VisitTypeCommandHandler : IVisitTypeCommandHandler<VisitType>
    {
        private readonly IVisitTypeRepository _visitTypeRepository;
        private readonly IUnitOfWork _unitOfWork;

        public VisitTypeCommandHandler(
            IVisitTypeRepository visitTypeRepository,
            IUnitOfWork unitOfWork
        )
        {
            _visitTypeRepository = visitTypeRepository ?? throw new ArgumentNullException(nameof(visitTypeRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        /// <summary>
        /// Updates the CPT code for a specific visit type by organization ID and visit name.
        /// </summary>
        /// <param name="orgId">The Organization ID.</param>
        /// <param name="visitName">The name of the visit type.</param>
        /// <param name="newCptCode">The new CPT code to update.</param>
        /// <returns>True if the update was successful, otherwise false.</returns>
        public async Task<bool> UpdateCptCodeAsync(Guid orgId, string visitName, string newCptCode, bool Subscription)
        {
            var success = await _visitTypeRepository.UpdateCptCodeAsync(orgId, visitName, newCptCode);
            if (success)
            {
                await _unitOfWork.SaveAsync();
            }
            return success;
        }

        /// <summary>
        /// Adds a new VisitType entity or reactivates an existing inactive VisitType.
        /// </summary>
        /// <param name="visitType">The VisitType entity to be added or reactivated.</param>
        /// <remarks>
        /// If a VisitType with the same OrganizationId and VisitName exists but is inactive, it is reactivated.
        /// Otherwise, a new VisitType entity is added.
        /// </remarks>
        /// <returns>A task representing the asynchronous operation.</returns>
        public async Task AddVisitTypeAsync(VisitType visitType,Guid orgId, bool Subscription)
        {
            if (visitType == null)
            {
                throw new ArgumentNullException(nameof(visitType));
            }

            var existingVisitType = await _unitOfWork.VisitTypeRepository
                .GetFirstOrDefaultAsync(vt => vt.OrganizationId == visitType.OrganizationId
                                           && vt.VisitName == visitType.VisitName
                                           && vt.IsActive == false);

            if (existingVisitType != null)
            {
                existingVisitType.IsActive = true;
            }
            else
            {
                await _unitOfWork.VisitTypeRepository.AddAsync(new List<VisitType> { visitType },orgId ,Subscription);
            }

            await _unitOfWork.SaveAsync();
        }

        /// <summary>
        /// Deletes a visit type based on Organization ID and visit name.
        /// </summary>
        /// <param name="orgId">The Organization ID.</param>
        /// <param name="visitName">The name of the visit type.</param>
        /// <returns>True if the deletion was successful, otherwise false.</returns>
        public async Task<bool> DeleteVisitTypeAsync(Guid orgId, string visitName, bool Subscription)
        {
            await _visitTypeRepository.DeleteByNameAsync(visitName, orgId, Subscription);
            await _unitOfWork.SaveAsync();
            return true;
        }
    }
}
