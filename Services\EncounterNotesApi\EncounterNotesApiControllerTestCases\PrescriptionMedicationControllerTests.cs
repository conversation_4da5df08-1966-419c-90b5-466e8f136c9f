using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class PrescriptionMedicationControllerTests
    {
        private Mock<IPrescriptionMedicationCommandHandler<PrescriptionMedication>> _prescriptionMedicationCommandHandlerMock;
        private Mock<IPrescriptionMedicationQueryHandler<PrescriptionMedication>> _prescriptionMedicationQueryHandlerMock;
        private Mock<ILogger<PrescriptionMedicationController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private PrescriptionMedicationController _controller;
        private List<PrescriptionMedication> _testPrescriptionMedications;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _prescriptionMedicationCommandHandlerMock = new Mock<IPrescriptionMedicationCommandHandler<PrescriptionMedication>>();
            _prescriptionMedicationQueryHandlerMock = new Mock<IPrescriptionMedicationQueryHandler<PrescriptionMedication>>();
            _loggerMock = new Mock<ILogger<PrescriptionMedicationController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No license available."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error occurred."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error posting data."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting data."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Delete successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testPrescriptionMedications = new List<PrescriptionMedication>
            {
                new PrescriptionMedication {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    AssessmentsID = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Assessments = "Hypertension",
                    BrandName = "Lisinopril",
                    DrugDetails = "10mg tablet",
                    Quantity = "30",
                    Frequency = "Once daily",
                    isActive = true,
                    StartDate = DateTime.Now.AddDays(-30),
                    EndDate = DateTime.Now.AddDays(30),
                    Subscription = _isSubscription
                },
                new PrescriptionMedication {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    AssessmentsID = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-15),
                    Assessments = "Type 2 Diabetes",
                    BrandName = "Metformin",
                    DrugDetails = "500mg tablet",
                    Quantity = "60",
                    Frequency = "Twice daily",
                    isActive = true,
                    StartDate = DateTime.Now.AddDays(-20),
                    EndDate = DateTime.Now.AddDays(40),
                    Subscription = _isSubscription
                }
            };

            _controller = new PrescriptionMedicationController(
                _prescriptionMedicationCommandHandlerMock.Object,
                _prescriptionMedicationQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetAllById_ReturnsPrescriptionMedications_WhenPrescriptionMedicationsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientPrescriptionMedications = _testPrescriptionMedications.Select(pm => { pm.PatientId = patientId; return pm; }).ToList();
            _prescriptionMedicationQueryHandlerMock.Setup(x => x.GetAllMedicationsbyId(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientPrescriptionMedications);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedPrescriptionMedications = okResult.Value as IEnumerable<PrescriptionMedication>;
            returnedPrescriptionMedications.Should().NotBeNull();
            returnedPrescriptionMedications.Should().BeEquivalentTo(patientPrescriptionMedications);
        }

        [Test]
        public async Task GetAllById_ReturnsNotFound_WhenNoPrescriptionMedicationsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _prescriptionMedicationQueryHandlerMock.Setup(x => x.GetAllMedicationsbyId(patientId, _orgId, _isSubscription))
                .ReturnsAsync((IEnumerable<PrescriptionMedication>)null);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsPrescriptionMedications_WhenActivePrescriptionMedicationsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activePrescriptionMedications = _testPrescriptionMedications.Select(pm => { pm.PatientId = patientId; pm.isActive = true; return pm; }).ToList();
            _prescriptionMedicationQueryHandlerMock.Setup(x => x.GetMedicationByIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activePrescriptionMedications);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedPrescriptionMedications = okResult.Value as IEnumerable<PrescriptionMedication>;
            returnedPrescriptionMedications.Should().NotBeNull();
            returnedPrescriptionMedications.Should().BeEquivalentTo(activePrescriptionMedications);
        }

        [Test]
        public async Task Registration_ValidPrescriptionMedications_ReturnsOk()
        {
            // Arrange
            var prescriptionMedications = _testPrescriptionMedications;

            // Act
            var result = await _controller.Registration(prescriptionMedications, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _prescriptionMedicationCommandHandlerMock.Verify(x => x.AddMedication(prescriptionMedications, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task Registration_EmptyList_ReturnsBadRequest()
        {
            // Arrange
            var emptyPrescriptionMedications = new List<PrescriptionMedication>();

            // Act
            var result = await _controller.Registration(emptyPrescriptionMedications, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);
        }

        [Test]
        public async Task DeleteByEntity_ValidPrescriptionMedication_ReturnsOk()
        {
            // Arrange
            var prescriptionMedication = _testPrescriptionMedications[0];

            // Act
            var result = await _controller.DeleteByEntity(prescriptionMedication, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _prescriptionMedicationCommandHandlerMock.Verify(x => x.DeleteMedicationByEntity(prescriptionMedication, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateMedicationById_ValidPrescriptionMedication_ReturnsOk()
        {
            // Arrange
            var prescriptionMedication = _testPrescriptionMedications[0];

            // Act
            var result = await _controller.UpdateMedicationById(prescriptionMedication.PatientId, prescriptionMedication, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _prescriptionMedicationCommandHandlerMock.Verify(x => x.UpdateMedication(prescriptionMedication, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateMedicationList_ValidPrescriptionMedications_ReturnsOk()
        {
            // Arrange
            var prescriptionMedications = _testPrescriptionMedications;

            // Act
            var result = await _controller.UpdateMedicationList(prescriptionMedications, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _prescriptionMedicationCommandHandlerMock.Verify(x => x.UpdateMedicationList(prescriptionMedications, _orgId, _isSubscription), Times.Once);
        }


    }
}
