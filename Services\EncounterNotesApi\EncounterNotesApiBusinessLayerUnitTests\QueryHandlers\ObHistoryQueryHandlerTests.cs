using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class ObHistoryQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IObHistoryRepository<ObHistory>> _obHistoryRepositoryMock;
        private ObHistoryQueryHandler _obHistoryQueryHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _obHistoryRepositoryMock = new Mock<IObHistoryRepository<ObHistory>>();
            _orgId = Guid.NewGuid();
            _subscription = false;

            _unitOfWorkMock.Setup(u => u.ObHistoryRepository).Returns(_obHistoryRepositoryMock.Object);
            _obHistoryQueryHandler = new ObHistoryQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetAllAsync_ShouldReturnAllObHistories()
        {
            // Arrange
            var obHistories = new List<ObHistory>
            {
                new ObHistory
                {
                    obId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Symptoms = "Morning sickness",
                    Notes = "Patient reports severe nausea in the mornings",
                    DateOfComplaint = DateTime.Now.AddDays(-10),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Subscription = _subscription,
                    IsDeleted = false
                },
                new ObHistory
                {
                    obId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Symptoms = "Back pain",
                    Notes = "Patient reports lower back pain",
                    DateOfComplaint = DateTime.Now.AddDays(-5),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Subscription = _subscription,
                    IsDeleted = false
                }
            };

            _obHistoryRepositoryMock.Setup(r => r.GetAllAsync(_orgId, _subscription))
                .ReturnsAsync(obHistories);

            // Act
            var result = await _obHistoryQueryHandler.GetAllAsync(_orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.Should().BeEquivalentTo(obHistories);
        }

        [Test]
        public async Task GetAllAsync_WithNoObHistories_ShouldReturnEmptyCollection()
        {
            // Arrange
            var obHistories = new List<ObHistory>();

            _obHistoryRepositoryMock.Setup(r => r.GetAllAsync(_orgId, _subscription))
                .ReturnsAsync(obHistories);

            // Act
            var result = await _obHistoryQueryHandler.GetAllAsync(_orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetByIdAsync_ShouldReturnObHistoryWithMatchingId()
        {
            // Arrange
            var obId = Guid.NewGuid();
            var obHistory = new ObHistory
            {
                obId = obId,
                PatientId = Guid.NewGuid(),
                Symptoms = "Morning sickness",
                Notes = "Patient reports severe nausea in the mornings",
                DateOfComplaint = DateTime.Now.AddDays(-10),
                OrganizationId = _orgId,
                PcpId = Guid.NewGuid(),
                Subscription = _subscription,
                IsDeleted = false
            };

            _obHistoryRepositoryMock.Setup(r => r.GetByIdAsync(obId, _orgId, _subscription))
                .ReturnsAsync(obHistory);

            // Act
            var result = await _obHistoryQueryHandler.GetByIdAsync(obId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(obHistory);
        }

        [Test]
        public async Task GetByIdAsync_WithNonExistentId_ShouldReturnNull()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid();

            _obHistoryRepositoryMock.Setup(r => r.GetByIdAsync(nonExistentId, _orgId, _subscription))
                .ReturnsAsync((ObHistory)null);

            // Act
            var result = await _obHistoryQueryHandler.GetByIdAsync(nonExistentId, _orgId, _subscription);

            // Assert
            result.Should().BeNull();
        }

        [Test]
        public async Task GetByPatientIdAsync_ShouldReturnObHistoriesWithMatchingPatientId()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var obHistories = new List<ObHistory>
            {
                new ObHistory
                {
                    obId = Guid.NewGuid(),
                    PatientId = patientId,
                    Symptoms = "Morning sickness",
                    Notes = "Patient reports severe nausea in the mornings",
                    DateOfComplaint = DateTime.Now.AddDays(-10),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Subscription = _subscription,
                    IsDeleted = false
                },
                new ObHistory
                {
                    obId = Guid.NewGuid(),
                    PatientId = patientId,
                    Symptoms = "Back pain",
                    Notes = "Patient reports lower back pain",
                    DateOfComplaint = DateTime.Now.AddDays(-5),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Subscription = _subscription,
                    IsDeleted = false
                }
            };

            _obHistoryRepositoryMock.Setup(r => r.GetByPatientIdAsync(patientId, _orgId, _subscription))
                .ReturnsAsync(obHistories);

            // Act
            var result = await _obHistoryQueryHandler.GetByPatientIdAsync(patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.Should().BeEquivalentTo(obHistories);
        }

        [Test]
        public async Task GetByPatientIdAsync_WithNoMatchingPatient_ShouldReturnEmptyCollection()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var obHistories = new List<ObHistory>();

            _obHistoryRepositoryMock.Setup(r => r.GetByPatientIdAsync(patientId, _orgId, _subscription))
                .ReturnsAsync(obHistories);

            // Act
            var result = await _obHistoryQueryHandler.GetByPatientIdAsync(patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetAllAsync_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            _obHistoryRepositoryMock.Setup(r => r.GetAllAsync(_orgId, _subscription))
                .ThrowsAsync(new Exception("Database connection error"));

            // Act & Assert
            await FluentActions.Invoking(async () => await _obHistoryQueryHandler.GetAllAsync(_orgId, _subscription))
                .Should().ThrowAsync<Exception>()
                .WithMessage("Database connection error");
        }
    }
}
