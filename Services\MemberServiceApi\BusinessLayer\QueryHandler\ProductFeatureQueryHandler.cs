﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MemberServiceBusinessLayer;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class ProductFeatureQueryHandler : IProductFeatureQueryHandler<ProductFeature>
    {
        private readonly IUnitOfWork _unitOfWork;

        public ProductFeatureQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<ProductFeature> GetProductFeatureByIdAsync(Guid id)
        {
            var ProductFeature = await _unitOfWork.ProductFeatureRepository.GetByIdAsync(id);
            return ProductFeature;
        }


        public async Task<List<ProductFeature>> GetAllProductFeaturesAsync()
        {
            var features = await _unitOfWork.ProductFeatureRepository.GetAllAsync();
            return features.ToList();
        }
    }
}
