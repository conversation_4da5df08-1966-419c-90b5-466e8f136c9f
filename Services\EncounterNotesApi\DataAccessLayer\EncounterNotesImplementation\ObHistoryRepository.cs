﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Interfaces.ShardManagement;
using Microsoft.Extensions.Logging;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class ObHistoryRepository : ShardGenericRepository<ObHistory>, IObHistoryRepository<ObHistory>
    {
        private readonly ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> _shardMapManagerService;
        private readonly ILogger<RecordDatabaseContext> _logger;
        private readonly string _shardMapName;
        public ObHistoryRepository(RecordDatabaseContext context,
                                   ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> shardMapManagerService,
                                   IStringLocalizer<EncounterDataAccessLayer> localizer,
                                   ILogger<RecordDatabaseContext> logger)
            : base(context, shardMapManagerService, localizer, logger)
        {
            _shardMapManagerService = shardMapManagerService;
            _logger = logger;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }

        public async Task AddAsync(IEnumerable<ObHistory> entities, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            await context.ObHistory.AddRangeAsync(entities);
            await context.SaveChangesAsync();
        }

        public async Task<ObHistory> GetByIdAsync(Guid id, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return null;
            return await context.ObHistory.FirstOrDefaultAsync(ob => ob.obId == id && !ob.IsDeleted);
        }

        public async Task<IEnumerable<ObHistory>> GetByPatientIdAsync(Guid patientId, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return Enumerable.Empty<ObHistory>();
            return await context.ObHistory.Where(ob => ob.PatientId == patientId && !ob.IsDeleted).ToListAsync();
        }

        public async Task UpdateAsync(ObHistory entity, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            context.ObHistory.Update(entity);
            await context.SaveChangesAsync();
        }

        public async Task DeleteByEntityAsync(ObHistory entity, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            entity.IsDeleted = true;
            context.ObHistory.Update(entity);
            await context.SaveChangesAsync();
        }

        public async Task DeleteByIdAsync(Guid obId, Guid OrgId, bool Subscription)
        {
            var entity = await GetByIdAsync(obId, OrgId, Subscription);
            if (entity != null)
            {
                entity.IsDeleted = true;
                await UpdateAsync(entity, OrgId, Subscription);
            }
        }

        public async Task UpdateObHistoryListAsync(List<ObHistory> obHistoryList, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;

            if (obHistoryList == null || obHistoryList.Count == 0)
                throw new ArgumentException("Invalid records provided.", nameof(obHistoryList));

            foreach (var obHistory in obHistoryList)
            {
                var existingObHistory = await context.ObHistory
                    .FirstOrDefaultAsync(ob => ob.obId == obHistory.obId);

                if (existingObHistory != null)
                {
                    existingObHistory.Notes = obHistory.Notes;
                    existingObHistory.Symptoms = obHistory.Symptoms;
                    existingObHistory.OrganizationId = obHistory.OrganizationId;
                    existingObHistory.PcpId = obHistory.PcpId;
                    existingObHistory.DateOfComplaint = obHistory.DateOfComplaint;
                    existingObHistory.IsDeleted = obHistory.IsDeleted;
                    context.ObHistory.Update(existingObHistory);
                }
            }
            await context.SaveChangesAsync();
        }
    }
}
