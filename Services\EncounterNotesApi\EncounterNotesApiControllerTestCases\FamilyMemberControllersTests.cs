using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;
using EncounterNotesDataAccessLayer.EncounterNotesContext;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class FamilyMemberControllersTests
    {
        private Mock<IFamilyMemberCommandHandler<FamilyMember>> _familyMemberCommandHandlerMock;
        private Mock<IFamilyMemberQueryHandler<FamilyMember>> _familyMemberQueryHandlerMock;
        private Mock<ILogger<FamilyMemberController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;

        private FamilyMemberController _controller;
        private List<FamilyMember> _testFamilyMembers;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _familyMemberCommandHandlerMock = new Mock<IFamilyMemberCommandHandler<FamilyMember>>();
            _familyMemberQueryHandlerMock = new Mock<IFamilyMemberQueryHandler<FamilyMember>>();
            _loggerMock = new Mock<ILogger<FamilyMemberController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();

            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["SuccessfulDeletion"]).Returns(new LocalizedString("SuccessfulDeletion", "Deletion successful."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting data."));
            _localizerMock.Setup(x => x["InvalidAddressData"]).Returns(new LocalizedString("InvalidAddressData", "Invalid address data."));
            _localizerMock.Setup(x => x["AddressInvalidMessage"]).Returns(new LocalizedString("AddressInvalidMessage", "Address data is invalid."));
            _localizerMock.Setup(x => x["AddingNewAddress"]).Returns(new LocalizedString("AddingNewAddress", "Adding new address."));
            _localizerMock.Setup(x => x["AddressAddedSuccessfully"]).Returns(new LocalizedString("AddressAddedSuccessfully", "Address added successfully."));
            _localizerMock.Setup(x => x["AddSuccessful"]).Returns(new LocalizedString("AddSuccessful", "Add successful."));
            _localizerMock.Setup(x => x["ErrorAddingAddress"]).Returns(new LocalizedString("ErrorAddingAddress", "Error adding address."));
            _localizerMock.Setup(x => x["InternalServerErrorMessage"]).Returns(new LocalizedString("InternalServerErrorMessage", "Internal server error."));
            _localizerMock.Setup(x => x["AccessError"]).Returns(new LocalizedString("AccessError", "Access error."));
            _localizerMock.Setup(x => x["Record not found"]).Returns(new LocalizedString("Record not found", "Record not found."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));

            // Create test data
            _testFamilyMembers = new List<FamilyMember>
            {
                new FamilyMember
                {
                    RecordID = Guid.NewGuid(),
                    OrganizationID = _orgId,
                    Relation = "Father",
                    Status = "Living",
                    DOB = DateTime.Now.AddYears(-60),
                    Age = 60,
                    Notes = "Healthy",
                    PatientId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Subscription = _isSubscription
                },
                new FamilyMember
                {
                    RecordID = Guid.NewGuid(),
                    OrganizationID = _orgId,
                    Relation = "Mother",
                    Status = "Living",
                    DOB = DateTime.Now.AddYears(-55),
                    Age = 55,
                    Notes = "Hypertension",
                    PatientId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Subscription = _isSubscription
                }
            };

            _controller = new FamilyMemberController(
                _familyMemberQueryHandlerMock.Object,
                _familyMemberCommandHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object,
                null // Pass null for the context parameter
            );
        }

        [Test]
        public async Task GetById_ReturnsOk_WhenFamilyMembersExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var familyMembers = _testFamilyMembers.ToList();
            _familyMemberQueryHandlerMock.Setup(x => x.GetFamilyMembersById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(familyMembers);

            // Act
            var result = await _controller.GetById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().BeEquivalentTo(familyMembers);
        }

        [Test]
        public async Task GetById_ReturnsNotFound_WhenNoFamilyMembersExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _familyMemberQueryHandlerMock.Setup(x => x.GetFamilyMembersById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(new List<FamilyMember>());

            // Act
            var result = await _controller.GetById(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task UpdateById_ReturnsOk_WhenUpdateSuccessful()
        {
            // Arrange
            var familyMember = _testFamilyMembers[0];
            var id = familyMember.RecordID;

            // Act
            var result = await _controller.UpdateById(id, familyMember, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _familyMemberCommandHandlerMock.Verify(x => x.UpdateFamilyMember(familyMember, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateById_ReturnsBadRequest_WhenIdMismatch()
        {
            // Arrange
            var familyMember = _testFamilyMembers[0];
            var wrongId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdateById(wrongId, familyMember, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["InvalidRecord"]);

            // Verify the command handler was not called
            _familyMemberCommandHandlerMock.Verify(x => x.UpdateFamilyMember(It.IsAny<FamilyMember>(), _orgId, _isSubscription), Times.Never);
        }

        [Test]
        public async Task DeleteById_ReturnsOk_WhenDeleteSuccessful()
        {
            // Arrange
            var id = Guid.NewGuid();

            // Act
            var result = await _controller.DeleteById(id, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulDeletion"]);

            // Verify the command handler was called
            _familyMemberCommandHandlerMock.Verify(x => x.DeleteFamilyMemberById(id, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task AddFamilyMember_ReturnsOk_WhenAddSuccessful()
        {
            // Arrange
            var familyMembers = _testFamilyMembers;

            // Act
            var result = await _controller.AddFamilyMember(familyMembers, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["AddSuccessful"]);

            // Verify the command handler was called
            _familyMemberCommandHandlerMock.Verify(x => x.AddFamilyMember(familyMembers, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task AddFamilyMember_ReturnsBadRequest_WhenEmptyList()
        {
            // Arrange
            var emptyList = new List<FamilyMember>();

            // Act
            var result = await _controller.AddFamilyMember(emptyList, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["AddressInvalidMessage"]);

            // Verify the command handler was not called
            _familyMemberCommandHandlerMock.Verify(x => x.AddFamilyMember(It.IsAny<List<FamilyMember>>(), _orgId, _isSubscription), Times.Never);
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsOk_WhenActiveFamilyMembersExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activeFamilyMembers = _testFamilyMembers.Where(f => f.IsActive).ToList();
            _familyMemberQueryHandlerMock.Setup(x => x.GetFamilyMemberByIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activeFamilyMembers);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().BeEquivalentTo(activeFamilyMembers);
        }

        [Test]
        public async Task UpdateAccess_ReturnsOk_WhenUpdateSuccessful()
        {
            // Arrange
            var familyMembers = _testFamilyMembers;
            _familyMemberCommandHandlerMock.Setup(x => x.UpdateFamilyMemberList(familyMembers, _orgId, _isSubscription))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.UpdateAccess(familyMembers, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            // The value is an anonymous type, so we can't check its exact type

            // Verify the command handler was called
            _familyMemberCommandHandlerMock.Verify(x => x.UpdateFamilyMemberList(familyMembers, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateAccess_ReturnsNotFound_WhenUpdateFails()
        {
            // Arrange
            var familyMembers = _testFamilyMembers;
            _familyMemberCommandHandlerMock.Setup(x => x.UpdateFamilyMemberList(familyMembers, _orgId, _isSubscription))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.UpdateAccess(familyMembers, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["Record not found"]);

            // Verify the command handler was called
            _familyMemberCommandHandlerMock.Verify(x => x.UpdateFamilyMemberList(familyMembers, _orgId, _isSubscription), Times.Once);
        }


    }
}
