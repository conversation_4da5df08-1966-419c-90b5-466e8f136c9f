﻿using EncounterNotesContracts;
using ShardModels;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface IRecordCommandHandler<TText>
    {
        Task DeleteRecordByEntity(Record record, Guid OrgId, bool Subscription);
        Task DeleteRecordById(Guid id, Guid OrgId, bool Subscription);
        Task AddRecord(List<TText> texts, Guid OrgId, bool Subscription);
        Task UpdateRecord(Record record, Guid OrgId, bool Subscription);
    }
}
