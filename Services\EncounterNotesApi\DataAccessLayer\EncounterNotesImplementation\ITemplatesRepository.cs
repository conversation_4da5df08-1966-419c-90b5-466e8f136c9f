﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface ITemplatesRepository : IShardGenericRepository<Templates>
    {
        Task<List<Templates>> GetTemplatesByPCPId(Guid PCPId, Guid OrgId, bool Subscription);
        Task<List<Templates>> GetTemplatesByPCPIdAndVisitType(Guid PCPId, String VisitType, Guid OrgId, bool Subscription);
    }
}