﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IPhysicalTherapyRepository : IShardGenericRepository<PhysicalTherapyData>
    {
        Task<IEnumerable<PhysicalTherapyData>> GetAllPhysicalTherapyAsync(Guid OrgId, bool Subscription);
    }
}
