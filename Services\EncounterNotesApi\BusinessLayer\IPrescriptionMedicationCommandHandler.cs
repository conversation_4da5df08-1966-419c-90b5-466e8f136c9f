﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface IPrescriptionMedicationCommandHandler<TText>
    {
        Task DeleteMedicationByEntity(PrescriptionMedication Pmedication, Guid OrgId, bool Subscription);
        Task DeleteMedicationById(Guid id, Guid OrgId, bool Subscription);
        Task AddMedication(List<TText> texts, Guid OrgId, bool Subscription);
        Task UpdateMedication(PrescriptionMedication Pmedication, Guid OrgId, bool Subscription);
        Task UpdateMedicationList(List<PrescriptionMedication> PMedications, Guid OrgId, bool Subscription);
    }
}
