using NUnit.Framework;
using Moq;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class BillingQueryHandlerTests
    {
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IBillingRepository> _billingRepositoryMock;
        private BillingQueryHandler _billingQueryHandler;
        private Guid _patientId;

        [SetUp]
        public void Setup()
        {
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _billingRepositoryMock = new Mock<IBillingRepository>();
            _patientId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.BillingRepository).Returns(_billingRepositoryMock.Object);
            _billingQueryHandler = new BillingQueryHandler(_unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetBillingByIdAsync_ShouldReturnBillingWithMatchingId()
        {
            // Arrange
            var billingId = Guid.NewGuid();
            var billing = new Billing
            {
                BillingId = billingId,
                PatientId = _patientId,
                VisitTypeId = Guid.NewGuid(),
                AssessmentId = Guid.NewGuid(),
                ProcedureCodeId = Guid.NewGuid(),
                EMCodeId = Guid.NewGuid(),
                BillingNotes = "Follow-up visit",
                Amount = 150.00m,
                CreatedDate = DateTime.UtcNow.AddDays(-5)
            };

            _billingRepositoryMock.Setup(r => r.GetByIdAsync(billingId))
                .ReturnsAsync(billing);

            // Act
            var result = await _billingQueryHandler.GetBillingByIdAsync(billingId);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(billing);
        }

        [Test]
        public async Task GetBillingByIdAsync_WithNonExistentId_ShouldReturnNull()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid();

            _billingRepositoryMock.Setup(r => r.GetByIdAsync(nonExistentId))
                .ReturnsAsync((Billing)null);

            // Act
            var result = await _billingQueryHandler.GetBillingByIdAsync(nonExistentId);

            // Assert
            result.Should().BeNull();
        }

        [Test]
        public async Task GetAllBillingsAsync_ShouldReturnAllBillings()
        {
            // Arrange
            var billings = new List<Billing>
            {
                new Billing
                {
                    BillingId = Guid.NewGuid(),
                    PatientId = _patientId,
                    VisitTypeId = Guid.NewGuid(),
                    AssessmentId = Guid.NewGuid(),
                    ProcedureCodeId = Guid.NewGuid(),
                    EMCodeId = Guid.NewGuid(),
                    BillingNotes = "Initial consultation",
                    Amount = 200.00m,
                    CreatedDate = DateTime.UtcNow.AddDays(-10)
                },
                new Billing
                {
                    BillingId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    VisitTypeId = Guid.NewGuid(),
                    AssessmentId = Guid.NewGuid(),
                    ProcedureCodeId = Guid.NewGuid(),
                    EMCodeId = Guid.NewGuid(),
                    BillingNotes = "Follow-up visit",
                    Amount = 150.00m,
                    CreatedDate = DateTime.UtcNow.AddDays(-5)
                }
            };

            _billingRepositoryMock.Setup(r => r.GetAllAsync())
                .ReturnsAsync(billings);

            // Act
            var result = await _billingQueryHandler.GetAllBillingsAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.Should().BeEquivalentTo(billings);
        }

        [Test]
        public async Task GetAllBillingsAsync_WithNoBillings_ShouldReturnEmptyCollection()
        {
            // Arrange
            var billings = new List<Billing>();

            _billingRepositoryMock.Setup(r => r.GetAllAsync())
                .ReturnsAsync(billings);

            // Act
            var result = await _billingQueryHandler.GetAllBillingsAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetBillingByPatientIdAsync_ShouldReturnBillingsForPatient()
        {
            // Arrange
            var billings = new List<Billing>
            {
                new Billing
                {
                    BillingId = Guid.NewGuid(),
                    PatientId = _patientId,
                    VisitTypeId = Guid.NewGuid(),
                    AssessmentId = Guid.NewGuid(),
                    ProcedureCodeId = Guid.NewGuid(),
                    EMCodeId = Guid.NewGuid(),
                    BillingNotes = "Initial consultation",
                    Amount = 200.00m,
                    CreatedDate = DateTime.UtcNow.AddDays(-10)
                },
                new Billing
                {
                    BillingId = Guid.NewGuid(),
                    PatientId = _patientId,
                    VisitTypeId = Guid.NewGuid(),
                    AssessmentId = Guid.NewGuid(),
                    ProcedureCodeId = Guid.NewGuid(),
                    EMCodeId = Guid.NewGuid(),
                    BillingNotes = "Follow-up visit",
                    Amount = 150.00m,
                    CreatedDate = DateTime.UtcNow.AddDays(-5)
                }
            };

            _billingRepositoryMock.Setup(r => r.GetByPatientIdAsync(_patientId))
                .ReturnsAsync(billings);

            // Act
            var result = await _billingQueryHandler.GetBillingByPatientIdAsync(_patientId);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(b => b.PatientId == _patientId).Should().BeTrue();
        }

        [Test]
        public async Task GetBillingByPatientIdAsync_WithNoBillingsForPatient_ShouldReturnEmptyCollection()
        {
            // Arrange
            var billings = new List<Billing>();

            _billingRepositoryMock.Setup(r => r.GetByPatientIdAsync(_patientId))
                .ReturnsAsync(billings);

            // Act
            var result = await _billingQueryHandler.GetBillingByPatientIdAsync(_patientId);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
