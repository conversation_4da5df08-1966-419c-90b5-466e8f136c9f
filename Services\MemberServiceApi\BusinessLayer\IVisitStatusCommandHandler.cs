﻿using System;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IVisitStatusCommandHandler<T>
    {
        Task UpdateVisitStatusAsync(T visitStatus, Guid orgId, bool subscription);
        Task DeleteVisitStatusByIdAsync(Guid id, Guid orgId, bool subscription);
        Task DeleteVisitStatusByEntityAsync(T visitStatus, Guid orgId, bool subscription);
    }
}
