﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IPrescriptionMedicationQueryHandler<TText>
    {
        Task<IEnumerable<PrescriptionMedication>> GetMedicationByIdAndIsActive(Guid id, Guid OrgId, bool Subscription);
        Task<IEnumerable<PrescriptionMedication>> GetAllMedicationsbyId(Guid id, Guid OrgId, bool Subscription);
    }
}
