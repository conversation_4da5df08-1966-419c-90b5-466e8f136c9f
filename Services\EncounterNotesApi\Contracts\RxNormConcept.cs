﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesContracts
{
    public class RxNormConcept : IContract
    {
        public string RXCUI { get; set; }
        public string LAT { get; set; }
        public string? TS { get; set; }
        public string? LUI { get; set; }
        public string? STT { get; set; }
        public string? SUI { get; set; }
        public string? ISPREF { get; set; }
        public string RXAUI { get; set; }
        public string SAUI { get; set; }
        public string SCUI { get; set; }
        public string? SDUI { get; set; }
        public string SAB { get; set; }
        public string TTY { get; set; }
        public string CODE { get; set; }
        public string STR { get; set; }
        public string? SRL { get; set; }
        public string SUPPRESS { get; set; }
        public string CVF { get; set; }
        
    }
}
