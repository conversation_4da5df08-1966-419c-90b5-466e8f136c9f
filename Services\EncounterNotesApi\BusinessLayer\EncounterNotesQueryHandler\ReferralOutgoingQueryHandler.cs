﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class ReferralOutgoingQueryHandler : IReferralOutgoingQueryHandler<PatientReferralOutgoing>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public ReferralOutgoingQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<PatientReferralOutgoing>> GetPatientReferralOutgoingByIdAndIsActive(Guid id, Guid OrgId, bool Subscription)
        {
            var PatientReferralOutgoing = await _unitOfWork.ReferralOutgoingRepository.GetAsync(OrgId, Subscription);

            return PatientReferralOutgoing.Where(PatientReferralOutgoing => PatientReferralOutgoing.PatientId == id && PatientReferralOutgoing.isActive == true);
        }

        public async Task<IEnumerable<PatientReferralOutgoing>> GetAllPatientReferralOutgoingbyId(Guid id, Guid OrgId, bool Subscription)
        {
            var PatientReferralOutgoing = await _unitOfWork.ReferralOutgoingRepository.GetAsync(OrgId, Subscription);

            return PatientReferralOutgoing.Where(PatientReferralOutgoing => PatientReferralOutgoing.PatientId == id);
        }
    }
}
