﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using ShardModels;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]

    public class SurgicalHistoryController : ControllerBase
    {
        private readonly ISurgicalHistoryCommandHandler<SurgicalHistory> _surgicalDataHandler;
        private readonly ISurgicalHistoryQueryHandler<SurgicalHistory> _surgicalQueryHandler;
        private readonly ILogger<SurgicalHistoryController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;


        public SurgicalHistoryController(
            ISurgicalHistoryCommandHandler<SurgicalHistory> surgicalDataHandler,
            ISurgicalHistoryQueryHandler<SurgicalHistory> surgicalQueryHandler,
            ILogger<SurgicalHistoryController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
            )
        {
            _surgicalDataHandler = surgicalDataHandler;
            _surgicalQueryHandler = surgicalQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        /// Get All Details By Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<SurgicalHistory>>> GetAllById(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult result;
            try
            {
                var data = await _surgicalQueryHandler.GetAllSurgeriesById(id, orgId, isSubscription);
                result = data != null ? Ok(data) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Get The Details which are currently Active
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id:guid}/IsActive/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<SurgicalHistory>>> GetAllByIdAndIsActive(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult result;
            try
            {
                var surgery = await _surgicalQueryHandler.GetSurgeryByIdAndIsActive(id, orgId, isSubscription);
                result = surgery != null ? Ok(surgery) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Add New Surgery
        /// </summary>
        /// <param name="surgeries"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddSurgery/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> Registration([FromBody] List<SurgicalHistory> surgeries, Guid orgId, bool isSubscription)
        {
            if (surgeries == null || surgeries.Count == 0)
            {
                return BadRequest(_localizer["NoLicense"]);
            }

            try
            {
                await _surgicalDataHandler.AddSurgery(surgeries, orgId, isSubscription);
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException ex)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Delete Surgery
        /// </summary>
        /// <param name="surgery"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> DeleteByEntity([FromBody] SurgicalHistory surgery, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            {
                if (surgery == null)
                {
                    result = BadRequest(_localizer["InvalidRecord"]);
                }
                else
                {
                    try
                    {
                        await _surgicalDataHandler.DeleteSurgeryByEntity(surgery, orgId, isSubscription);
                        result = Ok(_localizer["DeleteSuccessful"]);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, _localizer["DeleteLogError"]);
                        result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
                    }
                }
                return result;
            }
        }

        /// <summary>
        /// Update Surgery By Id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="SurHistory"></param>
        /// <returns></returns>
        [HttpPut("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateSurgeryById(Guid id, [FromBody] SurgicalHistory SurHistory, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            {
                if (SurHistory == null || SurHistory.PatientId != id)
                {
                    result = BadRequest(_localizer["InvalidRecord"]);
                }
                else
                {
                    try
                    {
                        await _surgicalDataHandler.UpdateSurgery(SurHistory, orgId, isSubscription);
                        result = Ok(_localizer["UpdateSuccessful"]);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, _localizer["UpdateLogError"]);
                        result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                    }
                }
                return result;
            }
        }

        /// <summary>
        /// Update Surgery(List)
        /// </summary>
        /// <param name="surgicalHistory"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("UpdateSurgeryList/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateSurgeryList([FromBody] List<SurgicalHistory> surgicalHistory, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            try
            {
                await _surgicalDataHandler.UpdateSurgeryList(surgicalHistory, orgId, isSubscription);
                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);

                result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
            }
            return result;
        }

    }
}