using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class ProcedureControllerTests
    {
        private Mock<IProcedureCommandHandler<Procedure>> _procedureCommandHandlerMock;
        private Mock<IProcedureQueryHandler<Procedure>> _procedureQueryHandlerMock;
        private Mock<ILogger<ProcedureController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private ProcedureController _controller;
        private List<Procedure> _testProcedures;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _procedureCommandHandlerMock = new Mock<IProcedureCommandHandler<Procedure>>();
            _procedureQueryHandlerMock = new Mock<IProcedureQueryHandler<Procedure>>();
            _loggerMock = new Mock<ILogger<ProcedureController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetProcedureError"]).Returns(new LocalizedString("GetProcedureError", "Error retrieving procedures."));
            _localizerMock.Setup(x => x["NoProcedureProvided"]).Returns(new LocalizedString("NoProcedureProvided", "No procedure provided."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error occurred."));
            _localizerMock.Setup(x => x["AddProcedureError"]).Returns(new LocalizedString("AddProcedureError", "Error adding procedure."));
            _localizerMock.Setup(x => x["InvalidProcedure"]).Returns(new LocalizedString("InvalidProcedure", "Invalid procedure."));
            _localizerMock.Setup(x => x["UpdateProcedureError"]).Returns(new LocalizedString("UpdateProcedureError", "Error updating procedure."));
            _localizerMock.Setup(x => x["UpdateProcedureListError"]).Returns(new LocalizedString("UpdateProcedureListError", "Error updating procedure list."));
            _localizerMock.Setup(x => x["ProcedureAdded"]).Returns(new LocalizedString("ProcedureAdded", "Procedure added."));
            _localizerMock.Setup(x => x["ProcedureUpdated"]).Returns(new LocalizedString("ProcedureUpdated", "Procedure updated."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testProcedures = new List<Procedure>
            {
                new Procedure {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    CPTCode = "99213",
                    Description = "Office visit, established patient",
                    Notes = "Routine follow-up",
                    OrderDate = DateTime.Now.AddDays(-30),
                    LastUpdatedDate = DateTime.Now.AddDays(-25),
                    CreatedByUserId = Guid.NewGuid(),
                    UpdatedByUserId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    OrderedBy = "Dr. Smith",
                    PcpId = Guid.NewGuid(),
                    Subscription = _isSubscription,
                    IsDeleted = false,
                    AssessmentData = "Hypertension",
                    AssessmentId = Guid.NewGuid()
                },
                new Procedure {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    CPTCode = "73610",
                    Description = "X-ray of ankle",
                    Notes = "Left ankle pain",
                    OrderDate = DateTime.Now.AddDays(-20),
                    LastUpdatedDate = DateTime.Now.AddDays(-15),
                    CreatedByUserId = Guid.NewGuid(),
                    UpdatedByUserId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    OrderedBy = "Dr. Johnson",
                    PcpId = Guid.NewGuid(),
                    Subscription = _isSubscription,
                    IsDeleted = false,
                    AssessmentData = "Ankle sprain",
                    AssessmentId = Guid.NewGuid()
                }
            };

            _controller = new ProcedureController(
                _procedureCommandHandlerMock.Object,
                _procedureQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetByPatientId_ReturnsProcedures_WhenProceduresExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientProcedures = _testProcedures.Select(p => { p.PatientId = patientId; return p; }).ToList();
            _procedureQueryHandlerMock.Setup(x => x.GetProcedureByPatientId(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientProcedures);

            // Act
            var result = await _controller.GetByPatientId(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedProcedures = okResult.Value as IEnumerable<Procedure>;
            returnedProcedures.Should().NotBeNull();
            returnedProcedures.Should().BeEquivalentTo(patientProcedures);
        }

        [Test]
        public async Task GetByPatientId_ReturnsNotFound_WhenNoProceduresExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _procedureQueryHandlerMock.Setup(x => x.GetProcedureByPatientId(patientId, _orgId, _isSubscription))
                .ReturnsAsync((IEnumerable<Procedure>)null);

            // Act
            var result = await _controller.GetByPatientId(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task AddProcedure_ValidProcedures_ReturnsOk()
        {
            // Arrange
            var procedures = _testProcedures;

            // Act
            var result = await _controller.AddProcedure(procedures, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["ProcedureAdded"]);

            // Verify the command handler was called
            _procedureCommandHandlerMock.Verify(x => x.AddProcedure(procedures, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task AddProcedure_EmptyList_ReturnsBadRequest()
        {
            // Arrange
            var emptyProcedures = new List<Procedure>();

            // Act
            var result = await _controller.AddProcedure(emptyProcedures, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoProcedureProvided"]);
        }

        [Test]
        public async Task UpdateProcedure_ValidProcedure_ReturnsOk()
        {
            // Arrange
            var procedure = _testProcedures[0];

            // Act
            var result = await _controller.UpdateProcedure(procedure.Id, procedure, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["ProcedureUpdated"]);

            // Verify the command handler was called
            _procedureCommandHandlerMock.Verify(x => x.UpdateProcedure(procedure, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateProcedure_InvalidId_ReturnsBadRequest()
        {
            // Arrange
            var procedure = _testProcedures[0];
            var differentId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdateProcedure(differentId, procedure, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["InvalidProcedure"]);
        }

        [Test]
        public async Task UpdateAssessmentsList_ValidProcedures_ReturnsOk()
        {
            // Arrange
            var procedures = _testProcedures;

            // Act
            var result = await _controller.UpdateAssessmentsList(procedures, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["ProcedureUpdated"]);

            // Verify the command handler was called
            _procedureCommandHandlerMock.Verify(x => x.UpdateProcedureListAsync(procedures, _orgId, _isSubscription), Times.Once);
        }
    }
}
