using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class PastResultsControllerTests
    {
        private Mock<IPastResultsCommandHandler<PastResults>> _pastResultsCommandHandlerMock;
        private Mock<IPastResultsQueryHandler<PastResults>> _pastResultsQueryHandlerMock;
        private Mock<ILogger<PastResultsController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private PastResultsController _controller;
        private List<PastResults> _testPastResults;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _pastResultsCommandHandlerMock = new Mock<IPastResultsCommandHandler<PastResults>>();
            _pastResultsQueryHandlerMock = new Mock<IPastResultsQueryHandler<PastResults>>();
            _loggerMock = new Mock<ILogger<PastResultsController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No license available."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error occurred."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error posting data."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting data."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Delete successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testPastResults = new List<PastResults>
            {
                new PastResults {
                    ResultId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    OrderName = "Complete Blood Count",
                    OrderDate = DateTime.Now.AddDays(-30),
                    ResultDate = DateTime.Now.AddDays(-28),
                    OrderType = "Laboratory",
                    OrderBy = "Dr. Smith",
                    ViewResults = "WBC: 7.5, RBC: 4.8, Hgb: 14.2, Hct: 42.1, Platelets: 250",
                    IsActive = true,
                    Subscription = _isSubscription
                },
                new PastResults {
                    ResultId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-15),
                    OrderName = "Chest X-Ray",
                    OrderDate = DateTime.Now.AddDays(-20),
                    ResultDate = DateTime.Now.AddDays(-19),
                    OrderType = "Radiology",
                    OrderBy = "Dr. Johnson",
                    ViewResults = "No acute cardiopulmonary process. Heart size normal. Lungs clear.",
                    IsActive = true,
                    Subscription = _isSubscription
                }
            };

            _controller = new PastResultsController(
                _pastResultsCommandHandlerMock.Object,
                _pastResultsQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetAllById_ReturnsPastResults_WhenPastResultsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientPastResults = _testPastResults.Select(pr => { pr.PatientId = patientId; return pr; }).ToList();
            _pastResultsQueryHandlerMock.Setup(x => x.GetAllResultsById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientPastResults);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedPastResults = okResult.Value as IEnumerable<PastResults>;
            returnedPastResults.Should().NotBeNull();
            returnedPastResults.Should().BeEquivalentTo(patientPastResults);
        }

        [Test]
        public async Task GetAllById_ReturnsNotFound_WhenNoPastResultsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _pastResultsQueryHandlerMock.Setup(x => x.GetAllResultsById(patientId, _orgId, _isSubscription))
                .ReturnsAsync((IEnumerable<PastResults>)null);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsPastResults_WhenActivePastResultsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activePastResults = _testPastResults.Select(pr => { pr.PatientId = patientId; pr.IsActive = true; return pr; }).ToList();
            _pastResultsQueryHandlerMock.Setup(x => x.GetResultByIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activePastResults);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedPastResults = okResult.Value as IEnumerable<PastResults>;
            returnedPastResults.Should().NotBeNull();
            returnedPastResults.Should().BeEquivalentTo(activePastResults);
        }

        [Test]
        public async Task Registration_ValidPastResults_ReturnsOk()
        {
            // Arrange
            var pastResults = _testPastResults;

            // Act
            var result = await _controller.Registration(pastResults, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _pastResultsCommandHandlerMock.Verify(x => x.AddResult(pastResults, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task Registration_EmptyList_ReturnsOk()
        {
            // Arrange
            var emptyPastResults = new List<PastResults>();

            // Act
            var result = await _controller.Registration(emptyPastResults, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);
        }

        [Test]
        public async Task DeleteByEntity_ValidPastResult_ReturnsOk()
        {
            // Arrange
            var pastResult = _testPastResults[0];

            // Act
            var result = await _controller.DeleteByEntity(pastResult, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _pastResultsCommandHandlerMock.Verify(x => x.DeleteResultsByEntity(pastResult, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateResultById_ValidPastResult_ReturnsOk()
        {
            // Arrange
            var pastResult = _testPastResults[0];

            // Act
            var result = await _controller.UpdateResultById(pastResult.PatientId, pastResult, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _pastResultsCommandHandlerMock.Verify(x => x.UpdateResult(pastResult, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdatePastList_ValidPastResults_ReturnsOk()
        {
            // Arrange
            var pastResults = _testPastResults;

            // Act
            var result = await _controller.UpdatePastList(pastResults, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _pastResultsCommandHandlerMock.Verify(x => x.UpdatePastList(pastResults, _orgId, _isSubscription), Times.Once);
        }


    }
}
