﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class FDBRoutedDosageFormMedicationRepository : GenericRepository<FDBRoutedDosageFormMedication>, IFDBRoutedDosageFormMedicationRepository
    {
        private readonly RecordDatabaseContext _context;

        public FDBRoutedDosageFormMedicationRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
        }

        public async Task<List<FDBRoutedDosageFormMedication>> GetRoutedDosageFormMedicationByROUTED_MED_ID(string ROUTED_MED_ID)
        {
            var medication = await _context.FDBRoutedDosageFormMedications
            .Where(t => t.ROUTED_MED_ID == ROUTED_MED_ID)
            .ToListAsync();
            return medication;
        }
    }
}