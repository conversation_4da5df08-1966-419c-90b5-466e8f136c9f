﻿using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using Azure.Storage.Blobs.Models;
using FFmpegBlazor;
using FFMpegCore;
using FFMpegCore.Pipes;
using Xabe.FFmpeg;
using Concentus.Oggfile;
using Concentus.Structs;
using Microsoft.JSInterop;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.Extensions.Localization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using System.IO;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [ApiController]
    [Route("[controller]")]
    public class SpeechController : ControllerBase
    {
        private readonly ISpeechDataHandler<Speech> _speechHandler;
        private readonly ILogger<SpeechController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;

        public SpeechController(ISpeechDataHandler<Speech> speechHandler, ILogger<SpeechController> logger, IStringLocalizer<EncounterNotesServiceStrings> localizer)
        {
            _speechHandler = speechHandler;
            _logger = logger;
            _localizer = localizer;

        }
        [HttpPost("{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<Guid?>> TranscribeAsync(SpeechRequest speechRequest, Guid orgId, bool isSubscription)
        {
            ActionResult<Guid?> result;
            try
            {
                Guid? firstRecordId = await _speechHandler.Handle(speechRequest, orgId, isSubscription);
                if (firstRecordId.HasValue)
                {
                    result= Ok(new { Message = _localizer["SuccessfulEncounterNote"], FirstRecordId = firstRecordId });
                }
                else
                {
                    result= BadRequest(_localizer["EncounterNoteFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["SpeechProcessingError"]);
                result= StatusCode(int.Parse(_localizer["500"]));
            }
            return result;
        }


        [HttpPost("upload")]
        [RequestSizeLimit(1048576000)]
        [RequestFormLimits(MultipartBodyLengthLimit = 1048576000)] 
        public async Task<IActionResult> UploadAudio(IFormFile audioFile)
        {
            IActionResult result;
            
            _logger.LogInformation(_localizer["UploadEndpointHit"]);

            if (audioFile == null || audioFile.Length == 0)
            {
                _logger.LogWarning(_localizer["NoFilesFound"]);
                result = BadRequest(_localizer["NoFilesFound"]);
            }
            else
            {
                try
                {
                    var outputFileStream = new MemoryStream();
                    await audioFile.CopyToAsync(outputFileStream);
                    outputFileStream.Position = 0;
                    var outputFileName = $"{Path.GetFileNameWithoutExtension(audioFile.FileName)}.{_localizer["webm"]}";
                    await _speechHandler.UploadToBlobAsync(outputFileStream, outputFileName);

                    _logger.LogInformation($"{outputFileName} {_localizer["UploadSuccessful"]}");
                    result = File(outputFileStream.ToArray(), _localizer["audio/webm"], outputFileName);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorProcessingAudioFile"]);
                    result = StatusCode(int.Parse(_localizer["500"]), ex.Message);
                }
            }

            return result;
        }
    }
}
