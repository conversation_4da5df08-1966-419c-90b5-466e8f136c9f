using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class HospitalizationRecordCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<HospitalizationRecordCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, HospitalizationRecord, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IHospitalizationRecordRepository> _hospitalizationRecordRepositoryMock;
        private HospitalizationRecordCommandHandler _hospitalizationRecordCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<HospitalizationRecordCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, HospitalizationRecord, EncounterDataAccessLayer>>();
            _hospitalizationRecordRepositoryMock = new Mock<IHospitalizationRecordRepository>();

            _unitOfWorkMock.Setup(u => u.HospitalizationRecordRepository).Returns(_hospitalizationRecordRepositoryMock.Object);

            _hospitalizationRecordCommandHandler = new HospitalizationRecordCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task AddHospitalizationRecord_ValidRecords_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var hospitalizationRecords = new List<HospitalizationRecord>
            {
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    Reason = "Pneumonia",
                    Date = DateTime.Now.AddDays(-30),
                    OrganizationID = _orgId,
                    PatientId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now,
                    Subscription = _subscription
                }
            };

            // Act
            await _hospitalizationRecordCommandHandler.AddHospitalizationRecord(hospitalizationRecords, _orgId, _subscription);

            // Assert
            _hospitalizationRecordRepositoryMock.Verify(r => r.AddAsync(hospitalizationRecords, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateHospitalizationRecord_ValidRecord_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var hospitalizationRecord = new HospitalizationRecord
            {
                RecordID = Guid.NewGuid(),
                Reason = "Updated reason",
                Date = DateTime.Now.AddDays(-20),
                OrganizationID = _orgId,
                PatientId = Guid.NewGuid(),
                IsActive = true,
                UpdatedBy = Guid.NewGuid(),
                UpdatedDate = DateTime.Now,
                Subscription = _subscription
            };

            // Act
            await _hospitalizationRecordCommandHandler.UpdateHospitalizationRecord(hospitalizationRecord, _orgId, _subscription);

            // Assert
            _hospitalizationRecordRepositoryMock.Verify(r => r.UpdateAsync(hospitalizationRecord, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteHospitalizationRecordById_ValidId_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var id = Guid.NewGuid();

            // Act
            await _hospitalizationRecordCommandHandler.DeleteHospitalizationRecordById(id, _orgId, _subscription);

            // Assert
            _hospitalizationRecordRepositoryMock.Verify(r => r.DeleteByIdAsync(id, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateHospitalizationRecordList_ValidRecords_ShouldUpdateExistingRecordsAndSave()
        {
            // Arrange
            var existingRecords = new List<HospitalizationRecord>
            {
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    Reason = "Original reason",
                    Date = DateTime.Now.AddDays(-30),
                    OrganizationID = _orgId,
                    PatientId = Guid.NewGuid(),
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            var updatedRecords = new List<HospitalizationRecord>
            {
                new HospitalizationRecord
                {
                    RecordID = existingRecords[0].RecordID,
                    Reason = "Updated reason",
                    Date = DateTime.Now.AddDays(-20),
                    OrganizationID = _orgId,
                    PatientId = existingRecords[0].PatientId,
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _hospitalizationRecordRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(existingRecords);

            // Act
            var result = await _hospitalizationRecordCommandHandler.UpdateHospitalizationRecordList(updatedRecords, _orgId, _subscription);

            // Assert
            result.Should().BeTrue();
            _hospitalizationRecordRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<HospitalizationRecord>(), _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}
