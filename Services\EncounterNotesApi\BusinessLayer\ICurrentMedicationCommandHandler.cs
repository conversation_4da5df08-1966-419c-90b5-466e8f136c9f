﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface ICurrentMedicationCommandHandler<TText>
    {
        Task DeleteMedicationByEntity(CurrentMedication currentmedication, Guid OrgId, bool Subscription);

        Task DeleteMedicationById(Guid id, Guid OrgId, bool Subscription);

        Task AddMedication(List<TText> texts, Guid OrgId, bool Subscription);

        Task UpdateMedication(CurrentMedication currentmedication, Guid OrgId, bool Subscription);

        Task UpdateMedicationList(List<CurrentMedication> Medications, Guid OrgId, bool Subscription);

    }
}
