using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class HospitalizationRecordQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IHospitalizationRecordRepository> _hospitalizationRecordRepositoryMock;
        private HospitalizationRecordQueryHandler _hospitalizationRecordQueryHandler;
        private Guid _orgId;
        private bool _subscription;
        private Guid _patientId;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _hospitalizationRecordRepositoryMock = new Mock<IHospitalizationRecordRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;
            _patientId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.HospitalizationRecordRepository).Returns(_hospitalizationRecordRepositoryMock.Object);
            _hospitalizationRecordQueryHandler = new HospitalizationRecordQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetHospitalizationRecordById_ShouldReturnAllHospitalizationRecordsForPatient()
        {
            // Arrange
            var hospitalizationRecords = new List<HospitalizationRecord>
            {
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationID = _orgId,
                    Reason = "Appendectomy",
                    Date = DateTime.Now.AddDays(-30),
                    IsActive = true,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-35),
                    UpdatedDate = DateTime.Now.AddDays(-30),
                    Subscription = _subscription
                },
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationID = _orgId,
                    Reason = "Pneumonia",
                    Date = DateTime.Now.AddDays(-60),
                    IsActive = false,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-65),
                    UpdatedDate = DateTime.Now.AddDays(-60),
                    Subscription = _subscription
                }
            };

            _hospitalizationRecordRepositoryMock.Setup(r => r.GetHospitalizationRecordById(_patientId, _orgId, _subscription))
                .ReturnsAsync(hospitalizationRecords);

            // Act
            var result = await _hospitalizationRecordQueryHandler.GetHospitalizationRecordById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(h => h.PatientId == _patientId).Should().BeTrue();
        }

        [Test]
        public async Task GetHospitalizationRecordById_WithNoRecords_ShouldReturnEmptyCollection()
        {
            // Arrange
            var hospitalizationRecords = new List<HospitalizationRecord>();

            _hospitalizationRecordRepositoryMock.Setup(r => r.GetHospitalizationRecordById(_patientId, _orgId, _subscription))
                .ReturnsAsync(hospitalizationRecords);

            // Act
            var result = await _hospitalizationRecordQueryHandler.GetHospitalizationRecordById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetHospitalizationRecordById_WhenRepositoryThrowsException_ShouldHandleExceptionAndReturnEmptyList()
        {
            // Arrange
            _hospitalizationRecordRepositoryMock.Setup(r => r.GetHospitalizationRecordById(_patientId, _orgId, _subscription))
                .ThrowsAsync(new Exception("Database connection error"));

            // Act
            var result = await _hospitalizationRecordQueryHandler.GetHospitalizationRecordById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetHospitalizationRecordByIdAndIsActive_ShouldReturnActiveHospitalizationRecordsForPatient()
        {
            // Arrange
            var hospitalizationRecords = new List<HospitalizationRecord>
            {
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationID = _orgId,
                    Reason = "Appendectomy",
                    Date = DateTime.Now.AddDays(-30),
                    IsActive = true,
                    Subscription = _subscription
                },
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationID = _orgId,
                    Reason = "Pneumonia",
                    Date = DateTime.Now.AddDays(-60),
                    IsActive = false, // Inactive
                    Subscription = _subscription
                },
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationID = _orgId,
                    Reason = "Fracture",
                    Date = DateTime.Now.AddDays(-45),
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _hospitalizationRecordRepositoryMock.Setup(r => r.GetHospitalizationRecordById(_patientId, _orgId, _subscription))
                .ReturnsAsync(hospitalizationRecords);

            // Act
            var result = await _hospitalizationRecordQueryHandler.GetHospitalizationRecordByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.All(h => h.PatientId == _patientId && h.IsActive).Should().BeTrue();
        }

        [Test]
        public async Task GetHospitalizationRecordByIdAndIsActive_WithNoActiveRecords_ShouldReturnEmptyCollection()
        {
            // Arrange
            var hospitalizationRecords = new List<HospitalizationRecord>
            {
                new HospitalizationRecord
                {
                    RecordID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationID = _orgId,
                    Reason = "Pneumonia",
                    Date = DateTime.Now.AddDays(-60),
                    IsActive = false, // Inactive
                    Subscription = _subscription
                }
            };

            _hospitalizationRecordRepositoryMock.Setup(r => r.GetHospitalizationRecordById(_patientId, _orgId, _subscription))
                .ReturnsAsync(hospitalizationRecords);

            // Act
            var result = await _hospitalizationRecordQueryHandler.GetHospitalizationRecordByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
