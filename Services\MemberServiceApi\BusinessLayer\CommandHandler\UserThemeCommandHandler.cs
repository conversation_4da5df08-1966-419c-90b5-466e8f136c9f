﻿using MemberServiceBusinessLayer;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class UserThemeCommandHandler : IUserThemeCommandHandler<UserTheme>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<UserThemeCommandHandler> _logger;

        public UserThemeCommandHandler(IUnitOfWork unitOfWork, ILogger<UserThemeCommandHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task AddUserTheme(UserTheme userTheme, Guid OrgID, bool Subscription)
        {
            await _unitOfWork.UserThemeRepository.AddAsync(new List<UserTheme> { userTheme}, OrgID, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateUserTheme(UserTheme userTheme, Guid OrgID, bool Subscription)
        {
            await _unitOfWork.UserThemeRepository.UpdateAsync(userTheme, OrgID, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteUserThemeById(Guid id, Guid OrgID, bool Subscription)
        {
            await _unitOfWork.UserThemeRepository.DeleteByIdAsync(id,OrgID, Subscription);
            await _unitOfWork.SaveAsync();
        }
    }
}
