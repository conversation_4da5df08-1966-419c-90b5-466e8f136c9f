﻿﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using NUnit.Framework;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using EncounterNotesService.EncounterNotesServiceResources;
using EncounterNotesDataAccessLayer.EncounterNotesContext;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class VaccinesControllerTests
    {
        private Mock<IVaccinesQueryHandler<Vaccines>> _vaccinesQueryHandlerMock;
        private Mock<ILogger<VaccinesController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private RecordDatabaseContext _contextMock;
        private VaccinesController _controller;
        private List<Vaccines> _testVaccines;

        [SetUp]
        public void Setup()
        {
            _vaccinesQueryHandlerMock = new Mock<IVaccinesQueryHandler<Vaccines>>();
            _loggerMock = new Mock<ILogger<VaccinesController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            // Since the controller doesn't actually use the context, we can pass null
            _contextMock = null;

            // Setup localizer mock
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving vaccines."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testVaccines = new List<Vaccines>
            {
                new Vaccines {
                    Id = Guid.NewGuid(),
                    VaccineName = "Influenza",
                    CVXCode = "141",
                    CPTCode = "90688",
                    CPTDescription = "Influenza virus vaccine, quadrivalent"
                },
                new Vaccines {
                    Id = Guid.NewGuid(),
                    VaccineName = "COVID-19",
                    CVXCode = "213",
                    CPTCode = "91303",
                    CPTDescription = "COVID-19 vaccine, mRNA"
                }
            };

            _controller = new VaccinesController(
                _vaccinesQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object,
                _contextMock
            );
        }

        [Test]
        public async Task Get_ReturnsVaccines_WhenVaccinesExist()
        {
            // Arrange
            _vaccinesQueryHandlerMock.Setup(x => x.GetData())
                .ReturnsAsync(_testVaccines);

            // Act
            var result = await _controller.Get();

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedVaccines = okResult.Value as IEnumerable<Vaccines>;
            returnedVaccines.Should().NotBeNull();
            returnedVaccines.Should().BeEquivalentTo(_testVaccines);
        }

        [Test]
        public async Task Get_ReturnsServerError_WhenExceptionOccurs()
        {
            // Arrange
            _vaccinesQueryHandlerMock.Setup(x => x.GetData())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Get();

            // Assert
            var statusCodeResult = result.Result as StatusCodeResult;
            statusCodeResult.Should().BeNull();

            var statusCodeObjectResult = result.Result as ObjectResult;
            statusCodeObjectResult.Should().NotBeNull();
            statusCodeObjectResult.StatusCode.Should().Be(500);
            statusCodeObjectResult.Value.Should().Be(_localizerMock.Object["GetLogError"]);
        }
    }
}
