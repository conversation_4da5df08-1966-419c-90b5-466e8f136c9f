﻿using EncounterNotesContracts;
using ShardModels;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface ISurgicalHistoryCommandHandler<TText>
    {
        Task DeleteSurgeryByEntity(SurgicalHistory surgicalhistory, Guid OrgId, bool Subscription);
        Task DeleteSurgeryById(Guid id, Guid OrgId, bool Subscription);
        Task AddSurgery(List<TText> texts, Guid OrgId, bool Subscription);
        Task UpdateSurgery(SurgicalHistory surgicalhistory, Guid OrgId, bool Subscription);
        Task UpdateSurgeryList(List<SurgicalHistory> Surgeries, Guid OrgId, bool Subscription);
    }
}
