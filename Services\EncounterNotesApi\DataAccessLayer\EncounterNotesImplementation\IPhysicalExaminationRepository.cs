﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IPhysicalExaminationRepository : IShardGenericRepository<PhysicalExamination>
    {
        Task<IEnumerable<PhysicalExamination>> GetAllExaminationsAsync( Guid OrgId, bool Subscription);
    }
}