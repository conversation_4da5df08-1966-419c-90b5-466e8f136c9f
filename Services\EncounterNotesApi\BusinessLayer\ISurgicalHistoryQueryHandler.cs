﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface ISurgicalHistoryQueryHandler<TText>
    {
        Task<IEnumerable<SurgicalHistory>> GetSurgeryByIdAndIsActive(Guid id, Guid OrgId, bool Subscription);
        Task<IEnumerable<SurgicalHistory>> GetAllSurgeriesById(Guid id, Guid OrgId, bool Subscription);
    }
}