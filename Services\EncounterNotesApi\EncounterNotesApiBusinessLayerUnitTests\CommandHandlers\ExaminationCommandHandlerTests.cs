using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class ExaminationCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<ExaminationCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, Examination, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IExaminationRepository> _examinationRepositoryMock;
        private ExaminationCommandHandler _examinationCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<ExaminationCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, Examination, EncounterDataAccessLayer>>();
            _examinationRepositoryMock = new Mock<IExaminationRepository>();

            _unitOfWorkMock.Setup(u => u.ExaminationRepository).Returns(_examinationRepositoryMock.Object);

            _examinationCommandHandler = new ExaminationCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task AddExaminationList_ValidExaminations_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var examinations = new List<Examination>
            {
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    UpdateDate = DateTime.Now,
                    GeneralDescription = "Normal appearance",
                    HEENT = "Normal",
                    Lungs = "Clear",
                    Abdomen = "Soft, non-tender",
                    PeripheralPulses = "Normal",
                    Skin = "Normal",
                    Others = "None",
                    Subscription = _subscription
                },
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    UpdateDate = DateTime.Now,
                    GeneralDescription = "Appears well",
                    HEENT = "Normal",
                    Lungs = "Wheezing noted",
                    Abdomen = "Soft, non-tender",
                    PeripheralPulses = "Normal",
                    Skin = "Dry",
                    Others = "None",
                    Subscription = _subscription
                }
            };

            // Act
            await _examinationCommandHandler.AddExaminationList(examinations, _orgId, _subscription);

            // Assert
            _examinationRepositoryMock.Verify(r => r.AddAsync(examinations, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateExaminationList_ValidExaminations_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var examinations = new List<Examination>
            {
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdateDate = DateTime.Now,
                    GeneralDescription = "Updated description 1",
                    HEENT = "Updated HEENT 1",
                    Lungs = "Updated lungs 1",
                    Abdomen = "Updated abdomen 1",
                    PeripheralPulses = "Updated pulses 1",
                    Skin = "Updated skin 1",
                    Others = "Updated others 1",
                    Subscription = _subscription
                },
                new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdateDate = DateTime.Now,
                    GeneralDescription = "Updated description 2",
                    HEENT = "Updated HEENT 2",
                    Lungs = "Updated lungs 2",
                    Abdomen = "Updated abdomen 2",
                    PeripheralPulses = "Updated pulses 2",
                    Skin = "Updated skin 2",
                    Others = "Updated others 2",
                    Subscription = _subscription
                }
            };

            // Act
            await _examinationCommandHandler.UpdateExaminationList(examinations, _orgId, _subscription);

            // Assert
            _examinationRepositoryMock.Verify(r => r.UpdateRangeAsync(examinations, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}
