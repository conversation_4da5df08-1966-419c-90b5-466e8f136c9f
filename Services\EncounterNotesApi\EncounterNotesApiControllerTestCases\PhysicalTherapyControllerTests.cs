using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class PhysicalTherapyControllerTests
    {
        private Mock<IPhysicalTherapyCommandHandler<PhysicalTherapyData>> _physicalTherapyCommandHandlerMock;
        private Mock<IPhysicalTherapyQueryHandler<PhysicalTherapyData>> _physicalTherapyQueryHandlerMock;
        private Mock<ILogger<PhysicalTherapyController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private PhysicalTherapyController _controller;
        private List<PhysicalTherapyData> _testPhysicalTherapies;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _physicalTherapyCommandHandlerMock = new Mock<IPhysicalTherapyCommandHandler<PhysicalTherapyData>>();
            _physicalTherapyQueryHandlerMock = new Mock<IPhysicalTherapyQueryHandler<PhysicalTherapyData>>();
            _loggerMock = new Mock<ILogger<PhysicalTherapyController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No license available."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error occurred."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error posting data."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting data."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Delete successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testPhysicalTherapies = new List<PhysicalTherapyData>
            {
                new PhysicalTherapyData {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    TherapyAssessment = "Lower back pain assessment",
                    ShortTermGoals = "Reduce pain level to 3/10 within 2 weeks",
                    LongTermGoals = "Return to normal activities within 2 months",
                    PhysicalTherapyDiagnosis = "Lumbar strain",
                    PhysicalTherapyProgram = "Core strengthening and flexibility",
                    Disabilities = "Limited mobility",
                    Functionals = "Difficulty with prolonged sitting",
                    Limitations = "Cannot lift more than 10 pounds",
                    IsActive = true,
                    Subscription = _isSubscription
                },
                new PhysicalTherapyData {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-15),
                    TherapyAssessment = "Shoulder mobility assessment",
                    ShortTermGoals = "Increase range of motion by 20% within 3 weeks",
                    LongTermGoals = "Full range of motion within 3 months",
                    PhysicalTherapyDiagnosis = "Rotator cuff tendinitis",
                    PhysicalTherapyProgram = "Shoulder strengthening and mobility exercises",
                    Disabilities = "Limited overhead reaching",
                    Functionals = "Difficulty with dressing",
                    Limitations = "Cannot lift arm above shoulder height",
                    IsActive = true,
                    Subscription = _isSubscription
                }
            };

            _controller = new PhysicalTherapyController(
                _physicalTherapyCommandHandlerMock.Object,
                _physicalTherapyQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetAllById_ReturnsPhysicalTherapies_WhenPhysicalTherapiesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientPhysicalTherapies = _testPhysicalTherapies.Select(pt => { pt.PatientId = patientId; return pt; }).ToList();
            _physicalTherapyQueryHandlerMock.Setup(x => x.GetAllPhysicalTherapyById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientPhysicalTherapies);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedPhysicalTherapies = okResult.Value as IEnumerable<PhysicalTherapyData>;
            returnedPhysicalTherapies.Should().NotBeNull();
            returnedPhysicalTherapies.Should().BeEquivalentTo(patientPhysicalTherapies);
        }

        [Test]
        public async Task GetAllById_ReturnsNotFound_WhenNoPhysicalTherapiesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _physicalTherapyQueryHandlerMock.Setup(x => x.GetAllPhysicalTherapyById(patientId, _orgId, _isSubscription))
                .ReturnsAsync((IEnumerable<PhysicalTherapyData>)null);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsPhysicalTherapies_WhenActivePhysicalTherapiesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activePhysicalTherapies = _testPhysicalTherapies.Select(pt => { pt.PatientId = patientId; pt.IsActive = true; return pt; }).ToList();
            _physicalTherapyQueryHandlerMock.Setup(x => x.GetPhysicalTherapyByIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activePhysicalTherapies);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedPhysicalTherapies = okResult.Value as IEnumerable<PhysicalTherapyData>;
            returnedPhysicalTherapies.Should().NotBeNull();
            returnedPhysicalTherapies.Should().BeEquivalentTo(activePhysicalTherapies);
        }

        [Test]
        public async Task AddPhysicalTherapy_ValidPhysicalTherapies_ReturnsOk()
        {
            // Arrange
            var physicalTherapies = _testPhysicalTherapies;

            // Act
            var result = await _controller.AddPhysicalTherapy(physicalTherapies, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _physicalTherapyCommandHandlerMock.Verify(x => x.AddPhysicalTherapy(physicalTherapies, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task AddPhysicalTherapy_EmptyList_ReturnsBadRequest()
        {
            // Arrange
            var emptyPhysicalTherapies = new List<PhysicalTherapyData>();

            // Act
            var result = await _controller.AddPhysicalTherapy(emptyPhysicalTherapies, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);
        }

        [Test]
        public async Task DeleteByEntity_ValidPhysicalTherapy_ReturnsOk()
        {
            // Arrange
            var physicalTherapy = _testPhysicalTherapies[0];

            // Act
            var result = await _controller.DeleteByEntity(physicalTherapy, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _physicalTherapyCommandHandlerMock.Verify(x => x.DeletePhysicalTherapyByEntity(physicalTherapy, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdatePhysicalTherapyById_ValidPhysicalTherapy_ReturnsOk()
        {
            // Arrange
            var physicalTherapy = _testPhysicalTherapies[0];

            // Act
            var result = await _controller.UpdatePhysicalTherapyById(physicalTherapy.PatientId, physicalTherapy, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _physicalTherapyCommandHandlerMock.Verify(x => x.UpdatePhysicalTherapy(physicalTherapy, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdatePhysicalTherapyList_ValidPhysicalTherapies_ReturnsOk()
        {
            // Arrange
            var physicalTherapies = _testPhysicalTherapies;

            // Act
            var result = await _controller.UpdatePhysicalTherapyList(physicalTherapies, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _physicalTherapyCommandHandlerMock.Verify(x => x.UpdatePhysicalTherapyList(physicalTherapies, _orgId, _isSubscription), Times.Once);
        }


    }
}
