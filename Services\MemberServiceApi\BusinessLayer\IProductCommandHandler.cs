﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IProductCommandHandler<TText>
    {
        Task DeleteProductByEntity(ProductDTO product, Guid orgId, bool subscription);
        Task DeleteProductById(Guid id, Guid orgId, bool subscription);
        Task AddProduct(List<TText> texts, Guid orgId, bool subscription);
        Task UpdateProduct(ProductDTO product, Guid orgId, bool subscription);
    }
}