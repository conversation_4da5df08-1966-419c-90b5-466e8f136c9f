﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class ObHistoryQueryHandler : IObHistoryQueryHandler<ObHistory>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public ObHistoryQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<ObHistory>> GetAllAsync(Guid OrgId, bool Subscription)
        {
            var obHistories = await _unitOfWork.ObHistoryRepository.GetAllAsync(OrgId, Subscription);
            return obHistories;
        }

        public async Task<ObHistory> GetByIdAsync(Guid id, Guid OrgId, bool Subscription)
        {
            var obHistory = await _unitOfWork.ObHistoryRepository.GetByIdAsync(id, OrgId, Subscription);
            return obHistory;
        }

        public async Task<IEnumerable<ObHistory>> GetByPatientIdAsync(Guid patientId, Guid OrgId, bool Subscription)
        {
            var obHistories = await _unitOfWork.ObHistoryRepository.GetByPatientIdAsync(patientId, OrgId, Subscription);
            return obHistories;
        }
    }
}
