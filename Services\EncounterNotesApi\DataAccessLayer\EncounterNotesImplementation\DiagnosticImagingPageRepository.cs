﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Interfaces.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class DiagnosticImagingPageRepository : ShardGenericRepository<DiagnosticImagingDTO>, IDiagnosticImagingPageRepository
    {
        private readonly RecordDatabaseContext _context;
        private readonly ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> _shardMapManagerService;
        private readonly IStringLocalizer<EncounterDataAccessLayer> _localizer;
        private readonly ILogger<RecordDatabaseContext> _logger;
        private readonly string _shardMapName;

        public DiagnosticImagingPageRepository(
            RecordDatabaseContext context,
            ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> shardMapManagerService,
            IStringLocalizer<EncounterDataAccessLayer> localizer,
            ILogger<RecordDatabaseContext> logger)
            : base(context, shardMapManagerService, localizer, logger)
        {
            _context = context;
            _shardMapManagerService = shardMapManagerService;
            _localizer = localizer;
            _logger = logger;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }

        public async Task<List<DiagnosticImagingDTO>> GetDiagnosticImagingById(Guid patientId, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return new List<DiagnosticImagingDTO>();

            return await context.DiagnosticImagingDTO
                .Where(DiagnosticImagingRecord => DiagnosticImagingRecord.PatientId == patientId && DiagnosticImagingRecord.IsActive == true)
                .Select(DiagnosticImagingRecord => new DiagnosticImagingDTO
                {
                    RecordID = DiagnosticImagingRecord.RecordID,
                    OrganizationID = DiagnosticImagingRecord.OrganizationID,
                    PatientId = DiagnosticImagingRecord.PatientId,
                    Status = DiagnosticImagingRecord.Status,
                    Provider = DiagnosticImagingRecord.Provider,
                    Facility = DiagnosticImagingRecord.Facility,
                    AssignedTo = DiagnosticImagingRecord.AssignedTo,
                    FutureOrder = DiagnosticImagingRecord.FutureOrder,
                    HigherPriority = DiagnosticImagingRecord.HigherPriority,
                    InHouse = DiagnosticImagingRecord.InHouse,
                    Procedgure = DiagnosticImagingRecord.Procedgure,
                    OrderDate = DiagnosticImagingRecord.OrderDate,
                    Reason = DiagnosticImagingRecord.Reason,
                    Recieved = DiagnosticImagingRecord.Recieved,
                    Date = DiagnosticImagingRecord.Date,
                    Result = DiagnosticImagingRecord.Result,
                    Notes = DiagnosticImagingRecord.Notes,
                    ClassicalInfo = DiagnosticImagingRecord.ClassicalInfo,
                    InternalNotes = DiagnosticImagingRecord.InternalNotes,
                    IsActive = DiagnosticImagingRecord.IsActive,
                    CreatedBy = DiagnosticImagingRecord.CreatedBy,
                    UpdatedBy = DiagnosticImagingRecord.UpdatedBy,
                    CreatedDate = DiagnosticImagingRecord.CreatedDate,
                    UpdatedDate = DiagnosticImagingRecord.UpdatedDate,

                    Assessments = context.DiagnosticImagingAssessment
                        .Where(DiagnosticAssessment => DiagnosticAssessment.DiagnosticImagingID == DiagnosticImagingRecord.RecordID)
                        .Select(DiagnosticAssessment => new DiagnosticImagingAssessment
                        {
                            ID = DiagnosticAssessment.ID,
                            DiagnosticImagingID = DiagnosticAssessment.DiagnosticImagingID,
                            AssessmentID = DiagnosticAssessment.AssessmentID
                        })
                        .ToList()
                })
                .ToListAsync();
        }

        public async Task<List<DiagnosticImagingDTO>> GetDiagnosticImagingByIdAndIsActive(Guid patientId, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return new List<DiagnosticImagingDTO>();

            return await context.DiagnosticImagingDTO
                .Where(DiagnosticImagingRecord => DiagnosticImagingRecord.PatientId == patientId)
                .Select(DiagnosticImagingRecord => new DiagnosticImagingDTO
                {
                    RecordID = DiagnosticImagingRecord.RecordID,
                    OrganizationID = DiagnosticImagingRecord.OrganizationID,
                    PatientId = DiagnosticImagingRecord.PatientId,
                    Status = DiagnosticImagingRecord.Status,
                    Provider = DiagnosticImagingRecord.Provider,
                    Facility = DiagnosticImagingRecord.Facility,
                    AssignedTo = DiagnosticImagingRecord.AssignedTo,
                    FutureOrder = DiagnosticImagingRecord.FutureOrder,
                    HigherPriority = DiagnosticImagingRecord.HigherPriority,
                    InHouse = DiagnosticImagingRecord.InHouse,
                    Procedgure = DiagnosticImagingRecord.Procedgure,
                    OrderDate = DiagnosticImagingRecord.OrderDate,
                    Reason = DiagnosticImagingRecord.Reason,
                    Recieved = DiagnosticImagingRecord.Recieved,
                    Date = DiagnosticImagingRecord.Date,
                    Result = DiagnosticImagingRecord.Result,
                    Notes = DiagnosticImagingRecord.Notes,
                    ClassicalInfo = DiagnosticImagingRecord.ClassicalInfo,
                    InternalNotes = DiagnosticImagingRecord.InternalNotes,
                    IsActive = DiagnosticImagingRecord.IsActive,
                    CreatedBy = DiagnosticImagingRecord.CreatedBy,
                    UpdatedBy = DiagnosticImagingRecord.UpdatedBy,
                    CreatedDate = DiagnosticImagingRecord.CreatedDate,
                    UpdatedDate = DiagnosticImagingRecord.UpdatedDate,

                    Assessments = context.DiagnosticImagingAssessment
                        .Where(DiagnosticAssessment => DiagnosticAssessment.DiagnosticImagingID == DiagnosticImagingRecord.RecordID)
                        .Select(DiagnosticAssessment => new DiagnosticImagingAssessment
                        {
                            ID = DiagnosticAssessment.ID,
                            DiagnosticImagingID = DiagnosticAssessment.DiagnosticImagingID,
                            AssessmentID = DiagnosticAssessment.AssessmentID
                        })
                        .ToList()
                })
                .ToListAsync();
        }
    }
}
