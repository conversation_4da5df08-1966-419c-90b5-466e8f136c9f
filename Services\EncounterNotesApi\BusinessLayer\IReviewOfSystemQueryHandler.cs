﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IReviewOfSystemQueryHandler<TText>
    {
        Task<IEnumerable<ReviewOfSystem>> GetReviewOfSystemByIdAndIsActive(Guid id, Guid OrgId, bool Subscription);
        Task<IEnumerable<ReviewOfSystem>> GetAllReviewOfSystemsById(Guid id, Guid OrgId, bool Subscription);
    }
}
