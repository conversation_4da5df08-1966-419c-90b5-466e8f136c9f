﻿using System;
using System.Text.Json.Serialization;

namespace EncounterNotesContracts
{
    public class Record : IContract
    {
        public Guid Id { get; set; }

        public Guid OrganizationId { get; set; }

        public Guid PCPId { get; set; }

        public Guid PatientId { get; set; }
        public string? PatientName { get; set; }
        public DateTime DateTime { get; set; }
        public string? Notes { get; set; }

        public string? Transcription { get; set; }
        public bool? isEditable { get; set; }
        public bool Subscription { get; set; }
        public virtual List<WordTiming> WordTimings { get; set; } = new List<WordTiming>();
    }
}