﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IGynHistoryRepository<T> where T : class
    {
        Task<IEnumerable<T>> GetAllAsync(Guid OrgId, bool Subscription);
        Task<IEnumerable<T>> GetByPatientIdAsync(Guid patientId, Guid OrgId, bool Subscription);
        Task<T> GetByIdAsync(Guid id, Guid OrgId, bool Subscription);
        Task AddAsync(IEnumerable<T> entities, Guid OrgId, bool Subscription);
        Task UpdateAsync(T entity, Guid OrgId, bool Subscription);
        Task DeleteByEntityAsync(T entity, Guid OrgId, bool Subscription);
        Task DeleteByIdAsync(Guid id, Guid OrgId, bool Subscription);
        Task UpdateGynHistoryListAsync(List<GynHistory> gynhistory, Guid OrgId, bool Subscription);
    }
}
