﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface ILicenseCommandHandler<TText>
    {
        Task DeleteLicenseByEntity(ProductLicense license, Guid OrgID, bool Subscription);
        Task DeleteLicenseById(Guid id, Guid OrgID, bool Subscription);
        Task AddLicense(List<TText> texts);
        Task UpdateLicense(ProductLicense license, Guid OrgID, bool Subscription);
        Task<bool> UpdateLicenseAccessAsync(List<ProductLicense> licenseAccessUpdates);
    }
}


