using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class AllergyCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<AllergyCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, Allergy, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IAllergyRepository> _allergyRepositoryMock;
        private AllergyCommandHandler _allergyCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<AllergyCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, Allergy, EncounterDataAccessLayer>>();
            _allergyRepositoryMock = new Mock<IAllergyRepository>();

            _unitOfWorkMock.Setup(u => u.AllergyRepository).Returns(_allergyRepositoryMock.Object);

            _allergyCommandHandler = new AllergyCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task AddAllergy_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var allergies = new List<Allergy>
            {
                new Allergy
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    AllergyInfo = "Peanut Allergy",
                    Classification = "Food",
                    Agent = "Peanuts",
                    Reaction = "Anaphylaxis",
                    isActive = true
                }
            };

            _allergyRepositoryMock.Setup(r => r.AddAsync(allergies, _orgId, _subscription))
                .Returns(Task.CompletedTask);
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _allergyCommandHandler.AddAllergy(allergies, _orgId, _subscription);

            // Assert
            _allergyRepositoryMock.Verify(r => r.AddAsync(allergies, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateAllergy_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var allergy = new Allergy
            {
                MedicineId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                AllergyInfo = "Peanut Allergy",
                Classification = "Food",
                Agent = "Peanuts",
                Reaction = "Anaphylaxis",
                isActive = true
            };

            _allergyRepositoryMock.Setup(r => r.UpdateAsync(allergy, _orgId, _subscription))
                .Returns(Task.CompletedTask);
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _allergyCommandHandler.UpdateAllergy(allergy, _orgId, _subscription);

            // Assert
            _allergyRepositoryMock.Verify(r => r.UpdateAsync(allergy, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateAllergyList_ShouldProcessAllListsAndSave()
        {
            // Arrange
            var newAllergies = new List<Allergy>
            {
                new Allergy
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    AllergyInfo = "New Allergy",
                    isActive = true
                }
            };

            var updatedAllergies = new List<Allergy>
            {
                new Allergy
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    AllergyInfo = "Updated Allergy",
                    isActive = true
                }
            };

            var deletedAllergies = new List<Allergy>
            {
                new Allergy
                {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    AllergyInfo = "Deleted Allergy",
                    isActive = true
                }
            };

            _allergyRepositoryMock.Setup(r => r.AddAsync(It.IsAny<Allergy>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);
            _allergyRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Allergy>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _allergyCommandHandler.UpdateAllergyList(newAllergies, updatedAllergies, deletedAllergies, _orgId, _subscription);

            // Assert
            _allergyRepositoryMock.Verify(r => r.AddAsync(It.IsAny<Allergy>(), _orgId, _subscription), Times.Once);
            _allergyRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<Allergy>(), _orgId, _subscription), Times.Exactly(2));
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);

            // Verify that deleted allergies have isActive set to false
            deletedAllergies[0].isActive.Should().BeFalse();
        }

        [Test]
        public async Task DeleteAllergyById_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var allergyId = Guid.NewGuid();

            _allergyRepositoryMock.Setup(r => r.DeleteByIdAsync(allergyId, _orgId, _subscription))
                .Returns(Task.CompletedTask);
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _allergyCommandHandler.DeleteAllergyById(allergyId, _orgId, _subscription);

            // Assert
            _allergyRepositoryMock.Verify(r => r.DeleteByIdAsync(allergyId, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteAllergyByEntity_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var allergy = new Allergy
            {
                MedicineId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                AllergyInfo = "Allergy to delete",
                isActive = true
            };

            _allergyRepositoryMock.Setup(r => r.DeleteByEntityAsync(allergy, _orgId, _subscription))
                .Returns(Task.CompletedTask);
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _allergyCommandHandler.DeleteAllergyByEntity(allergy, _orgId, _subscription);

            // Assert
            _allergyRepositoryMock.Verify(r => r.DeleteByEntityAsync(allergy, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }




    }
}
