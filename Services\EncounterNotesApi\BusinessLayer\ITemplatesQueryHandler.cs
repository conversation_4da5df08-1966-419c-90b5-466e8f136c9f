﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface ITemplatesQueryHandler<TText>
    {
        Task<IEnumerable<TText>> GetTemplates(Guid OrgId, bool Subscription);
        Task<TText> GetTemplatesById(Guid id, Guid OrgId, bool Subscription);

        Task<List<TText>> GetTemplatesByPCPId(Guid id, Guid OrgId, bool Subscription);

        Task<List<Templates>> GetTemplatesByPCPIdAndVisitType(Guid id, String VisitType,Guid OrgId, bool Subscription);
    }
}