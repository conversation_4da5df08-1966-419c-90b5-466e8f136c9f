﻿using Contracts;
using Microsoft.Azure.Amqp.Framing;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IAddressesCommandHandler<T>
    {

        Task AddAddressAsync(List<T> addresses, bool Subscription, Guid orgId);
        Task UpdateAddressAsync(T address, bool Subscription, Guid orgId);
        Task DeleteAddressAsync(Guid id, bool Subscription, Guid orgId);
    }
}
