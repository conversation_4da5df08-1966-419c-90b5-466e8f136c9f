﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IImmunizationRepository : IShardGenericRepository<Immunization>
    {
        Task<IEnumerable<Immunization>> GetAllImmunizationsAsync(Guid OrgId, bool Subscription);
    }
}