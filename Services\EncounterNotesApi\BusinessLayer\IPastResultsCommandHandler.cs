﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface IPastResultsCommandHandler<TText>
    {
        Task DeleteResultsByEntity(PastResults pastresults, Guid OrgId, bool Subscription);
        Task DeleteResultById(Guid id, Guid OrgId, bool Subscription);
        Task AddResult(List<TText> texts, Guid OrgId, bool Subscription);
        Task UpdateResult(PastResults pastResult, Guid OrgId, bool Subscription);
        Task UpdatePastList(List<PastResults> past, Guid OrgId, bool Subscription);
    }
}
