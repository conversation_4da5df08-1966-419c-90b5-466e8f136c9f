﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using Microsoft.Azure.Amqp.Framing;
using ShardModels;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class FamilyMemberController : ControllerBase
    {
        private readonly IFamilyMemberCommandHandler<FamilyMember> _familyMemberCommandHandler;
        private readonly IFamilyMemberQueryHandler<FamilyMember> _familyMemberQueryHandler;
        private readonly ILogger<FamilyMemberController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;
        public FamilyMemberController(
            IFamilyMemberQueryHandler<FamilyMember> familyMemberQueryHandler,
            IFamilyMemberCommandHandler<FamilyMember> familyMemberCommandHandler,
            ILogger<FamilyMemberController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer,
            RecordDatabaseContext context
            )
        {
            _familyMemberQueryHandler = familyMemberQueryHandler;
            _familyMemberCommandHandler = familyMemberCommandHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        ///  Get All Family Member by Id 
        /// </summary>
        [HttpGet("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<List<FamilyMember>>> GetById(Guid id, Guid orgId, bool isSubscription)
        {
            try
            {
                var familyMembers = await _familyMemberQueryHandler.GetFamilyMembersById(id, orgId, isSubscription);

                if (familyMembers == null || !familyMembers.Any())
                {
                    return NotFound(_localizer["RecordNotFound"]);
                }

                return Ok(familyMembers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500, _localizer["GetLogError"]);
            }
        }

        /// <summary>
        /// Update an existing member
        /// </summary>
        [HttpPut("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateById(Guid id, [FromBody] FamilyMember familyMember, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            if (familyMember == null || familyMember.RecordID != id)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _familyMemberCommandHandler.UpdateFamilyMember(familyMember, orgId, isSubscription);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                }
            }
            return result;
        }

        /// <summary>
        ///  Delete an existing Member By Id
        /// </summary>
        [HttpDelete("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> DeleteById(Guid id, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            try
            {
                await _familyMemberCommandHandler.DeleteFamilyMemberById(id, orgId, isSubscription);
                result = Ok(_localizer["SuccessfulDeletion"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Add new Member
        /// </summary>
        [HttpPost]
        [Route("{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> AddFamilyMember([FromBody] List<FamilyMember> familyMember, Guid orgId, bool isSubscription)
        {
            IActionResult response;
            if (familyMember == null || familyMember.Count == 0)
            {
                _logger.LogWarning(_localizer["InvalidAddressData"]);
                response = BadRequest(_localizer["AddressInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["AddingNewAddress"]);
                    await _familyMemberCommandHandler.AddFamilyMember(familyMember, orgId, isSubscription);
                    _logger.LogInformation(_localizer["AddressAddedSuccessfully"]);
                    response = Ok(_localizer["AddSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorAddingAddress"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        /// <summary>
        ///  Get Active Family Member by Id 
        /// </summary>
        [HttpGet("{id:guid}/isActive/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<FamilyMember>>> GetAllByIdAndIsActive(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult result;
            try
            {
                var familyMember = await _familyMemberQueryHandler.GetFamilyMemberByIdAndIsActive(id, orgId, isSubscription);
                result = familyMember != null ? Ok(familyMember) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        ///  Update an existing List of Family Members
        /// </summary>
        [HttpPut("updateMember/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateAccess(List<FamilyMember> familyMembers, Guid orgId, bool isSubscription)
        {
            if (familyMembers == null)
            {
                return BadRequest(_localizer["AccessError"]);
            }
            var success = await _familyMemberCommandHandler.UpdateFamilyMemberList(familyMembers, orgId, isSubscription);

            if (success)
            {
                return Ok(new { Message = _localizer["UpdateSuccessful"] });
            }
            else
            {
                return NotFound(_localizer["Record not found"]);
            }
        }

    }
}
