﻿﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using NUnit.Framework;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class CurrentMedicationControllerTests
    {
        private CurrentMedicationController _controller;
        private Mock<ICurrentMedicationCommandHandler<CurrentMedication>> _medicationCommandHandlerMock;
        private Mock<ICurrentMedicationQueryHandler<CurrentMedication>> _medicationQueryHandlerMock;
        private Mock<ILogger<CurrentMedicationController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private List<CurrentMedication> _testMedications;
        private readonly Guid _orgId = Guid.NewGuid();
        private readonly bool _isSubscription = false;

        [SetUp]
        public void Setup()
        {
            _medicationCommandHandlerMock = new Mock<ICurrentMedicationCommandHandler<CurrentMedication>>();
            _medicationQueryHandlerMock = new Mock<ICurrentMedicationQueryHandler<CurrentMedication>>();
            _loggerMock = new Mock<ILogger<CurrentMedicationController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();

            // Setup localizer mock
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving medication."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Medication registered successfully."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No license provided."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error adding medication."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Medication deleted successfully."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting medication."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Medication updated successfully."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating medication."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));

            _testMedications = new List<CurrentMedication>
            {
                new CurrentMedication {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    BrandName = "Aspirin",
                    DrugDetails = "Pain reliever",
                    Quantity = "100mg",
                    Frequency = "Once daily",
                    isActive = true,
                    StartDate = DateTime.Now.AddDays(-30),
                    EndDate = DateTime.Now.AddDays(30),
                    CheifComplaint = "Headache",
                    CheifComplaintId = Guid.NewGuid()
                },
                new CurrentMedication {
                    MedicineId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-15),
                    BrandName = "Lisinopril",
                    DrugDetails = "Blood pressure medication",
                    Quantity = "10mg",
                    Frequency = "Once daily",
                    isActive = true,
                    StartDate = DateTime.Now.AddDays(-20),
                    EndDate = null,
                    CheifComplaint = "Hypertension",
                    CheifComplaintId = Guid.NewGuid()
                }
            };

            _controller = new CurrentMedicationController(
                _medicationCommandHandlerMock.Object,
                _medicationQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsMedications_WhenMedicationsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activeMedications = _testMedications.Select(m => { m.PatientId = patientId; m.isActive = true; return m; }).ToList();
            _medicationQueryHandlerMock.Setup(x => x.GetMedicationByIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activeMedications);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedMedications = okResult.Value as IEnumerable<CurrentMedication>;
            returnedMedications.Should().NotBeNull();
            returnedMedications.Should().BeEquivalentTo(activeMedications);
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsNotFound_WhenNoMedicationsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _medicationQueryHandlerMock.Setup(x => x.GetMedicationByIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync((IEnumerable<CurrentMedication>)null);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task Registration_ValidMedications_ReturnsOk()
        {
            // Arrange
            var medicationsToAdd = _testMedications;

            // Act
            var result = await _controller.Registration(medicationsToAdd, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _medicationCommandHandlerMock.Verify(x => x.AddMedication(medicationsToAdd, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task Registration_EmptyMedications_ReturnsBadRequest()
        {
            // Arrange
            var emptyMedications = new List<CurrentMedication>();

            // Act
            var result = await _controller.Registration(emptyMedications, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);

            // Verify the command handler was not called
            _medicationCommandHandlerMock.Verify(x => x.AddMedication(It.IsAny<List<CurrentMedication>>(), _orgId, _isSubscription), Times.Never);
        }

        [Test]
        public async Task DeleteByEntity_ValidMedication_ReturnsOk()
        {
            // Arrange
            var medicationToDelete = _testMedications[0];

            // Act
            var result = await _controller.DeleteByEntity(medicationToDelete, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _medicationCommandHandlerMock.Verify(x => x.DeleteMedicationByEntity(medicationToDelete, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateMedicationById_ValidMedication_ReturnsOk()
        {
            // Arrange
            var medicationToUpdate = _testMedications[0];
            var patientId = medicationToUpdate.PatientId;

            // Act
            var result = await _controller.UpdateMedicationById(patientId, medicationToUpdate, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _medicationCommandHandlerMock.Verify(x => x.UpdateMedication(medicationToUpdate, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateMedicationList_ValidMedications_ReturnsOk()
        {
            // Arrange
            var medicationsToUpdate = _testMedications;

            // Act
            var result = await _controller.UpdateMedicationList(medicationsToUpdate, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _medicationCommandHandlerMock.Verify(x => x.UpdateMedicationList(medicationsToUpdate, _orgId, _isSubscription), Times.Once);
        }


    }
}
