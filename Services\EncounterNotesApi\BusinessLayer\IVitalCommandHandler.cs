﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface IVitalCommandHandler<TText>
    {
        Task DeleteVitalByEntity(Vitals vital, Guid OrgId, bool Subscription);
        Task DeleteVitalById(Guid id, Guid OrgId, bool Subscription);
        Task AddVital(List<TText> texts, Guid OrgId, bool Subscription);
        Task UpdateVital(Vitals vital, Guid OrgId, bool Subscription);
        Task UpdateVitalList(List<Vitals> vitals, Guid OrgId, bool Subscription);
    }
}
