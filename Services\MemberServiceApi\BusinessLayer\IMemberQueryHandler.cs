﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IMemberQueryHandler<TText>
    {
        Task<Patient> GetPatientdatabyid(Guid id, Guid OrgID, bool Subscription);
        Task<IEnumerable<TText>> GetMember(Guid OrgID, bool Subscription);
        Task<TText> GetMemberById(Guid id, Guid OrgID, bool Subscription);
        Task<IEnumerable<TText>> SearchMembersAsync(string searchTerm, Guid OrgID, bool Subscription);
        Task<IEnumerable<TText>> SearchMembersByEmailAsync(string searchTerm, Guid OrgID, bool Subscription);
        Task<IEnumerable<Office_visit_members>> GetPatientsByIdsAsync(List<Guid> patientIds, Guid OrgID, bool Subscription);
        Task<IEnumerable<ProviderPatient>> GetProviderPatientByOrganizationId(Guid id, bool Subscription);
        Task<IEnumerable<ProviderPatient>> GetPatientsByOrganizationId(Guid id, bool Subscription);
    }
}
