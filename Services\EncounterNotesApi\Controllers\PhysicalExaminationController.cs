﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using ShardModels;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]

    public class PhysicalExaminationController : ControllerBase
    {
        private readonly IPhysicalExaminationCommandHandler<PhysicalExamination> _physicalDataHandler;
        private readonly IPhysicalExaminationQueryHandler<PhysicalExamination> _physicalQueryHandler;
        private readonly ILogger<PhysicalExaminationController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;


        public PhysicalExaminationController(
            IPhysicalExaminationCommandHandler<PhysicalExamination> physicalDataHandler,
            IPhysicalExaminationQueryHandler<PhysicalExamination> physicalQueryHandler,
            ILogger<PhysicalExaminationController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
            )
        {
            _physicalDataHandler = physicalDataHandler;
            _physicalQueryHandler = physicalQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        /// Get All Details By Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<PhysicalExamination>>> GetAllById(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult result;
            try
            {
                var data = await _physicalQueryHandler.GetAllExaminationsById(id, orgId, isSubscription);
                result = data != null ? Ok(data) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id:guid}/IsActive/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<PhysicalExamination>>> GetAllByIdAndIsActive(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult result;
            try
            {
                var surgery = await _physicalQueryHandler.GetExaminationByIdAndIsActive(id, orgId, isSubscription);
                result = surgery != null ? Ok(surgery) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="physicals"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddExamination/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> Registration([FromBody] List<PhysicalExamination> physicals, Guid orgId, bool isSubscription)
        {
            if (physicals == null || physicals.Count == 0)
            {
                return Ok(_localizer["NoLicense"]);
            }

            try
            {
                await _physicalDataHandler.AddExamination(physicals, orgId, isSubscription);
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException ex)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="physical"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> DeleteByEntity([FromBody] PhysicalExamination physical, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            {
                if (physical == null)
                {
                    result = BadRequest(_localizer["InvalidRecord"]);
                }
                else
                {
                    try
                    {
                        await _physicalDataHandler.DeleteExaminationByEntity(physical, orgId, isSubscription);
                        result = Ok(_localizer["DeleteSuccessful"]);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, _localizer["DeleteLogError"]);
                        result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
                    }
                }
                return result;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="Phyexamination"></param>
        /// <returns></returns>
        [HttpPut("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateExaminationById(Guid id, [FromBody] PhysicalExamination Phyexamination, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            {
                if (Phyexamination == null || Phyexamination.PatientId != id)
                {
                    result = BadRequest(_localizer["InvalidRecord"]);
                }
                else
                {
                    try
                    {
                        await _physicalDataHandler.UpdateExamination(Phyexamination, orgId, isSubscription);
                        result = Ok(_localizer["UpdateSuccessful"]);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, _localizer["UpdateLogError"]);
                        result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                    }
                }
                return result;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="physicalExamination"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("UpdateExaminationList/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateExaminationList([FromBody] List<PhysicalExamination> physicalExamination, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            try
            {
                await _physicalDataHandler.UpdateExaminationList(physicalExamination, orgId, isSubscription);
                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);

                result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
            }
            return result;
        }

    }
}