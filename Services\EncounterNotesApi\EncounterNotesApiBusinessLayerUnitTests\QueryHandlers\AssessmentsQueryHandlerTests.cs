using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class AssessmentsQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IAssessmentsRepository> _assessmentsRepositoryMock;
        private AssessmentsQueryHandler _assessmentsQueryHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _assessmentsRepositoryMock = new Mock<IAssessmentsRepository>();

            _unitOfWorkMock.Setup(u => u.AssessmentsRepository).Returns(_assessmentsRepositoryMock.Object);

            _assessmentsQueryHandler = new AssessmentsQueryHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task GetAllAssessmentsById_ShouldReturnAllAssessmentsForPatient()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var assessments = new List<AssessmentsData>
            {
                new AssessmentsData
                {
                    AssessmentsID = Guid.NewGuid(),
                    PatientId = patientId,
                    Diagnosis = "Hypertension",
                    ICDCode = "I10",
                    Specify = "Essential hypertension",
                    Notes = "Patient has high blood pressure",
                    IsActive = true
                },
                new AssessmentsData
                {
                    AssessmentsID = Guid.NewGuid(),
                    PatientId = patientId,
                    Diagnosis = "Type 2 Diabetes",
                    ICDCode = "E11",
                    Specify = "Type 2 diabetes mellitus",
                    Notes = "Patient has elevated blood glucose",
                    IsActive = false
                },
                new AssessmentsData
                {
                    AssessmentsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    Diagnosis = "Asthma",
                    ICDCode = "J45",
                    Specify = "Asthma",
                    Notes = "Patient has breathing difficulties",
                    IsActive = true
                }
            };

            _assessmentsRepositoryMock.Setup(r => r.GetAllAssessmentsAsync(_orgId, _subscription))
                .ReturnsAsync(assessments);

            // Act
            var result = await _assessmentsQueryHandler.GetAllAssessmentsById(patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(a => a.PatientId == patientId).Should().BeTrue();
            result.Should().Contain(a => a.Diagnosis == "Hypertension");
            result.Should().Contain(a => a.Diagnosis == "Type 2 Diabetes");
            result.Should().NotContain(a => a.Diagnosis == "Asthma");
        }

        [Test]
        public async Task GetAssessmentsByIdAndIsActive_ShouldReturnOnlyActiveAssessmentsForPatient()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var assessments = new List<AssessmentsData>
            {
                new AssessmentsData
                {
                    AssessmentsID = Guid.NewGuid(),
                    PatientId = patientId,
                    Diagnosis = "Hypertension",
                    ICDCode = "I10",
                    Specify = "Essential hypertension",
                    Notes = "Patient has high blood pressure",
                    IsActive = true
                },
                new AssessmentsData
                {
                    AssessmentsID = Guid.NewGuid(),
                    PatientId = patientId,
                    Diagnosis = "Type 2 Diabetes",
                    ICDCode = "E11",
                    Specify = "Type 2 diabetes mellitus",
                    Notes = "Patient has elevated blood glucose",
                    IsActive = false
                },
                new AssessmentsData
                {
                    AssessmentsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    Diagnosis = "Asthma",
                    ICDCode = "J45",
                    Specify = "Asthma",
                    Notes = "Patient has breathing difficulties",
                    IsActive = true
                }
            };

            _assessmentsRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(assessments);

            // Act
            var result = await _assessmentsQueryHandler.GetAssessmentsByIdAndIsActive(patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.All(a => a.PatientId == patientId && a.IsActive).Should().BeTrue();
            result.Should().Contain(a => a.Diagnosis == "Hypertension");
            result.Should().NotContain(a => a.Diagnosis == "Type 2 Diabetes");
            result.Should().NotContain(a => a.Diagnosis == "Asthma");
        }

        [Test]
        public async Task GetAllMedications_ShouldReturnMedicationsRelatedToAssessments()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var medications = new List<string>
            {
                "Hypertension == Lisinopril 10mg",
                "Type 2 Diabetes == Metformin 500mg"
            };

            _assessmentsRepositoryMock.Setup(r => r.GetAssessmentRelatedMedications(patientId, _orgId, _subscription))
                .ReturnsAsync(medications);

            // Act
            var result = await _assessmentsQueryHandler.GetAllMedications(patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.Should().Contain("Hypertension == Lisinopril 10mg");
            result.Should().Contain("Type 2 Diabetes == Metformin 500mg");
        }

        [Test]
        public async Task GetAllAssessmentsById_WithNoAssessments_ShouldReturnEmptyCollection()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var assessments = new List<AssessmentsData>();

            _assessmentsRepositoryMock.Setup(r => r.GetAllAssessmentsAsync(_orgId, _subscription))
                .ReturnsAsync(assessments);

            // Act
            var result = await _assessmentsQueryHandler.GetAllAssessmentsById(patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetAssessmentsByIdAndIsActive_WithNoAssessments_ShouldReturnEmptyCollection()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var assessments = new List<AssessmentsData>();

            _assessmentsRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(assessments);

            // Act
            var result = await _assessmentsQueryHandler.GetAssessmentsByIdAndIsActive(patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetAllMedications_WithNoMedications_ShouldReturnEmptyCollection()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var medications = new List<string>();

            _assessmentsRepositoryMock.Setup(r => r.GetAssessmentRelatedMedications(patientId, _orgId, _subscription))
                .ReturnsAsync(medications);

            // Act
            var result = await _assessmentsQueryHandler.GetAllMedications(patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
