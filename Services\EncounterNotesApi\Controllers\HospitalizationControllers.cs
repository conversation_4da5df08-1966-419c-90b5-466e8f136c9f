﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using Microsoft.Azure.Amqp.Framing;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using ShardModels;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class HospitalizationRecordController : ControllerBase
    {
        private readonly IHospitalizationRecordCommandHandler<HospitalizationRecord> _hospitalizationRecordCommandHandler;
        private readonly IHospitalizationRecordQueryHandler<HospitalizationRecord> _hospitalizationRecordQueryHandler;
        private readonly ILogger<HospitalizationRecordController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;
        public HospitalizationRecordController(
            IHospitalizationRecordQueryHandler<HospitalizationRecord> hospitalizationRecordQueryHandler,
            IHospitalizationRecordCommandHandler<HospitalizationRecord> hospitalizationRecordCommandHandler,
            ILogger<HospitalizationRecordController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer,
            RecordDatabaseContext context
            )
        {
            _hospitalizationRecordQueryHandler = hospitalizationRecordQueryHandler;
            _hospitalizationRecordCommandHandler = hospitalizationRecordCommandHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        ///  Get All HospitalizationRecords by Id 
        /// </summary>
        [HttpGet("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<List<HospitalizationRecord>>> GetById(Guid id, Guid orgId, bool isSubscription)
        {
            try
            {
                var hospitalizationRecord = await _hospitalizationRecordQueryHandler.GetHospitalizationRecordById(id, orgId, isSubscription);

                if (hospitalizationRecord == null || !hospitalizationRecord.Any())
                {
                    return NotFound(_localizer["RecordNotFound"]);
                }

                return Ok(hospitalizationRecord);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500, _localizer["GetLogError"]);
            }
        }

        /// <summary>
        /// Update an existing HospitalizationRecord
        /// </summary>
        [HttpPut("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateById(Guid id, [FromBody] HospitalizationRecord hospitalizationRecord, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            if (hospitalizationRecord == null || hospitalizationRecord.RecordID != id)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _hospitalizationRecordCommandHandler.UpdateHospitalizationRecord(hospitalizationRecord, orgId, isSubscription);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                }
            }
            return result;
        }

        /// <summary>
        ///  Delete an existing HospitalizationRecord By Id
        /// </summary>
        [HttpDelete("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> DeleteById(Guid id, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            try
            {
                await _hospitalizationRecordCommandHandler.DeleteHospitalizationRecordById(id, orgId, isSubscription);
                result = Ok(_localizer["SuccessfulDeletion"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
            }
            return result;
        }

        /// <summary>
        /// Add new HospitalizationRecord List
        /// </summary>
        [HttpPost]
        [Route("{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> AddAddress([FromBody] List<HospitalizationRecord> records, Guid orgId, bool isSubscription)
        {
            IActionResult response;

            if (records == null || records.Count == 0)
            {
                _logger.LogWarning(_localizer["InvalidAddressData"]);
                response = BadRequest(_localizer["AddressInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["AddingNewAddress"]);
                    await _hospitalizationRecordCommandHandler.AddHospitalizationRecord(records, orgId, isSubscription);
                    _logger.LogInformation(_localizer["AddressAddedSuccessfully"]);
                    response = Ok(_localizer["AddSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorAddingAddress"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        /// <summary>
        ///  Get Active HospitalizationRecord by Id 
        /// </summary>
        [HttpGet("{id:guid}/isActive/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<HospitalizationRecord>>> GetAllByIdAndIsActive(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult result;
            try
            {
                var hospitalizationRecord = await _hospitalizationRecordQueryHandler.GetHospitalizationRecordByIdAndIsActive(id, orgId, isSubscription);
                result = hospitalizationRecord != null ? Ok(hospitalizationRecord) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        /// <summary>
        ///  Update an existing List of HospitalizationRecords
        /// </summary>
        [HttpPut("updateHospitalizationRecord/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateAccess(List<HospitalizationRecord> hospitalizationRecords, Guid orgId, bool isSubscription)
        {
            if (hospitalizationRecords == null)
            {
                return BadRequest(_localizer["AccessError"]);
            }
            var success = await _hospitalizationRecordCommandHandler.UpdateHospitalizationRecordList(hospitalizationRecords, orgId, isSubscription);

            if (success)
            {
                return Ok(new { Message = _localizer["UpdateSuccessful"] });
            }
            else
            {
                return NotFound(_localizer["Record not found"]);
            }
        }

    }
}