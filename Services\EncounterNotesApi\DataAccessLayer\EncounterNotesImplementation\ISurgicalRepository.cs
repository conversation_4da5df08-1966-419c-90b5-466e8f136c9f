﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface ISurgicalRepository : IShardGenericRepository<SurgicalHistory>
    {
        Task<IEnumerable<SurgicalHistory>> GetAllSurgeriesAsync( Guid OrgId, bool Subscription);
    }
}