﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using ShardModels;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public interface IProcedureCommandHandler<T> where T : class
    {
        Task AddProcedure(IEnumerable<T> procedure, Guid OrgId, bool Subscription);
        Task UpdateProcedure(T procedure, Guid OrgId, bool Subscription);
        Task UpdateProcedureListAsync(List<Procedure> procedure, Guid OrgId, bool Subscription);
    }
}
