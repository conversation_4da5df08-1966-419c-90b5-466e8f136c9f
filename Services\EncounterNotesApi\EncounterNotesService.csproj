﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>b4f96290-a040-4adf-a17a-72b68c8dbe8a</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>.</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="BusinessLayer\**" />
    <Compile Remove="Contracts\**" />
    <Compile Remove="DataAccessLayer\**" />
    <Compile Remove="EncounterNotesApiBusinessLayerTestCases\**" />
    <Compile Remove="EncounterNotesApiBusinessLayerUnitTests\**" />
    <Compile Remove="EncounterNotesApiControllerTestCases\**" />
    <Compile Remove="EncounterNotesApiDataAccessLayerTestCases\**" />
    <Compile Remove="EncounterNotesService.Tests\**" />
    <Content Remove="BusinessLayer\**" />
    <Content Remove="Contracts\**" />
    <Content Remove="DataAccessLayer\**" />
    <Content Remove="EncounterNotesApiBusinessLayerTestCases\**" />
    <Content Remove="EncounterNotesApiBusinessLayerUnitTests\**" />
    <Content Remove="EncounterNotesApiControllerTestCases\**" />
    <Content Remove="EncounterNotesApiDataAccessLayerTestCases\**" />
    <Content Remove="EncounterNotesService.Tests\**" />
    <EmbeddedResource Remove="BusinessLayer\**" />
    <EmbeddedResource Remove="Contracts\**" />
    <EmbeddedResource Remove="DataAccessLayer\**" />
    <EmbeddedResource Remove="EncounterNotesApiBusinessLayerTestCases\**" />
    <EmbeddedResource Remove="EncounterNotesApiBusinessLayerUnitTests\**" />
    <EmbeddedResource Remove="EncounterNotesApiControllerTestCases\**" />
    <EmbeddedResource Remove="EncounterNotesApiDataAccessLayerTestCases\**" />
    <EmbeddedResource Remove="EncounterNotesService.Tests\**" />
    <None Remove="BusinessLayer\**" />
    <None Remove="Contracts\**" />
    <None Remove="DataAccessLayer\**" />
    <None Remove="EncounterNotesApiBusinessLayerTestCases\**" />
    <None Remove="EncounterNotesApiBusinessLayerUnitTests\**" />
    <None Remove="EncounterNotesApiControllerTestCases\**" />
    <None Remove="EncounterNotesApiDataAccessLayerTestCases\**" />
    <None Remove="EncounterNotesService.Tests\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.3" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.22.2" />
    <PackageReference Include="Carbon.Redis" Version="2.8.2" />
    <PackageReference Include="Concentus" Version="2.2.2" />
    <PackageReference Include="Concentus.Oggfile" Version="1.0.6" />
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="FFmpegBlazor" Version="1.0.0.10" />
	  <PackageReference Include="FFMpegCore" Version="5.1.0" />
	  <PackageReference Include="Google_GenerativeAI" Version="1.0.2" />
	  <PackageReference Include="Markdig" Version="0.40.0" />
	  <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
	  <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="8.0.11" />
	  <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.8" />
	  <PackageReference Include="Microsoft.Azure.SqlDatabase.ElasticScale.Client" Version="2.4.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="8.0.10" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.8.3" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.24.1" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NUnit" Version="4.3.2" />
    <PackageReference Include="OpenAI" Version="2.1.0" />
    <PackageReference Include="ShardInterfaces" Version="1.0.0" />
    <PackageReference Include="ShardModels" Version="1.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.9.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="System.Text.Json" Version="9.0.2" />
    <PackageReference Include="Xabe.FFmpeg" Version="5.2.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="BusinessLayer\EncounterNotesBusinessLayer.csproj" />
    <ProjectReference Include="Contracts\EncounterNotesContracts.csproj" />
    <ProjectReference Include="DataAccessLayer\EncounterNotesDataAccessLayer.csproj" />
  </ItemGroup>

</Project>
