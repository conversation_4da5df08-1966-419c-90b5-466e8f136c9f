﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface IDiagnosticImagingCommandHandler<TText>
    {
        Task DeleteDiagnosticImagingById(Guid id, Guid OrgId, bool Subscription);

        Task<bool> UpdateDiagnosticImagingList(List<DiagnosticImage> diagnosticImagingList, Guid OrgId, bool Subscription);

        Task AddDiagnosticImaging(List<TText> diagnosticImagingList, Guid OrgId, bool Subscription);

        Task UpdateDiagnosticImaging(DiagnosticImage diagnosticImaging, Guid OrgId, bool Subscription);

    }
}
