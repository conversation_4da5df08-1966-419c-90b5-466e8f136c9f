﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Interfaces.ShardManagement;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly RecordDatabaseContext _context;
        private readonly IStringLocalizer<EncounterDataAccessLayer> _localizer;
        private readonly ILogger<RecordDatabaseContext> _logger;
        private readonly ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> _shardMapManagerService;

        public IRxNormConceptRepository RxNormConceptRepository { get; }
        public IFDBMedicationRepository FDBMedicationRepository { get; }
        public IFDBMedicationNameRepository FDBMedicationNameRepository { get; }
        public IFDBRoutedMedicationRepository FDBRoutedMedicationRepository { get; }
        public IFDBRoutedDosageFormMedicationRepository FDBRoutedDosageFormMedicationRepository { get; }
        public IFDBRouteLookUpRepository FDBRouteLookUpRepository { get; }
        public IFDBTakeLookUpRepository FDBTakeLookUpRepository { get; }
        public IFDBAllergyRepository FDBAllergyRepository { get; }
        public IFDBVaccineRepository FDBVaccineRepository { get; }
        public IFDB_ICDRepository FDBICDRepository { get; }
        public IFDBVaccine_CPT_CVXRepository FDBVaccine_CPT_CVXRepository { get; }
        public IRecordRepository RecordRepository { get; }
        public IFamilyMemberRepository FamilyMemberRepository { get; }
        public IRelationRepository RelationRepository { get; }
        public IDiagnosticImagingAssessmentRepository DiagnosticImagingAssessmentRepository { get; }
        public IAllergyRepository AllergyRepository { get; }
        public IHospitalizationRecordRepository HospitalizationRecordRepository { get; }
        public ITemplatesRepository TemplatesRepository { get; }
        public IPredefinedTemplatesRepository PredefinedTemplatesRepository { get; }
        public ISurgicalRepository SurgicalRepository { get; }
        public IImmunizationRepository ImmunizationRepository { get; }
        public IPastResultsRepository PastResultsRepository { get; }
        public ISoapNotesComponentsRepository SoapNotesComponentsRepository { get; }
        public IPhysicalExaminationRepository PhysicalExaminationRepository { get; }
        public IICDRepository ICDRepository { get; }
        public IVaccinesRepository VaccinesRepository { get; }
        public ITherapeuticInterventionsListRepository TherapeuticInterventionsListRepository { get; }
        public IHistoryOfPresentIllnessRepository HistoryOfPresentIllnessRepository {  get; }
        public ISymptomsRepository SymptomsRepository { get; }
        public IChiefComplaintRepository<ChiefComplaint> ChiefComplaintRepository { get; }   
        public IMedicalHistoryRepository MedicalHistoryRepository { get; }
        public ICurrentMedicationRepository CurrentMedicationRepository { get; }
        public ISocialHistoryRepository SocialHistoryRepository { get; }
        public IVitalRepository VitalRepository { get; }
        public IReferralOutgoingRepository ReferralOutgoingRepository { get; }
        public IReviewOfSystemRepository ReviewOfSystemRepository { get; }
        public IVisionExaminationRepository VisionExaminationRepository { get; }
        public IAssessmentsRepository AssessmentsRepository { get; }
        public ITherapeuticInterventionsRepository TherapeuticInterventionsRepository { get; }
        public IPhysicalTherapyRepository PhysicalTherapyRepository { get; }

        public IProcedureRepository ProcedureRepository { get; }
        public ICPTRepository CPTRepository { get; }

        public IPrescriptionMedicationRepository PrescriptionMedicationRepository { get; }
        public IDiagnosticImagingRepository DiagnosticImagingRepository { get; }
        public IDiagnosticImagingPageRepository DiagnosticImagingPageRepository { get; }
        public IObHistoryRepository<ObHistory> ObHistoryRepository { get; }
        public IGynHistoryRepository<GynHistory> GynHistoryRepository { get; }
        public IExaminationRepository ExaminationRepository { get; }
        public ILabTestsRepository LabTestsRepository { get; }
        public IBillingRepository BillingRepository { get; }

        public UnitOfWork(RecordDatabaseContext context, ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> shardMapManagerService, IStringLocalizer<EncounterDataAccessLayer> localizer,
                                 ILogger<RecordDatabaseContext> logger)
        {
            _shardMapManagerService = shardMapManagerService ?? throw new ArgumentNullException(nameof(shardMapManagerService));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _context = context ?? throw new ArgumentNullException(nameof(context));
            
            VisionExaminationRepository = new VisionExaminationRepository(_context, _shardMapManagerService, localizer, _logger);
            AllergyRepository = new AllergyRepository(_context, _shardMapManagerService, localizer, _logger);
            SocialHistoryRepository = new SocialHistoryRepository(_context, _shardMapManagerService, localizer, _logger);
            RecordRepository = new RecordRepository(_context, _shardMapManagerService, localizer, _logger);
            FamilyMemberRepository = new FamilyMemberRepository(_context, _shardMapManagerService, localizer, _logger);
            DiagnosticImagingRepository = new DiagnosticImagingRepository(_context, _shardMapManagerService, localizer, _logger);
            DiagnosticImagingPageRepository = new DiagnosticImagingPageRepository(_context, _shardMapManagerService, localizer, _logger);
            CurrentMedicationRepository = new CurrentMedicationRepository(_context, _shardMapManagerService, localizer, _logger);
            SurgicalRepository = new SurgicalRepository(_context, _shardMapManagerService, localizer, _logger);
            ImmunizationRepository = new ImmunizationRepository(_context, _shardMapManagerService, localizer, _logger);
            PhysicalExaminationRepository = new PhysicalExaminationRepository(_context, _shardMapManagerService, localizer, _logger);
            PastResultsRepository = new PastResultsRepository(_context, _shardMapManagerService, localizer, _logger);
            HospitalizationRecordRepository = new HospitalizationRecordRepository(_context, _shardMapManagerService, localizer, _logger);
            HistoryOfPresentIllnessRepository = new HistoryOfPresentIllnessRepository(_context, _shardMapManagerService, localizer, _logger);
            ChiefComplaintRepository = new ChiefComplaintRepository(_context, _shardMapManagerService, localizer, _logger);
            MedicalHistoryRepository = new MedicalHistoryRepository(_context, _shardMapManagerService, localizer, _logger);
            PhysicalTherapyRepository = new PhysicalTherapyRepository(_context, _shardMapManagerService, localizer, _logger);
            AssessmentsRepository = new AssessmentsRepository(_context, _shardMapManagerService, localizer, _logger);
            TherapeuticInterventionsRepository = new TherapeuticInterventionsRepository(_context, _shardMapManagerService, localizer, _logger);
            ReviewOfSystemRepository = new ReviewOfSystemRepository(_context, _shardMapManagerService, localizer, _logger);
            VitalRepository = new VitalRepository(_context, _shardMapManagerService, localizer, _logger);
            ReferralOutgoingRepository = new ReferralOutgoingRepository(_context, _shardMapManagerService, localizer, _logger);
            ProcedureRepository = new ProcedureRepository(_context, _shardMapManagerService, localizer, _logger);
            PrescriptionMedicationRepository = new PrescriptionMedicationRepository(_context, _shardMapManagerService, localizer, _logger);
            ObHistoryRepository = new ObHistoryRepository(_context, _shardMapManagerService, localizer, _logger);
            GynHistoryRepository = new GynHistoryRepository(_context, _shardMapManagerService, localizer, _logger);
            ExaminationRepository = new ExaminationRepository(_context, _shardMapManagerService, localizer, _logger);
            TemplatesRepository = new TemplatesRepository(_context, _shardMapManagerService, localizer, _logger);

            RxNormConceptRepository = new RxNormConceptRepository(context,localizer);
            FDBMedicationRepository = new FDBMedicationRepository(context, localizer);
            FDBMedicationNameRepository = new FDBMedicationNameRepository(context, localizer);
            FDBRoutedMedicationRepository = new FDBRoutedMedicationRepository(context, localizer);
            FDBRoutedDosageFormMedicationRepository = new FDBRoutedDosageFormMedicationRepository(context, localizer);
            FDBTakeLookUpRepository = new FDBTakeLookUpRepository(context, localizer);
            FDBRouteLookUpRepository = new FDBRouteLookUpRepository(context, localizer);
            FDBAllergyRepository = new FDBAllergyRepository(context, localizer);
            FDBVaccineRepository = new FDBVaccineRepository(context, localizer);
            FDBVaccine_CPT_CVXRepository = new FDBVaccine_CPT_CVXRepository(context, localizer);
            FDBICDRepository = new FDB_ICDRepository(context, localizer);

            DiagnosticImagingAssessmentRepository = new DiagnosticImagingAssessmentRepository(_context, localizer);
            ICDRepository = new ICDRepository(_context, localizer);
            VaccinesRepository = new VaccinesRepository(_context, localizer);
            TherapeuticInterventionsListRepository = new TherapeuticInterventionsListRepository(_context, localizer);
            SoapNotesComponentsRepository = new SoapNotesComponentsRepository(_context, localizer);
            SymptomsRepository = new SymptomsRepository(_context, localizer);
            CPTRepository = new CPTRepository(_context, localizer);
            PredefinedTemplatesRepository = new PredefinedTemplatesRepository(_context, localizer);
            RelationRepository = new RelationRepository(_context, localizer);
            LabTestsRepository =  new LabTestsRepository(_context, localizer);
            BillingRepository = new BillingRepository(context, localizer);

        }
        public async Task<int> SaveAsync()
        {
            return await _context.SaveChangesAsync();
        }
        public void Dispose()
        {
            _context.Dispose();
        }
    }
}