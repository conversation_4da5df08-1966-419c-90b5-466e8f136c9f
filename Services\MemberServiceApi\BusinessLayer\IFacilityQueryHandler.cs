﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IFacilityQueryHandler<T> where T : Facility
    {
        Task<T> GetFacilityByIdAsync(bool Subscription, Guid id, Guid OrgID);
        Task<IEnumerable<T>> GetFacilitiesByNameAsync(bool Subscription, string name, Guid OrgID);
        Task<List<T>> GetAllFacilitiesAsync(Guid orgId, bool Subscription);
        Task<IEnumerable<T>> GetFacilitiesByOrgAsync(bool Subscription, Guid id);
    }
}
