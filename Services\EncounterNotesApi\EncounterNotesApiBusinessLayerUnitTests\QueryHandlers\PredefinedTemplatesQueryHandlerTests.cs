using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class PredefinedTemplatesQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IPredefinedTemplatesRepository> _predefinedTemplatesRepositoryMock;
        private PredefinedTemplatesQueryHandler _predefinedTemplatesQueryHandler;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _predefinedTemplatesRepositoryMock = new Mock<IPredefinedTemplatesRepository>();

            _unitOfWorkMock.Setup(u => u.PredefinedTemplatesRepository).Returns(_predefinedTemplatesRepositoryMock.Object);
            _predefinedTemplatesQueryHandler = new PredefinedTemplatesQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetTemplates_ShouldReturnAllTemplates()
        {
            // Arrange
            var templates = new List<PredefinedTemplates>
            {
                new PredefinedTemplates
                {
                    Id = Guid.NewGuid(),
                    Template = "Template for general examination",
                    TemplateName = "General Examination",
                    IsDefault = true,
                    VisitType = "General"
                },
                new PredefinedTemplates
                {
                    Id = Guid.NewGuid(),
                    Template = "Template for follow-up visit",
                    TemplateName = "Follow-up Visit",
                    IsDefault = false,
                    VisitType = "Follow-up"
                },
                new PredefinedTemplates
                {
                    Id = Guid.NewGuid(),
                    Template = "Template for annual check-up",
                    TemplateName = "Annual Check-up",
                    IsDefault = false,
                    VisitType = "Annual"
                }
            };

            _predefinedTemplatesRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(templates);

            // Act
            var result = await _predefinedTemplatesQueryHandler.GetTemplates();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(3);
            result.Should().BeEquivalentTo(templates);
        }

        [Test]
        public async Task GetTemplates_WithNoTemplates_ShouldReturnEmptyCollection()
        {
            // Arrange
            var templates = new List<PredefinedTemplates>();

            _predefinedTemplatesRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(templates);

            // Act
            var result = await _predefinedTemplatesQueryHandler.GetTemplates();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetTemplatesById_ShouldReturnTemplateWithMatchingId()
        {
            // Arrange
            var templateId = Guid.NewGuid();
            var template = new PredefinedTemplates
            {
                Id = templateId,
                Template = "Template for general examination",
                TemplateName = "General Examination",
                IsDefault = true,
                VisitType = "General"
            };

            _predefinedTemplatesRepositoryMock.Setup(r => r.GetByIdAsync(templateId))
                .ReturnsAsync(template);

            // Act
            var result = await _predefinedTemplatesQueryHandler.GetTemplatesById(templateId);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be(templateId);
            result.Should().BeEquivalentTo(template);
        }

        [Test]
        public async Task GetTemplatesById_WithNonExistentId_ShouldReturnNull()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid();

            _predefinedTemplatesRepositoryMock.Setup(r => r.GetByIdAsync(nonExistentId))
                .ReturnsAsync((PredefinedTemplates)null);

            // Act
            var result = await _predefinedTemplatesQueryHandler.GetTemplatesById(nonExistentId);

            // Assert
            result.Should().BeNull();
        }

        [Test]
        public async Task GetTemplatesById_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            var templateId = Guid.NewGuid();
            var expectedException = new Exception("Database connection error");

            _predefinedTemplatesRepositoryMock.Setup(r => r.GetByIdAsync(templateId))
                .ThrowsAsync(expectedException);

            // Act & Assert
            await FluentActions.Invoking(async () => await _predefinedTemplatesQueryHandler.GetTemplatesById(templateId))
                .Should().ThrowAsync<Exception>()
                .WithMessage("Database connection error");
        }
    }
}
