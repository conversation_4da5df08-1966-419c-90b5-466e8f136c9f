using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class TemplatesCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<TemplatesCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, Templates, EncounterDataAccessLayer>> _migrationMock;
        private Mock<ITemplatesRepository> _templatesRepositoryMock;
        private TemplatesCommandHandler _templatesCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<TemplatesCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, Templates, EncounterDataAccessLayer>>();
            _templatesRepositoryMock = new Mock<ITemplatesRepository>();

            _unitOfWorkMock.Setup(u => u.TemplatesRepository).Returns(_templatesRepositoryMock.Object);

            _templatesCommandHandler = new TemplatesCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task AddTemplates_ValidTemplates_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var templates = new List<Templates>
            {
                new Templates
                {
                    Id = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    TemplateName = "General Examination",
                    CreatedDate = DateTime.Now,
                    IsDefault = true,
                    Template = "Template content for general examination",
                    VisitType = "General",
                    Subscription = _subscription
                }
            };

            // Act
            await _templatesCommandHandler.AddTemplates(templates, _orgId, _subscription);

            // Assert
            _templatesRepositoryMock.Verify(r => r.AddAsync(templates, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateTemplates_ValidTemplates_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var templates = new List<Templates>
            {
                new Templates
                {
                    Id = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    TemplateName = "Updated Template Name",
                    CreatedDate = DateTime.Now.AddDays(-30),
                    IsDefault = true,
                    Template = "Updated template content",
                    VisitType = "Updated Visit Type",
                    Subscription = _subscription
                }
            };

            // Act
            await _templatesCommandHandler.UpdateTemplates(templates, _orgId, _subscription);

            // Assert
            _templatesRepositoryMock.Verify(r => r.UpdateRangeAsync(templates, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteTemplatesById_ValidId_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var id = Guid.NewGuid();

            // Act
            await _templatesCommandHandler.DeleteTemplatesById(id, _orgId, _subscription);

            // Assert
            _templatesRepositoryMock.Verify(r => r.DeleteByIdAsync(id, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteTemplatesByEntity_ValidEntity_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var template = new Templates
            {
                Id = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                TemplateName = "Template to Delete",
                CreatedDate = DateTime.Now.AddDays(-30),
                IsDefault = false,
                Template = "Template content to delete",
                VisitType = "General",
                Subscription = _subscription
            };

            // Act
            await _templatesCommandHandler.DeleteTemplatesByEntity(template, _orgId, _subscription);

            // Assert
            _templatesRepositoryMock.Verify(r => r.DeleteByEntityAsync(template, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }




    }
}
