using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class MedicalHistoryQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IMedicalHistoryRepository> _medicalHistoryRepositoryMock;
        private MedicalHistoryQueryHandler _medicalHistoryQueryHandler;
        private Guid _orgId;
        private bool _subscription;
        private Guid _patientId;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _medicalHistoryRepositoryMock = new Mock<IMedicalHistoryRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;
            _patientId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.MedicalHistoryRepository).Returns(_medicalHistoryRepositoryMock.Object);
            _medicalHistoryQueryHandler = new MedicalHistoryQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetAllMedicalHistoriesById_ShouldReturnAllMedicalHistoriesForPatient()
        {
            // Arrange
            var medicalHistories = new List<MedicalHistory>
            {
                new MedicalHistory
                {
                    MedicalHistoryID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    History = "Hypertension diagnosed 5 years ago, well-controlled with medication",
                    IsActive = true,
                    Subscription = _subscription
                },
                new MedicalHistory
                {
                    MedicalHistoryID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdatedDate = DateTime.Now.AddDays(-55),
                    History = "Type 2 diabetes diagnosed 3 years ago, managed with diet and exercise",
                    IsActive = false,
                    Subscription = _subscription
                },
                new MedicalHistory
                {
                    MedicalHistoryID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-45),
                    UpdatedDate = DateTime.Now.AddDays(-40),
                    History = "Asthma since childhood, uses inhaler as needed",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _medicalHistoryRepositoryMock.Setup(r => r.GetAllMedicalHistoriesAsync(_orgId, _subscription))
                .ReturnsAsync(medicalHistories);

            // Act
            var result = await _medicalHistoryQueryHandler.GetAllMedicalHistoriesById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(m => m.PatientId == _patientId).Should().BeTrue();
        }

        [Test]
        public async Task GetAllMedicalHistoriesById_WithNoMedicalHistories_ShouldReturnEmptyCollection()
        {
            // Arrange
            var medicalHistories = new List<MedicalHistory>();

            _medicalHistoryRepositoryMock.Setup(r => r.GetAllMedicalHistoriesAsync(_orgId, _subscription))
                .ReturnsAsync(medicalHistories);

            // Act
            var result = await _medicalHistoryQueryHandler.GetAllMedicalHistoriesById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetMedicalHistoryByIdAndIsActive_ShouldReturnActiveMedicalHistoriesForPatient()
        {
            // Arrange
            var medicalHistories = new List<MedicalHistory>
            {
                new MedicalHistory
                {
                    MedicalHistoryID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    History = "Hypertension diagnosed 5 years ago, well-controlled with medication",
                    IsActive = true,
                    Subscription = _subscription
                },
                new MedicalHistory
                {
                    MedicalHistoryID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    History = "Type 2 diabetes diagnosed 3 years ago, managed with diet and exercise",
                    IsActive = false, // Inactive
                    Subscription = _subscription
                },
                new MedicalHistory
                {
                    MedicalHistoryID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    History = "Asthma since childhood, uses inhaler as needed",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _medicalHistoryRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(medicalHistories);

            // Act
            var result = await _medicalHistoryQueryHandler.GetMedicalHistoryByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.All(m => m.PatientId == _patientId && m.IsActive).Should().BeTrue();
        }

        [Test]
        public async Task GetMedicalHistoryByIdAndIsActive_WithNoActiveMedicalHistories_ShouldReturnEmptyCollection()
        {
            // Arrange
            var medicalHistories = new List<MedicalHistory>
            {
                new MedicalHistory
                {
                    MedicalHistoryID = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    History = "Type 2 diabetes diagnosed 3 years ago, managed with diet and exercise",
                    IsActive = false, // Inactive
                    Subscription = _subscription
                }
            };

            _medicalHistoryRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(medicalHistories);

            // Act
            var result = await _medicalHistoryQueryHandler.GetMedicalHistoryByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
