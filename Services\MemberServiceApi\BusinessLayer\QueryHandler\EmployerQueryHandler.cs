﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class EmployerQueryHandler : IEmployerQueryHandler<Employer>
    {
        private readonly IUnitOfWork _unitOfWork;

        public EmployerQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<Employer> GetEmployerByIdAsync(Guid id, Guid orgId, bool Subscription)
        {
            var Employer = await _unitOfWork.EmployerRepository.GetByIdAsync(id, orgId, Subscription);
            return Employer;
        }



        public async Task<List<Employer>> GetAllEmployerAsync(Guid orgId, bool Subscription)
        {
            var Employers = await _unitOfWork.EmployerRepository.GetAllAsync();
            return Employers.ToList();
        }
    }
}


