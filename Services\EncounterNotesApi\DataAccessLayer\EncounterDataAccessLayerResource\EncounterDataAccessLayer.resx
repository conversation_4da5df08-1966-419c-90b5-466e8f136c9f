﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Records" xml:space="preserve">
    <value>Records</value>
  </data>
  <data name="EncounterNotesService" xml:space="preserve">
    <value>EncounterNotesService</value>
  </data>
  <data name="WordTimings" xml:space="preserve">
    <value>WordTimings</value>
  </data>
  <data name="DatabaseError" xml:space="preserve">
    <value>Error in the database</value>
  </data>
  <data name="DateTime" xml:space="preserve">
    <value>DateTime</value>
  </data>
  <data name="PatientCurrentMedication" xml:space="preserve">
    <value>PatientCurrentMedication</value>
  </data>
  <data name="Allergy" xml:space="preserve">
    <value>Allergy</value>
  </data>
  <data name="GetLogErrorAllergy" xml:space="preserve">
    <value>Error while fetching Allergies data</value>
  </data>
  <data name="PostLogErrorAllergy" xml:space="preserve">
    <value>Error while registerinig allergies data</value>
  </data>
  <data name="NoLicense" xml:space="preserve">
    <value>No License</value>
  </data>
  <data name="SuccessfulRegistration" xml:space="preserve">
    <value>Registered successfully</value>
  </data>
  <data name="InvalidRecord" xml:space="preserve">
    <value>Invalid Record</value>
  </data>
  <data name="RedisNotConnectedRetrying" xml:space="preserve">
    <value>Redis is not connected. Retrying...</value>
  </data>
  <data name="FailedToStoreDataInCache" xml:space="preserve">
    <value>Failed to store data in cache for key: {0}</value>
  </data>
  <data name="RedisCacheError " xml:space="preserve">
    <value>Redis Cache Error</value>
  </data>
  <data name="RedisTimeoutError" xml:space="preserve">
    <value>Redis Timeout Error</value>
  </data>
  <data name="PatientSocialHistory" xml:space="preserve">
    <value>PatientSocialHistory</value>
  </data>
  <data name="Assessments" xml:space="preserve">
    <value>Assessments</value>
  </data>
  <data name="FDB" xml:space="preserve">
    <value>FDB</value>
  </data>
  <data name="RxNorm" xml:space="preserve">
    <value>RxNorm</value>
  </data>
  <data name="RxConcepts" xml:space="preserve">
    <value>RxConcepts</value>
  </data>
  <data name="MEDMedication" xml:space="preserve">
    <value>MEDMedication</value>
  </data>
  <data name="MEDMedicationName" xml:space="preserve">
    <value>MEDMedicationName</value>
  </data>
  <data name="MEDRoutedDosageFormMedication" xml:space="preserve">
    <value>MEDRoutedDosageFormMedication</value>
  </data>
  <data name="MEDRoutedMedication" xml:space="preserve">
    <value>MEDRoutedMedication</value>
  </data>
  <data name="MedicationTakeLookUp" xml:space="preserve">
    <value>MedicationTakeLookUp</value>
  </data>
  <data name="MedicationRouteLookUp" xml:space="preserve">
    <value>MedicationRouteLookUp</value>
  </data>
  <data name="Allergies" xml:space="preserve">
    <value>Allergies</value>
  </data>
  <data name="Vaccines" xml:space="preserve">
    <value>Vaccines</value>
  </data>
  <data name="Vaccine_CPT_CVX_LookUp" xml:space="preserve">
    <value>Vaccine_CPT_CVX_LookUp</value>
  </data>
  <data name="ICD" xml:space="preserve">
    <value>ICD</value>
  </data>
</root>