using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class VitalQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IVitalRepository> _vitalRepositoryMock;
        private VitalQueryHandler _vitalQueryHandler;
        private Guid _orgId;
        private bool _subscription;
        private Guid _patientId;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _vitalRepositoryMock = new Mock<IVitalRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;
            _patientId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.VitalRepository).Returns(_vitalRepositoryMock.Object);
            _vitalQueryHandler = new VitalQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetAllVitalsById_ShouldReturnAllVitalsForPatient()
        {
            // Arrange
            var vitals = new List<Vitals>
            {
                new Vitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Temperature = "98.6",
                    BP = "120/80",
                    Weight = "70 kg",
                    Height = "175 cm",
                    Pulse = "72",
                    isActive = true,
                    Subscription = _subscription
                },
                new Vitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdatedDate = DateTime.Now.AddDays(-55),
                    Temperature = "99.1",
                    BP = "125/85",
                    Weight = "71 kg",
                    Height = "175 cm",
                    Pulse = "75",
                    isActive = false,
                    Subscription = _subscription
                },
                new Vitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-45),
                    UpdatedDate = DateTime.Now.AddDays(-40),
                    Temperature = "98.2",
                    BP = "118/78",
                    Weight = "65 kg",
                    Height = "165 cm",
                    Pulse = "68",
                    isActive = true,
                    Subscription = _subscription
                }
            };

            _vitalRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(vitals);

            // Act
            var result = await _vitalQueryHandler.GetAllVitalsById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(v => v.PatientId == _patientId).Should().BeTrue();
        }

        [Test]
        public async Task GetAllVitalsById_WithNoVitals_ShouldReturnEmptyCollection()
        {
            // Arrange
            var vitals = new List<Vitals>();

            _vitalRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(vitals);

            // Act
            var result = await _vitalQueryHandler.GetAllVitalsById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetVitalsByIdAndIsActive_ShouldReturnActiveVitalsForPatient()
        {
            // Arrange
            var vitals = new List<Vitals>
            {
                new Vitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Temperature = "98.6",
                    BP = "120/80",
                    isActive = true,
                    Subscription = _subscription
                },
                new Vitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Temperature = "99.1",
                    BP = "125/85",
                    isActive = false, // Inactive
                    Subscription = _subscription
                },
                new Vitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Temperature = "98.2",
                    BP = "118/78",
                    isActive = true,
                    Subscription = _subscription
                }
            };

            _vitalRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(vitals);

            // Act
            var result = await _vitalQueryHandler.GetVitalsByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.All(v => v.PatientId == _patientId && v.isActive).Should().BeTrue();
        }

        [Test]
        public async Task GetVitalsByIdAndIsActive_WithNoActiveVitals_ShouldReturnEmptyCollection()
        {
            // Arrange
            var vitals = new List<Vitals>
            {
                new Vitals
                {
                    VitalId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Temperature = "99.1",
                    BP = "125/85",
                    isActive = false, // Inactive
                    Subscription = _subscription
                }
            };

            _vitalRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(vitals);

            // Act
            var result = await _vitalQueryHandler.GetVitalsByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
