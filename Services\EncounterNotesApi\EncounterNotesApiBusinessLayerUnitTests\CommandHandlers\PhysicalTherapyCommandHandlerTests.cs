using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class PhysicalTherapyCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<PhysicalTherapyCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, PhysicalTherapyData, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IPhysicalTherapyRepository> _physicalTherapyRepositoryMock;
        private PhysicalTherapyCommandHandler _physicalTherapyCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<PhysicalTherapyCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, PhysicalTherapyData, EncounterDataAccessLayer>>();
            _physicalTherapyRepositoryMock = new Mock<IPhysicalTherapyRepository>();

            _unitOfWorkMock.Setup(u => u.PhysicalTherapyRepository).Returns(_physicalTherapyRepositoryMock.Object);

            _physicalTherapyCommandHandler = new PhysicalTherapyCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task AddPhysicalTherapy_ValidPhysicalTherapies_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var physicalTherapies = new List<PhysicalTherapyData>
            {
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now,
                    TherapyAssessment = "Lower back pain assessment",
                    ShortTermGoals = "Reduce pain level to 3/10 within 2 weeks",
                    LongTermGoals = "Return to normal activities within 2 months",
                    PhysicalTherapyDiagnosis = "Lumbar strain",
                    PhysicalTherapyProgram = "Core strengthening and flexibility",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            // Act
            await _physicalTherapyCommandHandler.AddPhysicalTherapy(physicalTherapies, _orgId, _subscription);

            // Assert
            _physicalTherapyRepositoryMock.Verify(r => r.AddAsync(physicalTherapies, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdatePhysicalTherapy_ValidPhysicalTherapy_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var physicalTherapy = new PhysicalTherapyData
            {
                PhysicalTherapyID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-30),
                UpdatedDate = DateTime.Now,
                TherapyAssessment = "Updated lower back pain assessment",
                ShortTermGoals = "Updated short term goals",
                LongTermGoals = "Updated long term goals",
                PhysicalTherapyDiagnosis = "Updated diagnosis",
                PhysicalTherapyProgram = "Updated program",
                IsActive = true,
                Subscription = _subscription
            };

            // Act
            await _physicalTherapyCommandHandler.UpdatePhysicalTherapy(physicalTherapy, _orgId, _subscription);

            // Assert
            _physicalTherapyRepositoryMock.Verify(r => r.UpdateAsync(physicalTherapy, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdatePhysicalTherapyList_ValidPhysicalTherapies_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var physicalTherapies = new List<PhysicalTherapyData>
            {
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    UpdatedDate = DateTime.Now,
                    TherapyAssessment = "Updated assessment 1",
                    IsActive = true,
                    Subscription = _subscription
                },
                new PhysicalTherapyData
                {
                    PhysicalTherapyID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    UpdatedDate = DateTime.Now,
                    TherapyAssessment = "Updated assessment 2",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            // Act
            await _physicalTherapyCommandHandler.UpdatePhysicalTherapyList(physicalTherapies, _orgId, _subscription);

            // Assert
            _physicalTherapyRepositoryMock.Verify(r => r.UpdateRangeAsync(physicalTherapies, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeletePhysicalTherapyById_ValidId_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var id = Guid.NewGuid();

            // Act
            await _physicalTherapyCommandHandler.DeletePhysicalTherapyById(id, _orgId, _subscription);

            // Assert
            _physicalTherapyRepositoryMock.Verify(r => r.DeleteByIdAsync(id, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeletePhysicalTherapyByEntity_ValidEntity_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var physicalTherapy = new PhysicalTherapyData
            {
                PhysicalTherapyID = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                IsActive = false,
                Subscription = _subscription
            };

            // Act
            await _physicalTherapyCommandHandler.DeletePhysicalTherapyByEntity(physicalTherapy, _orgId, _subscription);

            // Assert
            _physicalTherapyRepositoryMock.Verify(r => r.DeleteByEntityAsync(physicalTherapy, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}
