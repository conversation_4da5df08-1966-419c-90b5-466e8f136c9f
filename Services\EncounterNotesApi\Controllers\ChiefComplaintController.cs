﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using Microsoft.AspNetCore.Authorization;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.Identity.Web.Resource;
using System.Text.Json;
using System.Text.Json.Serialization;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using ShardModels;

namespace EncounterNotesApi.Controllers
{
    /// <summary>
    /// Controller for managing Chief Complaints.
    /// </summary>
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ChiefComplaintController : ControllerBase
    {
        private readonly IChiefComplaintCommandHandler<ChiefComplaint> _chiefcomplaintCommandHandler;
        private readonly IChiefComplaintQueryHandler<ChiefComplaint> _chiefcomplaintQueryHandler;
        private readonly ILogger<ChiefComplaintController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;
        private readonly JsonSerializerOptions _jsonOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="ChiefComplaintController"/> class.
        /// </summary>
        public ChiefComplaintController(
            IChiefComplaintQueryHandler<ChiefComplaint> chiefcomplaintQueryHandler,
            IChiefComplaintCommandHandler<ChiefComplaint> chiefcomplaintCommandHandler,
            ILogger<ChiefComplaintController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer)
        {
            _chiefcomplaintCommandHandler = chiefcomplaintCommandHandler;
            _chiefcomplaintQueryHandler = chiefcomplaintQueryHandler;
            _logger = logger;
            _localizer = localizer;
            _jsonOptions = new JsonSerializerOptions
            {
                ReferenceHandler = ReferenceHandler.IgnoreCycles,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
        }

        /// <summary>
        /// Creates a new Chief Complaint.
        /// </summary>
        [HttpPost]
        [Route("{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> CreateChiefComplaint([FromBody] ChiefComplaint chiefComplaintDto, Guid orgId, bool isSubscription)
        {
            try
            {
                var chiefComplaint = new ChiefComplaint
                {
                    Id = chiefComplaintDto.Id,
                    PatientId = chiefComplaintDto.PatientId,
                    Description = chiefComplaintDto.Description,
                    DateOfComplaint = chiefComplaintDto.DateOfComplaint,
                    OrganizationId = chiefComplaintDto.OrganizationId,
                    PcpId = chiefComplaintDto.PcpId

                };

                await _chiefcomplaintCommandHandler.AddChiefComplaint(new List<ChiefComplaint> { chiefComplaint }, orgId, isSubscription);
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorCreatingComplaint"]);
                return BadRequest(_localizer["ErrorCreatingComplaint"]);
            }
        }

        /// <summary>
        /// Retrieves a Chief Complaint by ID.
        /// </summary>
        [HttpGet("{id}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> GetChiefComplaintById(Guid id, Guid orgId, bool isSubscription)
        {
            try
            {
                var complaint = await _chiefcomplaintQueryHandler.GetChiefComplaintById(id, orgId, isSubscription);
                if (complaint == null)
                {
                    return NotFound(_localizer["ComplaintNotFound"]);
                }
                return Ok(complaint);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingComplaint"]);
                return BadRequest(_localizer["ErrorFetchingComplaint"]);
            }
        }

        /// <summary>
        /// Retrieves Chief Complaints by Patient ID.
        /// </summary>
        [HttpGet("patient/{patientId}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> GetByPatientIdAsync(Guid patientId, Guid orgId, bool isSubscription)
        {
            try
            {
                var complaints = await _chiefcomplaintQueryHandler.GetChiefComplaintsByPatientId(patientId, orgId, isSubscription);
                return Ok(complaints);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingComplaints"]);
                return BadRequest(_localizer["ErrorFetchingComplaints"]);
            }
        }

        /// <summary>
        /// Deletes a Chief Complaint by ID.
        /// </summary>
        [HttpDelete("{id}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> DeleteChiefComplaint(Guid id, Guid orgId, bool isSubscription)
        {
            try
            {
                var complaint = await _chiefcomplaintQueryHandler.GetChiefComplaintById(id, orgId, isSubscription);
                if (complaint == null)
                {
                    return NotFound(_localizer["ComplaintNotFound"]);
                }
                await _chiefcomplaintCommandHandler.DeleteChiefComplaintById(id, orgId, isSubscription);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingComplaint"]);
                return BadRequest(_localizer["ErrorDeletingComplaint"]);
            }
        }

        /// <summary>
        /// Performs a bulk update of Chief Complaints.
        /// </summary>
        [HttpPut("bulk-update/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateComplaintList([FromBody] List<ChiefComplaint> chiefComplaints, Guid orgId, bool isSubscription)
        {
            if (chiefComplaints == null || !chiefComplaints.Any())
            {
                return BadRequest("Invalid records provided.");
            }

            try
            {
                await _chiefcomplaintCommandHandler.UpdateChiefComplaintListAsync(chiefComplaints, orgId, isSubscription);
                return Ok("Bulk update successful");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating Chief Complaint list");
                return StatusCode(500, "An error occurred while updating the complaint list.");
            }
        }

        /// <summary>
        /// Updates a specific Chief Complaint.
        /// </summary>
        [HttpPut("{id}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateChiefComplaint(Guid id, [FromBody] ChiefComplaint chiefComplaintDto, Guid orgId, bool isSubscription)
        {
            try
            {
                var existingComplaint = await _chiefcomplaintQueryHandler.GetChiefComplaintById(id, orgId, isSubscription);
                if (existingComplaint == null)
                {
                    return NotFound(_localizer["ComplaintNotFound"]);
                }

                existingComplaint.Description = chiefComplaintDto.Description;
                existingComplaint.DateOfComplaint = chiefComplaintDto.DateOfComplaint;
                await _chiefcomplaintCommandHandler.UpdateChiefComplaint(existingComplaint, orgId, isSubscription);
                return Ok(existingComplaint);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingComplaint"]);
                return BadRequest(_localizer["ErrorUpdatingComplaint"]);
            }
        }

        /// <summary>
        /// Retrieves all Chief Complaints.
        /// </summary>
        [HttpGet]
        [Route("{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> GetAllChiefComplaints(Guid orgId, bool isSubscription)
        {
            try
            {
                var complaints = await _chiefcomplaintQueryHandler.GetAllChiefComplaints(orgId, isSubscription);
                return Ok(complaints);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingComplaints"]);
                return BadRequest(_localizer["ErrorFetchingComplaints"]);
            }
        }

    }
}