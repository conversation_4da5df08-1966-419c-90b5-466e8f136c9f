﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class AddressesQueryHandler : IAddressesQueryHandler<Address>
    {
        private readonly IUnitOfWork _unitOfWork;

        public AddressesQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<Address> GetAddressByIdAsync(Guid id, Guid OrgID, bool Subscription)
        {
            var address = await _unitOfWork.AddressesRepository.GetByIdAsync(id, OrgID, Subscription);
            return address;
        }

        public async Task<List<Address>> GetAllAddressesAsync(Guid orgId, bool Subscription)
        {
            var addresses = await _unitOfWork.AddressesRepository.GetAllAsync(orgId, Subscription);
            return addresses.ToList();
        }
    }
}
