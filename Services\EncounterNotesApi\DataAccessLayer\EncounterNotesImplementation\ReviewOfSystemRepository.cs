﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Interfaces.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class ReviewOfSystemRepository : ShardGenericRepository<ReviewOfSystem>, IReviewOfSystemRepository
    {
        private readonly RecordDatabaseContext _context;
        private readonly IStringLocalizer<EncounterDataAccessLayer> _localizer;
        private readonly ILogger<RecordDatabaseContext> _logger;
        private readonly ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> _shardMapManagerService;
        private readonly string _shardMapName;
        public ReviewOfSystemRepository(
            RecordDatabaseContext context,
            ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> shardMapManagerService,
            IStringLocalizer<EncounterDataAccessLayer> localizer,
            ILogger<RecordDatabaseContext> logger)
            : base(context, shardMapManagerService, localizer, logger)
        {
            _context = context;
            _shardMapManagerService = shardMapManagerService;
            _localizer = localizer;
            _logger = logger;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }

        public async Task<IEnumerable<ReviewOfSystem>> GetAllReviewOfSystemsAsync(Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return Array.Empty<ReviewOfSystem>();

            return await context.ReviewOfSystems
                .AsNoTracking()
                .ToListAsync();
        }
    }
}
