using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class ICDControllerTests
    {
        private Mock<IICDQueryHandler<ICD>> _icdQueryHandlerMock;
        private Mock<ILogger<ICDController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private RecordDatabaseContext _contextMock;
        private ICDController _controller;
        private List<ICD> _testICDs;

        [SetUp]
        public void Setup()
        {
            _icdQueryHandlerMock = new Mock<IICDQueryHandler<ICD>>();
            _loggerMock = new Mock<ILogger<ICDController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();

            // Since the controller doesn't actually use the context, we can pass null
            _contextMock = null;

            // Mock localizer strings
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error fetching ICD codes."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testICDs = new List<ICD>
            {
                new ICD { Code = "A00.0", Description = "Cholera due to Vibrio cholerae 01, biovar cholerae" },
                new ICD { Code = "A00.1", Description = "Cholera due to Vibrio cholerae 01, biovar eltor" },
                new ICD { Code = "A00.9", Description = "Cholera, unspecified" }
            };

            _controller = new ICDController(
                _icdQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object,
                _contextMock
            );
        }

        [Test]
        public async Task Get_ReturnsListOfICDCodes()
        {
            // Arrange
            _icdQueryHandlerMock.Setup(x => x.GetICD()).ReturnsAsync(_testICDs);

            // Act
            var result = await _controller.Get();

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var icdCodes = okResult.Value as IEnumerable<ICD>;
            icdCodes.Should().NotBeNull();
            icdCodes.Should().BeEquivalentTo(_testICDs);
        }

        [Test]
        public async Task Get_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            _icdQueryHandlerMock.Setup(x => x.GetICD()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Get();

            // Assert
            var statusCodeResult = result.Result as ObjectResult;
            statusCodeResult.Should().NotBeNull();
            statusCodeResult.StatusCode.Should().Be(500);
            statusCodeResult.Value.Should().Be(_localizerMock.Object["GetLogError"]);
        }
    }
}
