﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Interfaces.ShardManagement;
using Microsoft.Extensions.Logging;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class ChiefComplaintRepository : ShardGenericRepository<ChiefComplaint>, IChiefComplaintRepository<ChiefComplaint>
    {
        private readonly ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> _shardMapManagerService;
        private readonly ILogger<RecordDatabaseContext> _logger;
        private readonly string _shardMapName;
        public ChiefComplaintRepository(RecordDatabaseContext context, 
                                        ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> shardMapManagerService,
                                        IStringLocalizer<EncounterDataAccessLayer> localizer,
                                        ILogger<RecordDatabaseContext> logger)
            : base(context, shardMapManagerService, localizer, logger)
        {
            _shardMapManagerService = shardMapManagerService;
            _logger = logger;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }

        public async Task AddAsync(IEnumerable<ChiefComplaint> entities, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            await context.ChiefComplaint.AddRangeAsync(entities);
            await context.SaveChangesAsync();
        }

        public async Task<IEnumerable<ChiefComplaint>> GetAllAsync(Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return Enumerable.Empty<ChiefComplaint>();
            return await context.ChiefComplaint.ToListAsync();
        }

        public async Task<ChiefComplaint> GetByIdAsync(Guid id, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return null;
            return await context.ChiefComplaint.FirstOrDefaultAsync(cc => cc.Id == id && !cc.IsDeleted);
        }

        public async Task<IEnumerable<ChiefComplaint>> GetByPatientIdAsync(Guid patientId, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return Enumerable.Empty<ChiefComplaint>();
            return await context.ChiefComplaint.Where(cc => cc.PatientId == patientId && !cc.IsDeleted).ToListAsync();
        }

        public async Task UpdateAsync(ChiefComplaint entity, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            context.ChiefComplaint.Update(entity);
            await context.SaveChangesAsync();
        }

        public async Task DeleteByEntityAsync(ChiefComplaint entity, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            entity.IsDeleted = true;
            context.ChiefComplaint.Update(entity);
            await context.SaveChangesAsync();
        }

        public async Task DeleteByIdAsync(Guid id, Guid OrgId, bool Subscription)
        {
            var entity = await GetByIdAsync(id, OrgId, Subscription);
            if (entity != null)
            {
                entity.IsDeleted = true;
                await UpdateAsync(entity, OrgId, Subscription);
            }
        }

        public async Task UpdateChiefComplaintListAsync(List<ChiefComplaint> complaints, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;

            if (complaints == null || complaints.Count == 0)
                throw new ArgumentException("Invalid records provided.", nameof(complaints));

            foreach (var complaint in complaints)
            {
                var existingComplaint = await context.ChiefComplaint
                    .FirstOrDefaultAsync(cc => cc.Id == complaint.Id);

                if (existingComplaint != null)
                {
                    existingComplaint.Description = complaint.Description;
                    existingComplaint.DateOfComplaint = complaint.DateOfComplaint;
                    existingComplaint.IsDeleted = complaint.IsDeleted;
                    context.ChiefComplaint.Update(existingComplaint);
                }
            }
            await context.SaveChangesAsync();
        }
    }
}
