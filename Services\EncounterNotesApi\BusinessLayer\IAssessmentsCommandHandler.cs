﻿using EncounterNotesContracts;
using ShardModels;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface IAssessmentsCommandHandler<TText>
    {
        Task DeleteAssessmentsByEntity(AssessmentsData _Assessments, Guid OrgId, bool Subscription);
        Task DeleteAssessmentsById(Guid id, Guid OrgId, bool Subscription);
        Task AddAssessments(List<TText> texts, Guid OrgId, bool Subscription);
        Task UpdateAssessments(AssessmentsData _Assessments, Guid OrgId, bool Subscription);
        Task UpdateAssessmentsList(List<AssessmentsData> _Assessments, Guid OrgId, bool Subscription);
    }
}
