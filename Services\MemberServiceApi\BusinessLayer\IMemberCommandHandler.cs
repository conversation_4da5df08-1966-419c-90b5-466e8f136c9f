﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ShardModels;

namespace MemberServiceBusinessLayer
{
    public interface IMemberCommandHandler<TText>
    {
        Task DeleteMemberByEntity(Member member, Guid OrgID, bool Subscription);
        Task DeleteMemberById(Guid id, Guid OrgID, bool Subscription);
        Task AddMember(List<TText> texts);
        Task UpdateMember(Member member, Guid OrgID, bool Subscription);
        Task<List<string>> RegisterMembersAsync(List<MemberDto> registrations, Guid OrgID, bool Subscription);
    }
}


