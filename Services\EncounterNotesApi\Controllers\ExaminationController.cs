﻿using EncounterNotesContracts;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Web.Resource;
using Microsoft.Extensions.Localization;
using EncounterNotesBusinessLayer;
using EncounterNotesService;
using ShardModels;

namespace EncounterNotesService.Controllers
{

    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ExaminationController : ControllerBase
    {
        private readonly IExaminationCommandHandler _examinationCommandHandler;
        private readonly IExaminationQueryHandler _examinationQueryHandler;
        private readonly ILogger<ExaminationController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;

        public ExaminationController(
            IExaminationCommandHandler examinationCommandHandler,
            IExaminationQueryHandler examinationQueryHandler,
            ILogger<ExaminationController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer)
        {
            _examinationCommandHandler = examinationCommandHandler;
            _examinationQueryHandler = examinationQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        [HttpGet("{PatientId:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<Examination>>> GetAllExaminationByPatientId(Guid PatientId, Guid orgId, bool isSubscription)
        {
            ActionResult<IEnumerable<Examination>> result = StatusCode(StatusCodes.Status500InternalServerError, _localizer["InternalServerError"]);

            try
            {
                var examination = await _examinationQueryHandler.GetExaminationByPatientId(PatientId, orgId, isSubscription);
                result = examination != null ? Ok(examination) : Ok(new List<Examination>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching examinations for PatientId: {PatientId}", PatientId);
            }

            return result;
        }

        [HttpGet("{PatientId:guid}/IsActive/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<Examination>>> GetActiveExaminationByPatientId(Guid PatientId, Guid orgId, bool isSubscription)
        {
            ActionResult<IEnumerable<Examination>> result = StatusCode(StatusCodes.Status500InternalServerError, _localizer["InternalServerError"]);

            try
            {
                var examination = await _examinationQueryHandler.GetExaminationByPatientIdAndIsActive(PatientId, orgId, isSubscription);
                result = examination != null ? Ok(examination) : Ok(new List<Examination>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching active examinations for PatientId: {PatientId}", PatientId);
            }

            return result;
        }

        [HttpPost("Bulk/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult> AddExaminationinBulk([FromBody] List<Examination> examinations, Guid orgId, bool isSubscription)
        {
            ActionResult result = StatusCode(StatusCodes.Status500InternalServerError, _localizer["InternalServerError"]);

            if (examinations == null || !examinations.Any())
            {
                result = BadRequest(_localizer["ExaminationListIsEmpty"]);
            }
            else
            {
                try
                {
                    await _examinationCommandHandler.AddExaminationList(examinations, orgId, isSubscription);
                    result = Ok(_localizer["SuccessfulRegistration"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error adding examinations in bulk");
                }
            }

            return result;
        }



        [HttpPut]
        [Route("UpdateExamList/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateExaminationsList([FromBody] List<Examination> examinations, Guid orgId, bool isSubscription)
        {
            IActionResult result;

            if (examinations == null || !examinations.Any())
            {
                result = BadRequest(_localizer["ExaminationListIsEmpty"]);
            }
            else
            {
                try
                {
                    await _examinationCommandHandler.UpdateExaminationList(examinations, orgId, isSubscription);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);

                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                }
            }
            return result;
        }

    }
}
