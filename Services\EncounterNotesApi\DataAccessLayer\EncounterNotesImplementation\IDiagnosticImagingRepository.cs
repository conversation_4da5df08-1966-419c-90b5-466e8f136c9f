﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IDiagnosticImagingRepository : IShardGenericRepository<DiagnosticImage>
    {
        Task<List<DiagnosticImage>> GetDiagnosticImagingById(Guid recordId, Guid OrgId, bool Subscription);
    }
}
