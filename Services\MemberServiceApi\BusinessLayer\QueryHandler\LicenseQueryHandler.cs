﻿using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class LicenseQueryHandler : ILicenseQueryHandler<ProductLicense>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public LicenseQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<ProductLicense>> GetLicense(Guid OrgId, bool Subscription)
        {
            return await _unitOfWork.ProductLicenseRepository.GetAllAsync(OrgId,Subscription);
        }

        public async Task<ProductLicense> GetLicenseById(Guid id, Guid OrgID, bool Subscription)
        {
            return await _unitOfWork.ProductLicenseRepository.GetByIdAsync(id, OrgID, Subscription);
        }
    }
}