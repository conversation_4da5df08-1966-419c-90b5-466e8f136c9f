using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class HistoryOfPresentIllnessCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<HistoryOfPresentIllnessCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, HistoryOfPresentIllness, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IHistoryOfPresentIllnessRepository> _hpiRepositoryMock;
        private HistoryOfPresentIllnessCommandHandler _hpiCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<HistoryOfPresentIllnessCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, HistoryOfPresentIllness, EncounterDataAccessLayer>>();
            _hpiRepositoryMock = new Mock<IHistoryOfPresentIllnessRepository>();

            _unitOfWorkMock.Setup(u => u.HistoryOfPresentIllnessRepository).Returns(_hpiRepositoryMock.Object);

            _hpiCommandHandler = new HistoryOfPresentIllnessCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task AddHistoryOfPresentIllness_ValidRecords_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var records = new List<HistoryOfPresentIllness>
            {
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Location = "Chest",
                    Duration = "3 days",
                    Severity = "Moderate",
                    Symptoms = "Chest pain, shortness of breath",
                    StartDate = DateTime.Now.AddDays(-3),
                    EndDate = null,
                    Notes = "Pain increases with deep breathing",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            // Act
            await _hpiCommandHandler.AddHistoryOfPresentIllness(records, _orgId, _subscription);

            // Assert
            _hpiRepositoryMock.Verify(r => r.AddAsync(records, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateHistoryOfPresentIllness_ValidRecord_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var record = new HistoryOfPresentIllness
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                Location = "Updated location",
                Duration = "Updated duration",
                Severity = "Updated severity",
                Symptoms = "Updated symptoms",
                StartDate = DateTime.Now.AddDays(-5),
                EndDate = DateTime.Now,
                Notes = "Updated notes",
                IsActive = true,
                Subscription = _subscription
            };

            // Act
            await _hpiCommandHandler.UpdateHistoryOfPresentIllness(record, _orgId, _subscription);

            // Assert
            _hpiRepositoryMock.Verify(r => r.UpdateAsync(record, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteHistoryOfPresentIllnessById_ValidId_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var id = Guid.NewGuid();

            // Act
            await _hpiCommandHandler.DeleteHistoryOfPresentIllnessById(id, _orgId, _subscription);

            // Assert
            _hpiRepositoryMock.Verify(r => r.DeleteByIdAsync(id, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteHistoryOfPresentIllnessByEntity_ValidEntity_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var record = new HistoryOfPresentIllness
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                PCPId = Guid.NewGuid(),
                IsActive = false,
                Subscription = _subscription
            };

            // Act
            await _hpiCommandHandler.DeleteHistoryOfPresentIllnessByEntity(record, _orgId, _subscription);

            // Assert
            _hpiRepositoryMock.Verify(r => r.DeleteByEntityAsync(record, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateHistoryOfPresentIllnessBulk_ValidRecords_ShouldCallRepositoryForEachRecordAndSave()
        {
            // Arrange
            var records = new List<HistoryOfPresentIllness>
            {
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Location = "Updated location 1",
                    Symptoms = "Updated symptoms 1",
                    IsActive = true,
                    Subscription = _subscription
                },
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Location = "Updated location 2",
                    Symptoms = "Updated symptoms 2",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            // Act
            await _hpiCommandHandler.UpdateHistoryOfPresentIllnessBulk(records, _orgId, _subscription);

            // Assert
            _hpiRepositoryMock.Verify(r => r.UpdateRangeAsync(records, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}
