﻿using MemberServiceBusinessLayer;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class ProductMembersQueryHandler : IProductMembersQueryHandler<ProductUserAccess>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public ProductMembersQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<Member>> GetProductMembers(Guid productId, Guid orgId, bool Subscription)
        {
            return await _unitOfWork.ProductUserAccessRepository.GetMembersForProduct(productId, orgId ,Subscription);
        }
       
    }
}