using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using System.Data.SqlClient;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class LabTestsControllerTests
    {
        private Mock<ILabTestsCommandHandler<LabTests>> _labTestsCommandHandlerMock;
        private Mock<ILabTestsQueryHandler<LabTests>> _labTestsQueryHandlerMock;
        private Mock<ILogger<LabTestsController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private LabTestsController _controller;
        private List<LabTests> _testLabTests;

        [SetUp]
        public void Setup()
        {
            _labTestsCommandHandlerMock = new Mock<ILabTestsCommandHandler<LabTests>>();
            _labTestsQueryHandlerMock = new Mock<ILabTestsQueryHandler<LabTests>>();
            _loggerMock = new Mock<ILogger<LabTestsController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No license available."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error occurred."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error posting data."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting data."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Delete successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testLabTests = new List<LabTests>
            {
                new LabTests {
                    LabTestsId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = Guid.NewGuid(),
                    PcpId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    LabTest1 = "Complete Blood Count",
                    LabTest2 = "Lipid Panel",
                    TestOrganization = "Quest Diagnostics",
                    IsActive = true,
                    AssessmentData = "Routine checkup",
                    AssessmentId = Guid.NewGuid()
                },
                new LabTests {
                    LabTestsId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = Guid.NewGuid(),
                    PcpId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-15),
                    LabTest1 = "Comprehensive Metabolic Panel",
                    LabTest2 = "Thyroid Function Tests",
                    TestOrganization = "LabCorp",
                    IsActive = true,
                    AssessmentData = "Follow-up",
                    AssessmentId = Guid.NewGuid()
                }
            };

            _controller = new LabTestsController(
                _labTestsCommandHandlerMock.Object,
                _labTestsQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetAllById_ReturnsLabTests_WhenLabTestsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientLabTests = _testLabTests.Select(lt => { lt.PatientId = patientId; return lt; }).ToList();
            _labTestsQueryHandlerMock.Setup(x => x.GetAllLabTestsById(patientId))
                .ReturnsAsync(patientLabTests);

            // Act
            var result = await _controller.GetAllById(patientId);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedLabTests = okResult.Value as IEnumerable<LabTests>;
            returnedLabTests.Should().NotBeNull();
            returnedLabTests.Should().BeEquivalentTo(patientLabTests);
        }

        [Test]
        public async Task GetAllById_ReturnsNotFound_WhenNoLabTestsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _labTestsQueryHandlerMock.Setup(x => x.GetAllLabTestsById(patientId))
                .ReturnsAsync((IEnumerable<LabTests>)null);

            // Act
            var result = await _controller.GetAllById(patientId);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsLabTests_WhenActiveLabTestsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activeLabTests = _testLabTests.Select(lt => { lt.PatientId = patientId; lt.IsActive = true; return lt; }).ToList();
            _labTestsQueryHandlerMock.Setup(x => x.GetLabTestsByIdAndIsActive(patientId))
                .ReturnsAsync(activeLabTests);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedLabTests = okResult.Value as IEnumerable<LabTests>;
            returnedLabTests.Should().NotBeNull();
            returnedLabTests.Should().BeEquivalentTo(activeLabTests);
        }

        [Test]
        public async Task AddLabTests_ValidLabTests_ReturnsOk()
        {
            // Arrange
            var labTests = _testLabTests;

            // Act
            var result = await _controller.AddLabTests(labTests);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _labTestsCommandHandlerMock.Verify(x => x.AddLabTests(labTests), Times.Once);
        }

        [Test]
        public async Task AddLabTests_EmptyList_ReturnsBadRequest()
        {
            // Arrange
            var emptyLabTests = new List<LabTests>();

            // Act
            var result = await _controller.AddLabTests(emptyLabTests);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);
        }

        [Test]
        public async Task DeleteByEntity_ValidLabTest_ReturnsOk()
        {
            // Arrange
            var labTest = _testLabTests[0];

            // Act
            var result = await _controller.DeleteByEntity(labTest);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _labTestsCommandHandlerMock.Verify(x => x.DeleteLabTestsByEntity(labTest), Times.Once);
        }

        [Test]
        public async Task UpdateLabTestsById_ValidLabTest_ReturnsOk()
        {
            // Arrange
            var labTest = _testLabTests[0];

            // Act
            var result = await _controller.UpdateLabTestsById(labTest.PatientId, labTest);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _labTestsCommandHandlerMock.Verify(x => x.UpdateLabTests(labTest), Times.Once);
        }

        [Test]
        public async Task UpdateLabTestsList_ValidLabTests_ReturnsOk()
        {
            // Arrange
            var labTests = _testLabTests;

            // Act
            var result = await _controller.UpdateLabTestsList(labTests);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _labTestsCommandHandlerMock.Verify(x => x.UpdateLabTestsList(labTests), Times.Once);
        }
    }
}
