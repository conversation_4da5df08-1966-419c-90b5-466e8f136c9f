using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class SurgicalHistoryControllerTests
    {
        private Mock<ISurgicalHistoryCommandHandler<SurgicalHistory>> _surgicalCommandHandlerMock;
        private Mock<ISurgicalHistoryQueryHandler<SurgicalHistory>> _surgicalQueryHandlerMock;
        private Mock<ILogger<SurgicalHistoryController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private SurgicalHistoryController _controller;
        private List<SurgicalHistory> _testSurgeries;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _surgicalCommandHandlerMock = new Mock<ISurgicalHistoryCommandHandler<SurgicalHistory>>();
            _surgicalQueryHandlerMock = new Mock<ISurgicalHistoryQueryHandler<SurgicalHistory>>();
            _loggerMock = new Mock<ILogger<SurgicalHistoryController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No license available."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error occurred."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error posting data."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting data."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Delete successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testSurgeries = new List<SurgicalHistory>
            {
                new SurgicalHistory {
                    SurgeryId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Surgery = "Appendectomy",
                    IsActive = true,
                    Subscription = _isSubscription
                },
                new SurgicalHistory {
                    SurgeryId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-15),
                    Surgery = "Knee Arthroscopy",
                    IsActive = true,
                    Subscription = _isSubscription
                }
            };

            _controller = new SurgicalHistoryController(
                _surgicalCommandHandlerMock.Object,
                _surgicalQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetAllById_ReturnsSurgeries_WhenSurgeriesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientSurgeries = _testSurgeries.Select(s => { s.PatientId = patientId; return s; }).ToList();
            _surgicalQueryHandlerMock.Setup(x => x.GetAllSurgeriesById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientSurgeries);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedSurgeries = okResult.Value as IEnumerable<SurgicalHistory>;
            returnedSurgeries.Should().NotBeNull();
            returnedSurgeries.Should().BeEquivalentTo(patientSurgeries);
        }

        [Test]
        public async Task GetAllById_ReturnsNotFound_WhenNoSurgeriesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _surgicalQueryHandlerMock.Setup(x => x.GetAllSurgeriesById(patientId, _orgId, _isSubscription))
                .ReturnsAsync((IEnumerable<SurgicalHistory>)null);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsSurgeries_WhenActiveSurgeriesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activeSurgeries = _testSurgeries.Select(s => { s.PatientId = patientId; s.IsActive = true; return s; }).ToList();
            _surgicalQueryHandlerMock.Setup(x => x.GetSurgeryByIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activeSurgeries);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedSurgeries = okResult.Value as IEnumerable<SurgicalHistory>;
            returnedSurgeries.Should().NotBeNull();
            returnedSurgeries.Should().BeEquivalentTo(activeSurgeries);
        }

        [Test]
        public async Task Registration_ValidSurgeries_ReturnsOk()
        {
            // Arrange
            var surgeries = _testSurgeries;

            // Act
            var result = await _controller.Registration(surgeries, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _surgicalCommandHandlerMock.Verify(x => x.AddSurgery(surgeries, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task Registration_EmptyList_ReturnsBadRequest()
        {
            // Arrange
            var emptySurgeries = new List<SurgicalHistory>();

            // Act
            var result = await _controller.Registration(emptySurgeries, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);
        }

        [Test]
        public async Task UpdateSurgeryById_ValidSurgery_ReturnsOk()
        {
            // Arrange
            var surgery = _testSurgeries[0];

            // Act
            var result = await _controller.UpdateSurgeryById(surgery.PatientId, surgery, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _surgicalCommandHandlerMock.Verify(x => x.UpdateSurgery(surgery, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateSurgeryList_ValidSurgeries_ReturnsOk()
        {
            // Arrange
            var surgeries = _testSurgeries;

            // Act
            var result = await _controller.UpdateSurgeryList(surgeries, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _surgicalCommandHandlerMock.Verify(x => x.UpdateSurgeryList(surgeries, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task DeleteByEntity_ValidSurgery_ReturnsOk()
        {
            // Arrange
            var surgery = _testSurgeries[0];

            // Act
            var result = await _controller.DeleteByEntity(surgery, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _surgicalCommandHandlerMock.Verify(x => x.DeleteSurgeryByEntity(surgery, _orgId, _isSubscription), Times.Once);
        }


    }
}
