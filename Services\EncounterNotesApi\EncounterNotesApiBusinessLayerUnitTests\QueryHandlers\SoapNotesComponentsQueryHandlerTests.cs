using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class SoapNotesComponentsQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ISoapNotesComponentsRepository> _soapNotesComponentsRepositoryMock;
        private SoapNotesComponentsQueryHandler _soapNotesComponentsQueryHandler;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _soapNotesComponentsRepositoryMock = new Mock<ISoapNotesComponentsRepository>();

            _unitOfWorkMock.Setup(u => u.SoapNotesComponentsRepository).Returns(_soapNotesComponentsRepositoryMock.Object);
            _soapNotesComponentsQueryHandler = new SoapNotesComponentsQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetDetails_ShouldReturnAllSoapNotesComponents()
        {
            // Arrange
            var components = new List<SoapNotesComponents>
            {
                new SoapNotesComponents
                {
                    Id = Guid.NewGuid(),
                    Name = "Subjective"
                },
                new SoapNotesComponents
                {
                    Id = Guid.NewGuid(),
                    Name = "Objective"
                },
                new SoapNotesComponents
                {
                    Id = Guid.NewGuid(),
                    Name = "Assessment"
                },
                new SoapNotesComponents
                {
                    Id = Guid.NewGuid(),
                    Name = "Plan"
                }
            };

            _soapNotesComponentsRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(components);

            // Act
            var result = await _soapNotesComponentsQueryHandler.GetDetails();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(4);
            result.Should().BeEquivalentTo(components);
        }

        [Test]
        public async Task GetDetails_WithNoComponents_ShouldReturnEmptyCollection()
        {
            // Arrange
            var components = new List<SoapNotesComponents>();

            _soapNotesComponentsRepositoryMock.Setup(r => r.GetAsync())
                .ReturnsAsync(components);

            // Act
            var result = await _soapNotesComponentsQueryHandler.GetDetails();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetDetails_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            _soapNotesComponentsRepositoryMock.Setup(r => r.GetAsync())
                .ThrowsAsync(new Exception("Database connection error"));

            // Act & Assert
            await FluentActions.Invoking(async () => await _soapNotesComponentsQueryHandler.GetDetails())
                .Should().ThrowAsync<Exception>()
                .WithMessage("Database connection error");
        }
    }
}
