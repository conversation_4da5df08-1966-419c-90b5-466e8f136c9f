﻿using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class ProductOrganizationMappingsCommandHandler : IProductOrganizationMappingsCommandHandler<ProductOrganizationMapping>
    {
        private readonly IUnitOfWork _unitOfWork;

        public ProductOrganizationMappingsCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddProductOrganizationMappingAsync(List<ProductOrganizationMapping> ProductOrganizationMappings, Guid OrgId)
        {
            await _unitOfWork.ProductOrganizationMappingRepository.AddAsync(ProductOrganizationMappings);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateProductOrganizationMappingAsync(ProductOrganizationMapping ProductOrganizationMapping)
        {
            await _unitOfWork.ProductOrganizationMappingRepository.UpdateAsync(ProductOrganizationMapping);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteProductOrganizationMappingAsync(Guid id)
        {
            await _unitOfWork.ProductOrganizationMappingRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }
    }
}
