﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class FDBVaccineRepository : GenericRepository<FDBVaccines>, IFDBVaccineRepository
    {
        private readonly RecordDatabaseContext _context;

        public FDBVaccineRepository(RecordDatabaseContext context, IStringLocalizer<EncounterDataAccessLayer> localizer) : base(context, localizer)
        {
            _context = context;
        }
        public async Task<List<FDBVaccines>> GetActiveVaccines()
        {
            var ActiveVaccines = await _context.FDBVaccines
            .Where(t => t.EVD_CVX_CODE_STATUS == "Active")
            .ToListAsync();
            return ActiveVaccines;
        }
    }
}
