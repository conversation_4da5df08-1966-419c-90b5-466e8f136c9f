﻿using Contracts;
using MemberServiceBusinessLayer;
using MemberServiceDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class AddressesCommandHandler : IAddressesCommandHandler<Address>
    {
        private readonly IUnitOfWork _unitOfWork;

        public AddressesCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddAddressAsync(List<Address> address, bool Subscription, Guid orgId)
        {
            await _unitOfWork.AddressesRepository.AddAsync(address, orgId, Subscription );
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateAddressAsync(Address address,bool Subscription, Guid orgId)
        {
            await _unitOfWork.AddressesRepository.UpdateAsync(address, orgId, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteAddressAsync(Guid id, bool Subscription, Guid orgId)
        {
            await _unitOfWork.AddressesRepository.DeleteByIdAsync(id, orgId, Subscription);
            await _unitOfWork.SaveAsync();
        }
        public async Task AddAddressAsync(List<Address> addresses, Guid orgId, bool Subscription)
        {
            await _unitOfWork.AddressesRepository.AddAsync(addresses, orgId, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateAddressAsync(bool Subscription, Guid orgId, Address address)
        {
            await _unitOfWork.AddressesRepository.UpdateAsync(address, orgId, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteAddressAsync(Guid id, Guid orgId, bool Subscription)
        {
            await _unitOfWork.AddressesRepository.DeleteByIdAsync(id, orgId, Subscription);
            await _unitOfWork.SaveAsync();
        }
    }
}
