using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class PhysicalExaminationCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<PhysicalExaminationCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, PhysicalExamination, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IPhysicalExaminationRepository> _physicalExaminationRepositoryMock;
        private PhysicalExaminationCommandHandler _physicalExaminationCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<PhysicalExaminationCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, PhysicalExamination, EncounterDataAccessLayer>>();
            _physicalExaminationRepositoryMock = new Mock<IPhysicalExaminationRepository>();

            _unitOfWorkMock.Setup(u => u.PhysicalExaminationRepository).Returns(_physicalExaminationRepositoryMock.Object);

            _physicalExaminationCommandHandler = new PhysicalExaminationCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task AddExamination_ValidExaminations_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var examinations = new List<PhysicalExamination>
            {
                new PhysicalExamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Skin = "Normal",
                    Rash = "None",
                    Tester = "Dr. Smith",
                    Moles = "Few on back",
                    Hemangioma = "None",
                    VascularMalformation = "None",
                    PCPId = Guid.NewGuid(),
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            // Act
            await _physicalExaminationCommandHandler.AddExamination(examinations, _orgId, _subscription);

            // Assert
            _physicalExaminationRepositoryMock.Verify(r => r.AddAsync(examinations, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateExamination_ValidExamination_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var examination = new PhysicalExamination
            {
                ExaminationId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                CreatedDate = DateTime.Now.AddDays(-30),
                UpdatedDate = DateTime.Now,
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                Skin = "Updated skin condition",
                Rash = "Updated rash condition",
                Tester = "Dr. Johnson",
                Moles = "Updated moles description",
                Hemangioma = "Updated hemangioma description",
                VascularMalformation = "Updated vascular malformation description",
                PCPId = Guid.NewGuid(),
                IsActive = true,
                Subscription = _subscription
            };

            // Act
            await _physicalExaminationCommandHandler.UpdateExamination(examination, _orgId, _subscription);

            // Assert
            _physicalExaminationRepositoryMock.Verify(r => r.UpdateAsync(examination, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateExaminationList_ValidExaminations_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var examinations = new List<PhysicalExamination>
            {
                new PhysicalExamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Skin = "Updated skin condition 1",
                    IsActive = true,
                    Subscription = _subscription
                },
                new PhysicalExamination
                {
                    ExaminationId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now,
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    Skin = "Updated skin condition 2",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            // Act
            await _physicalExaminationCommandHandler.UpdateExaminationList(examinations, _orgId, _subscription);

            // Assert
            _physicalExaminationRepositoryMock.Verify(r => r.UpdateRangeAsync(examinations, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteExaminationById_ValidId_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var id = Guid.NewGuid();

            // Act
            await _physicalExaminationCommandHandler.DeleteExaminationById(id, _orgId, _subscription);

            // Assert
            _physicalExaminationRepositoryMock.Verify(r => r.DeleteByIdAsync(id, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteExaminationByEntity_ValidEntity_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var examination = new PhysicalExamination
            {
                ExaminationId = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                OrganizationId = _orgId,
                IsActive = false,
                Subscription = _subscription
            };

            // Act
            await _physicalExaminationCommandHandler.DeleteExaminationByEntity(examination, _orgId, _subscription);

            // Assert
            _physicalExaminationRepositoryMock.Verify(r => r.DeleteByEntityAsync(examination, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }




    }
}
