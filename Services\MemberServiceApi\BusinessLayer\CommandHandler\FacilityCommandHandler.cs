﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using MemberServiceDataAccessLayer.Implementation;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class FacilityCommandHandler : IFacilityCommandHandler<Facility>
    {
        private readonly IUnitOfWork _unitOfWork;

        public FacilityCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        public async Task AddFacilityAsync(List<Facility> facilities,bool Subscription, Guid orgId)
        {
            await _unitOfWork.FacilityRepository.AddAsync(facilities , orgId, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateFacilityAsync(Facility facility, bool Subscription, Guid orgId)
        {
            await _unitOfWork.FacilityRepository.UpdateAsync(facility,orgId, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteFacilityAsync(bool Subscription,Guid id, Guid orgId)
        {
            await _unitOfWork.FacilityRepository.DeleteByIdAsync(id,orgId, Subscription);
            await _unitOfWork.SaveAsync();
        }
    }
}
