﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface IAllergyCommandHandler<TText>
    {
        Task DeleteAllergyByEntity(Allergy allergy, Guid OrgId, bool Subscription);
        Task DeleteAllergyById(Guid id, Guid OrgId, bool Subscription);
        Task AddAllergy(List<TText> texts, Guid OrgId, bool Subscription);
        Task UpdateAllergy(Allergy allergy, Guid OrgId, bool Subscription);
        Task UpdateAllergyList(List<Allergy> newAllergies, List<Allergy> updatedAllergies, List<Allergy> deletedAllergies, Guid OrgId, bool Subscription);
    }
}
