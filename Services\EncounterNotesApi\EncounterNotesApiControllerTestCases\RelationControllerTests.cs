using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using EncounterNotesDataAccessLayer.EncounterNotesContext;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class RelationControllerTests
    {
        private Mock<IRelationCommandHandler<Relations>> _relationCommandHandlerMock;
        private Mock<IRelationQueryHandler<Relations>> _relationQueryHandlerMock;
        private Mock<ILogger<RelationController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private RecordDatabaseContext _contextMock;
        private RelationController _controller;
        private List<Relations> _testRelations;

        [SetUp]
        public void Setup()
        {
            _relationCommandHandlerMock = new Mock<IRelationCommandHandler<Relations>>();
            _relationQueryHandlerMock = new Mock<IRelationQueryHandler<Relations>>();
            _loggerMock = new Mock<ILogger<RelationController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            // Since the controller doesn't actually use the context, we can pass null
            _contextMock = null;

            // Mock localizer strings
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error fetching relations."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No relations provided."));
            _localizerMock.Setup(x => x["SuccessfulEncounterNote"]).Returns(new LocalizedString("SuccessfulEncounterNote", "Relations added successfully."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error adding relations."));

            _testRelations = new List<Relations>
            {
                new Relations
                {
                    RelationId = Guid.NewGuid(),
                    Relation = "Parent"
                },
                new Relations
                {
                    RelationId = Guid.NewGuid(),
                    Relation = "Sibling"
                },
                new Relations
                {
                    RelationId = Guid.NewGuid(),
                    Relation = "Spouse"
                }
            };

            _controller = new RelationController(
                _relationCommandHandlerMock.Object,
                _relationQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object,
                _contextMock
            );
        }

        [Test]
        public async Task Get_ReturnsListOfRelations()
        {
            // Arrange
            _relationQueryHandlerMock.Setup(x => x.GetAllRelations()).ReturnsAsync(_testRelations);

            // Act
            var result = await _controller.Get();

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var relations = okResult.Value as IEnumerable<Relations>;
            relations.Should().NotBeNull();
            relations.Should().BeEquivalentTo(_testRelations);
        }

        [Test]
        public async Task Get_ExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            _relationQueryHandlerMock.Setup(x => x.GetAllRelations()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Get();

            // Assert
            var statusCodeResult = result.Result as ObjectResult;
            statusCodeResult.Should().NotBeNull();
            statusCodeResult.StatusCode.Should().Be(500);
            statusCodeResult.Value.Should().Be(_localizerMock.Object["GetLogError"]);
        }

        [Test]
        public async Task EncounterNote_ValidRelations_ReturnsOk()
        {
            // Arrange
            var relationsToAdd = _testRelations;
            var firstRelationId = relationsToAdd.First().RelationId;

            _relationCommandHandlerMock.Setup(x => x.AddRelation(It.IsAny<List<Relations>>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.EncounterNote(relationsToAdd);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            // Check that the response contains the expected message and first relation ID
            var response = okResult.Value;
            response.Should().NotBeNull();

            // Use reflection to access the anonymous object properties
            var messageProperty = response.GetType().GetProperty("Message");
            var firstRelationIdProperty = response.GetType().GetProperty("FirstRelationId");

            messageProperty.Should().NotBeNull();
            firstRelationIdProperty.Should().NotBeNull();

            var message = messageProperty.GetValue(response);
            var returnedId = firstRelationIdProperty.GetValue(response);

            message.Should().Be(_localizerMock.Object["SuccessfulEncounterNote"]);
            returnedId.Should().Be(firstRelationId);

            // Verify the command handler was called
            _relationCommandHandlerMock.Verify(x => x.AddRelation(It.IsAny<List<Relations>>()), Times.Once);
        }

        [Test]
        public async Task EncounterNote_EmptyRelations_ReturnsBadRequest()
        {
            // Arrange
            var emptyRelations = new List<Relations>();

            // Act
            var result = await _controller.EncounterNote(emptyRelations);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);

            // Verify the command handler was not called
            _relationCommandHandlerMock.Verify(x => x.AddRelation(It.IsAny<List<Relations>>()), Times.Never);
        }

        [Test]
        public async Task EncounterNote_NullRelations_ReturnsBadRequest()
        {
            // Arrange
            List<Relations> nullRelations = null;

            // Act
            var result = await _controller.EncounterNote(nullRelations);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);

            // Verify the command handler was not called
            _relationCommandHandlerMock.Verify(x => x.AddRelation(It.IsAny<List<Relations>>()), Times.Never);
        }

        [Test]
        public async Task EncounterNote_DatabaseException_ReturnsInternalServerError()
        {
            // Arrange
            var relationsToAdd = _testRelations;

            // Create a mock SqlException using reflection since SqlException has internal constructors
            var sqlException = System.Runtime.Serialization.FormatterServices.GetUninitializedObject(typeof(System.Data.SqlClient.SqlException)) as System.Data.SqlClient.SqlException;

            _relationCommandHandlerMock.Setup(x => x.AddRelation(It.IsAny<List<Relations>>()))
                .ThrowsAsync(sqlException);

            // Act
            var result = await _controller.EncounterNote(relationsToAdd);

            // Assert
            var statusCodeResult = result as ObjectResult;
            statusCodeResult.Should().NotBeNull();
            statusCodeResult.StatusCode.Should().Be(500);
            statusCodeResult.Value.Should().Be(_localizerMock.Object["DatabaseError"]);
        }

        [Test]
        public async Task EncounterNote_GeneralException_ReturnsInternalServerError()
        {
            // Arrange
            var relationsToAdd = _testRelations;
            _relationCommandHandlerMock.Setup(x => x.AddRelation(It.IsAny<List<Relations>>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.EncounterNote(relationsToAdd);

            // Assert
            var statusCodeResult = result as ObjectResult;
            statusCodeResult.Should().NotBeNull();
            statusCodeResult.StatusCode.Should().Be(500);
            statusCodeResult.Value.Should().Be(_localizerMock.Object["PostLogError"]);
        }
    }
}
