﻿using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class MemberQueryHandler : IMemberQueryHandler<Member>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public MemberQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<Member>> GetMember(Guid OrgID, bool Subscription)
        {
            return await _unitOfWork.MemberRepository.GetAllAsync(OrgID, Subscription);
        }

        public async Task<Member> GetMemberById(Guid id, Guid OrgID, bool Subscription)
        {
            return await _unitOfWork.MemberRepository.GetByIdAsync(id, OrgID, Subscription);
        }

        public async Task<IEnumerable<ProviderPatient>> GetProviderPatientByOrganizationId(Guid id, bool Subscription)
        {
            return await _unitOfWork.MemberRepository.GetProviderPatientByOrganizationId(id, Subscription);
        }

        public async Task<IEnumerable<ProviderPatient>> GetPatientsByOrganizationId(Guid id, bool Subscription)
        {
            return await _unitOfWork.MemberRepository.GetPatientsByOrganizationId(id,Subscription);
        }
        public async Task<Patient> GetPatientdatabyid(Guid id, Guid OrgID, bool Subscription)
        {
            var member = await _unitOfWork.MemberRepository.GetByIdAsync(id, OrgID, Subscription);
            var addressId = member.AddressId.Value;
            var insuranceId = member.InsuranceId.Value;
            var addressDetails = await _unitOfWork.AddressesRepository.GetByIdAsync(addressId,OrgID,Subscription);
            var insuranceDetails = await _unitOfWork.InsuranceRepository.GetByIdAsync(insuranceId,OrgID,Subscription);

            var patient = new Patient
            {
                Id = member.Id,
                OrganizationID = member.OrganizationID,
                PCPName = member.PCPName,
                Name = member.UserName,
                Email = member.Email,
                PhoneNumber = member.PhoneNumber,
                Sex = member.SexualOrientation,
                DOB = member.DateOfBirth,
                Street = addressDetails.AddressLine1,
                City = addressDetails.City,
                State = addressDetails.State,
                PostalCode = addressDetails.PostalCode,
                PrimaryInsuranceProvider = insuranceDetails.PrimaryInsuranceProvider,
                PolicyNumber = insuranceDetails.PolicyNumber,
                PlanName=insuranceDetails.PlanName,
                GroupNumber =insuranceDetails.GroupNumber,
                PatientImageURL = member.ProfileImageUrl
            };

            return patient;
        }

        public async Task<IEnumerable<Member>> SearchMembersAsync(string searchTerm, Guid OrgID, bool Subscription)
        {
            return await _unitOfWork.MemberRepository.SearchMembersAsync(searchTerm, OrgID, Subscription);
        }
        
        public async Task<IEnumerable<Member>> SearchMembersByEmailAsync(string searchTerm, Guid OrgID, bool Subscription)
        {
            return await _unitOfWork.MemberRepository.SearchMembersByEmailAsync(searchTerm, OrgID, Subscription);
        }
        public async Task<IEnumerable<Office_visit_members>> GetPatientsByIdsAsync(List<Guid> patientIds, Guid OrgID, bool Subscription)
        {
            return await _unitOfWork.MemberRepository.GetPatientsByIdsAsync(patientIds,OrgID,Subscription);
        }

    }
}
