﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class SocialHistoryQueryHandler : ISocialHistoryQueryHandler<PatientSocialHistory>
    {
        private readonly IUnitOfWork _unitOfWork;

        public SocialHistoryQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Retrieves the active social history records for a given patient ID.
        /// </summary>
        public async Task<List<PatientSocialHistory>> GetHistoryByIdAndIsActive(Guid patientId, Guid orgId, bool isSubscription)
        {
            var allRecords = await _unitOfWork.SocialHistoryRepository.GetAsync(orgId, isSubscription);
            return allRecords
                .Where(history => history.PatientId == patientId && history.isActive == true)
                .ToList();
        }

        /// <summary>
        /// Retrieves all social history records for a given patient ID.
        /// </summary>
        public async Task<List<PatientSocialHistory>> GetAllByPatientId(Guid patientId, Guid orgId, bool isSubscription)
        {
            var allRecords = await _unitOfWork.SocialHistoryRepository.GetAsync(orgId, isSubscription);
            return allRecords
                .Where(history => history.PatientId == patientId)
                .ToList();
        }
    }
}
