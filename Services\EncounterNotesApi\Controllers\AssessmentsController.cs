﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using System;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class AssessmentsController : ControllerBase
    {
        private readonly IAssessmentsCommandHandler<AssessmentsData> _AssessmentsDataHandler;
        private readonly IAssessmentsQueryHandler<AssessmentsData> _AssessmentsQueryHandler;
        private readonly ILogger<AssessmentsController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;

        public AssessmentsController(
            IAssessmentsCommandHandler<AssessmentsData> medicalDataHandler,
            IAssessmentsQueryHandler<AssessmentsData> medicalQueryHandler,
            ILogger<AssessmentsController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
        )
        {
            _AssessmentsDataHandler = medicalDataHandler;
            _AssessmentsQueryHandler = medicalQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        [HttpGet("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<AssessmentsData>>> GetAllById(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult<IEnumerable<AssessmentsData>> result;

            try
            {
                var data = await _AssessmentsQueryHandler.GetAllAssessmentsById(id, orgId, isSubscription);
                result = data != null ? Ok(data) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(500, _localizer["GetLogError"]);
            }

            return result;
        }
        [HttpGet("{PatientId:guid}/AssesmentsRelatedMedications/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<List<string>>> GetAllMedicationsRelatedToAssessments(Guid PatientId, Guid orgId, bool isSubscription)
        {
            ActionResult<List<string>> result;
            try
            {
                result = await _AssessmentsQueryHandler.GetAllMedications(PatientId, orgId, isSubscription);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(500, _localizer["GetLogError"]);
            }
            return result;
        }

        [HttpGet("{id:guid}/IsActive/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<AssessmentsData>>> GetAllByIdAndIsActive(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult<IEnumerable<AssessmentsData>> result;

            try
            {
                var diagnosis = await _AssessmentsQueryHandler.GetAssessmentsByIdAndIsActive(id, orgId, isSubscription);
                result = diagnosis != null ? Ok(diagnosis) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(500, _localizer["GetLogError"]);
            }

            return result;
        }

        [HttpPost]
        [Route("AddAssessments/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> AddAssessments([FromBody] List<AssessmentsData> _Assessments, Guid orgId, bool isSubscription)
        {
            IActionResult result;

            if (_Assessments == null || _Assessments.Count == 0)
            {
                result = BadRequest(_localizer["NoLicense"]);
            }
            else
            {
                try
                {
                    await _AssessmentsDataHandler.AddAssessments(_Assessments, orgId, isSubscription);
                    result = Ok(_localizer["SuccessfulRegistration"]);
                }
                catch (SqlException)
                {
                    result = StatusCode(500, _localizer["DatabaseError"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["PostLogError"]);
                    result = StatusCode(500);
                }
            }

            return result;
        }

        [HttpDelete]
        [Route("{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> DeleteByEntity([FromBody] AssessmentsData _Assessments, Guid orgId, bool isSubscription)
        {
            IActionResult result;

            if (_Assessments == null)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _AssessmentsDataHandler.DeleteAssessmentsByEntity(_Assessments, orgId, isSubscription);
                    result = Ok(_localizer["DeleteSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["DeleteLogError"]);
                    result = StatusCode(500, _localizer["DeleteLogError"]);
                }
            }

            return result;
        }

        [HttpPut("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateAssessmentsById(Guid id, [FromBody] AssessmentsData _Assessments, Guid orgId, bool isSubscription)
        {
            IActionResult result;

            if (_Assessments == null || _Assessments.PatientId != id)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _AssessmentsDataHandler.UpdateAssessments(_Assessments, orgId, isSubscription);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(500, _localizer["UpdateLogError"]);
                }
            }

            return result;
        }

        [HttpPut]
        [Route("UpdateAssessmentsList/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateAssessmentsList([FromBody] List<AssessmentsData> _Assessments, Guid orgId, bool isSubscription)
        {
            IActionResult result;

            try
            {
                await _AssessmentsDataHandler.UpdateAssessmentsList(_Assessments, orgId, isSubscription);
                result = Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                result = StatusCode(500, _localizer["UpdateLogError"]);
            }

            return result;
        }

    }
}