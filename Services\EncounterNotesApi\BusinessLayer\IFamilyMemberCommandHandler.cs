﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface IFamilyMemberCommandHandler<TText>
    {
        Task DeleteFamilyMemberById(Guid id, Guid OrgId, bool Subscription);

        Task<bool> UpdateFamilyMemberList(List<FamilyMember> FamilyMemberList, Guid OrgId, bool Subscription);

        Task AddFamilyMember(List<TText> familyMemberList, Guid OrgId, bool Subscription);

        Task UpdateFamilyMember(FamilyMember familyMember, Guid OrgId, bool Subscription);

    }
}
