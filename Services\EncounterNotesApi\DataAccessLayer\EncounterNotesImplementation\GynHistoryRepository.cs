﻿using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Interfaces.ShardManagement;
using Microsoft.Extensions.Logging;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class GynHistoryRepository : ShardGenericRepository<GynHistory>, IGynHistoryRepository<GynHistory>
    {
        private readonly ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> _shardMapManagerService;
        private readonly ILogger<RecordDatabaseContext> _logger;
        private readonly string _shardMapName;

        public GynHistoryRepository(RecordDatabaseContext context,
                                    ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> shardMapManagerService,
                                    IStringLocalizer<EncounterDataAccessLayer> localizer,
                                    ILogger<RecordDatabaseContext> logger)
            : base(context, shardMapManagerService, localizer, logger)
        {
            _shardMapManagerService = shardMapManagerService;
            _logger = logger;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }

        public async Task AddAsync(IEnumerable<GynHistory> entities, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            await context.GynHistory.AddRangeAsync(entities);
            await context.SaveChangesAsync();
        }

        public async Task<GynHistory> GetByIdAsync(Guid id, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return null;
            return await context.GynHistory.FirstOrDefaultAsync(gyn => gyn.gynId == id && !gyn.IsDeleted);
        }

        public async Task<IEnumerable<GynHistory>> GetByPatientIdAsync(Guid patientId, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return Enumerable.Empty<GynHistory>();
            return await context.GynHistory.Where(gyn => gyn.PatientId == patientId && !gyn.IsDeleted).ToListAsync();
        }

        public async Task UpdateAsync(GynHistory entity, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            context.GynHistory.Update(entity);
            await context.SaveChangesAsync();
        }

        public async Task DeleteByEntityAsync(GynHistory entity, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            entity.IsDeleted = true;
            context.GynHistory.Update(entity);
            await context.SaveChangesAsync();
        }

        public async Task DeleteByIdAsync(Guid gynId, Guid OrgId, bool Subscription)
        {
            var entity = await GetByIdAsync(gynId, OrgId, Subscription);
            if (entity != null)
            {
                entity.IsDeleted = true;
                await UpdateAsync(entity, OrgId, Subscription);
            }
        }

        public async Task UpdateGynHistoryListAsync(List<GynHistory> gynHistoryList, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;

            if (gynHistoryList == null || gynHistoryList.Count == 0)
                throw new ArgumentException("Invalid records provided.", nameof(gynHistoryList));

            foreach (var gynHistory in gynHistoryList)
            {
                var existingGynHistory = await context.GynHistory
                    .FirstOrDefaultAsync(gyn => gyn.gynId == gynHistory.gynId);

                if (existingGynHistory != null)
                {
                    existingGynHistory.Notes = gynHistory.Notes;
                    existingGynHistory.Symptoms = gynHistory.Symptoms;
                    existingGynHistory.OrganizationId = gynHistory.OrganizationId;
                    existingGynHistory.PcpId = gynHistory.PcpId;
                    existingGynHistory.DateOfHistory = gynHistory.DateOfHistory;
                    existingGynHistory.IsDeleted = gynHistory.IsDeleted;
                    context.GynHistory.Update(existingGynHistory);
                }
            }
            await context.SaveChangesAsync();
        }
    }
}
