using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class ProcedureQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IProcedureRepository> _procedureRepositoryMock;
        private ProcedureQueryHandler _procedureQueryHandler;
        private Guid _orgId;
        private bool _subscription;
        private Guid _patientId;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _procedureRepositoryMock = new Mock<IProcedureRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;
            _patientId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.ProcedureRepository).Returns(_procedureRepositoryMock.Object);
            _procedureQueryHandler = new ProcedureQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetProcedureByPatientId_ShouldReturnAllProceduresForPatient()
        {
            // Arrange
            var procedures = new List<Procedure>
            {
                new Procedure
                {
                    Id = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    CreatedByUserId = Guid.NewGuid(),
                    UpdatedByUserId = Guid.NewGuid(),
                    CPTCode = "99213",
                    Description = "Office or other outpatient visit",
                    Notes = "Routine follow-up",
                    OrderDate = DateTime.Now.AddDays(-30),
                    LastUpdatedDate = DateTime.Now.AddDays(-25),
                    OrderedBy = "Dr. Smith",
                    AssessmentData = "Hypertension, well-controlled",
                    AssessmentId = Guid.NewGuid(),
                    IsDeleted = true, // Note: In the handler, it filters for IsDeleted = !false (which is true)
                    Subscription = _subscription
                },
                new Procedure
                {
                    Id = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    CreatedByUserId = Guid.NewGuid(),
                    UpdatedByUserId = Guid.NewGuid(),
                    CPTCode = "99214",
                    Description = "Office or other outpatient visit, moderate complexity",
                    Notes = "Evaluation of new symptoms",
                    OrderDate = DateTime.Now.AddDays(-60),
                    LastUpdatedDate = DateTime.Now.AddDays(-55),
                    OrderedBy = "Dr. Johnson",
                    AssessmentData = "Diabetes mellitus type 2",
                    AssessmentId = Guid.NewGuid(),
                    IsDeleted = false, // This should be filtered out
                    Subscription = _subscription
                },
                new Procedure
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    CreatedByUserId = Guid.NewGuid(),
                    UpdatedByUserId = Guid.NewGuid(),
                    CPTCode = "99215",
                    Description = "Office or other outpatient visit, high complexity",
                    Notes = "Comprehensive evaluation",
                    OrderDate = DateTime.Now.AddDays(-45),
                    LastUpdatedDate = DateTime.Now.AddDays(-40),
                    OrderedBy = "Dr. Brown",
                    AssessmentData = "Congestive heart failure",
                    AssessmentId = Guid.NewGuid(),
                    IsDeleted = true,
                    Subscription = _subscription
                }
            };

            _procedureRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(procedures);

            // Act
            var result = await _procedureQueryHandler.GetProcedureByPatientId(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1); // Only one procedure has PatientId = _patientId AND IsDeleted = true
            result.All(p => p.PatientId == _patientId && p.IsDeleted == true).Should().BeTrue();
        }

        [Test]
        public async Task GetProcedureByPatientId_WithNoProcedures_ShouldReturnEmptyCollection()
        {
            // Arrange
            var procedures = new List<Procedure>();

            _procedureRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(procedures);

            // Act
            var result = await _procedureQueryHandler.GetProcedureByPatientId(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetProcedureByPatientId_WithNoMatchingProcedures_ShouldReturnEmptyCollection()
        {
            // Arrange
            var procedures = new List<Procedure>
            {
                new Procedure
                {
                    Id = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    CPTCode = "99214",
                    Description = "Office or other outpatient visit, moderate complexity",
                    IsDeleted = false, // This should be filtered out because IsDeleted = false
                    Subscription = _subscription
                },
                new Procedure
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    CPTCode = "99215",
                    Description = "Office or other outpatient visit, high complexity",
                    IsDeleted = true, // This should be filtered out because PatientId doesn't match
                    Subscription = _subscription
                }
            };

            _procedureRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(procedures);

            // Act
            var result = await _procedureQueryHandler.GetProcedureByPatientId(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
