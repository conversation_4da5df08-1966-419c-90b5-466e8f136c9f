﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface ISocialHistoryQueryHandler<TText>
    {
        Task<List<PatientSocialHistory>> GetHistoryByIdAndIsActive(Guid patientId, Guid orgId, bool isSubscription);
        Task<List<PatientSocialHistory>> GetAllByPatientId(Guid patientId, Guid orgId, bool isSubscription);
    }
}
