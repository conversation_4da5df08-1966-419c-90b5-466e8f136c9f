﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface IDiagnosticImagingPageCommandHandler<TText>
    {
        Task DeleteDiagnosticImagingById(Guid id, Guid OrgId, bool Subscription);

        Task<bool> UpdateDiagnosticImagingList(List<DiagnosticImagingDTO> diagnosticImagingList, Guid OrgId, bool Subscription);

        Task AddDiagnosticImaging(List<TText> diagnosticImagingList, Guid OrgId, bool Subscription);

        Task UpdateDiagnosticImaging(Diagnostic<PERSON>magingDTO diagnosticImaging, Guid OrgId, bool Subscription);

    }
}
