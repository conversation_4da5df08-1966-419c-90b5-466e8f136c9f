using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class TherapeuticInterventionsControllerTests
    {
        private Mock<ITherapeuticInterventionsCommandHandler<TherapeuticInterventionsData>> _therapeuticInterventionsCommandHandlerMock;
        private Mock<ITherapeuticInterventionsQueryHandler<TherapeuticInterventionsData>> _therapeuticInterventionsQueryHandlerMock;
        private Mock<ILogger<TherapeuticInterventionsController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private TherapeuticInterventionsController _controller;
        private List<TherapeuticInterventionsData> _testTherapeuticInterventions;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _therapeuticInterventionsCommandHandlerMock = new Mock<ITherapeuticInterventionsCommandHandler<TherapeuticInterventionsData>>();
            _therapeuticInterventionsQueryHandlerMock = new Mock<ITherapeuticInterventionsQueryHandler<TherapeuticInterventionsData>>();
            _loggerMock = new Mock<ILogger<TherapeuticInterventionsController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error fetching therapeutic interventions."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No therapeutic interventions provided."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Therapeutic interventions added successfully."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error adding therapeutic interventions."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid therapeutic intervention record."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Therapeutic intervention deleted successfully."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting therapeutic intervention."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Therapeutic intervention updated successfully."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating therapeutic intervention."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added successfully."));

            _testTherapeuticInterventions = new List<TherapeuticInterventionsData>
            {
                new TherapeuticInterventionsData {
                    TherapeuticInterventionsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    PCPId = Guid.NewGuid(),
                    TherapyType = "Physical Therapy",
                    Notes = "Twice weekly sessions for 6 weeks",
                    IsActive = true,
                    Subscription = _isSubscription
                },
                new TherapeuticInterventionsData {
                    TherapeuticInterventionsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-15),
                    PCPId = Guid.NewGuid(),
                    TherapyType = "Occupational Therapy",
                    Notes = "Weekly sessions for 8 weeks",
                    IsActive = true,
                    Subscription = _isSubscription
                }
            };

            _controller = new TherapeuticInterventionsController(
                _therapeuticInterventionsCommandHandlerMock.Object,
                _therapeuticInterventionsQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetAllById_ReturnsTherapeuticInterventions_WhenInterventionsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientInterventions = _testTherapeuticInterventions.Select(i => { i.PatientId = patientId; return i; }).ToList();
            _therapeuticInterventionsQueryHandlerMock.Setup(x => x.GetAllTherapeuticInterventionsById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientInterventions);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedInterventions = okResult.Value as IEnumerable<TherapeuticInterventionsData>;
            returnedInterventions.Should().NotBeNull();
            returnedInterventions.Should().BeEquivalentTo(patientInterventions);
        }

        [Test]
        public async Task GetAllById_ReturnsNotFound_WhenNoInterventionsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _therapeuticInterventionsQueryHandlerMock.Setup(x => x.GetAllTherapeuticInterventionsById(patientId, _orgId, _isSubscription))
                .ReturnsAsync((IEnumerable<TherapeuticInterventionsData>)null);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task GetAllByIdAndIsActive_ReturnsActiveInterventions_WhenInterventionsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activeInterventions = _testTherapeuticInterventions.Select(i => { i.PatientId = patientId; i.IsActive = true; return i; }).ToList();
            _therapeuticInterventionsQueryHandlerMock.Setup(x => x.GetTherapeuticInterventionsByIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activeInterventions);

            // Act
            var result = await _controller.GetAllByIdAndIsActive(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedInterventions = okResult.Value as IEnumerable<TherapeuticInterventionsData>;
            returnedInterventions.Should().NotBeNull();
            returnedInterventions.Should().BeEquivalentTo(activeInterventions);
        }

        [Test]
        public async Task AddTherapeuticInterventions_ValidInterventions_ReturnsOk()
        {
            // Arrange
            var interventionsToAdd = _testTherapeuticInterventions;

            // Act
            var result = await _controller.AddTherapeuticInterventions(interventionsToAdd, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _therapeuticInterventionsCommandHandlerMock.Verify(x => x.AddTherapeuticInterventions(interventionsToAdd, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task AddTherapeuticInterventions_EmptyInterventions_ReturnsBadRequest()
        {
            // Arrange
            var emptyInterventions = new List<TherapeuticInterventionsData>();

            // Act
            var result = await _controller.AddTherapeuticInterventions(emptyInterventions, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);

            // Verify the command handler was not called
            _therapeuticInterventionsCommandHandlerMock.Verify(x => x.AddTherapeuticInterventions(It.IsAny<List<TherapeuticInterventionsData>>(), _orgId, _isSubscription), Times.Never);
        }

        [Test]
        public async Task DeleteByEntity_ValidIntervention_ReturnsOk()
        {
            // Arrange
            var interventionToDelete = _testTherapeuticInterventions[0];

            // Act
            var result = await _controller.DeleteByEntity(interventionToDelete, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _therapeuticInterventionsCommandHandlerMock.Verify(x => x.DeleteTherapeuticInterventionsByEntity(interventionToDelete, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateTherapeuticInterventionsById_ValidIntervention_ReturnsOk()
        {
            // Arrange
            var interventionToUpdate = _testTherapeuticInterventions[0];
            var patientId = interventionToUpdate.PatientId.Value;

            // Act
            var result = await _controller.UpdateTherapeuticInterventionsById(patientId, interventionToUpdate, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _therapeuticInterventionsCommandHandlerMock.Verify(x => x.UpdateTherapeuticInterventions(interventionToUpdate, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateTherapeuticInterventionsList_ValidInterventions_ReturnsOk()
        {
            // Arrange
            var interventionsToUpdate = _testTherapeuticInterventions;

            // Act
            var result = await _controller.UpdateTherapeuticInterventionsList(interventionsToUpdate, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _therapeuticInterventionsCommandHandlerMock.Verify(x => x.UpdateTherapeuticInterventionsList(interventionsToUpdate, _orgId, _isSubscription), Times.Once);
        }


    }
}
