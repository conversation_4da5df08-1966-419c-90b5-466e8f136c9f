﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IDiagnosticImagingQueryHandler<T>
    {
        Task<IEnumerable<DiagnosticImage>> GetDiagnosticImagingByIdAndIsActive(Guid id, Guid OrgId, bool Subscription);
        Task<List<T>> GetDiagnosticImagingById(Guid id, Guid OrgId, bool Subscription);
    }
}
