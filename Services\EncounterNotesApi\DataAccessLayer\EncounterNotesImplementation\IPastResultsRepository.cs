﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IPastResultsRepository : IShardGenericRepository<PastResults>
    {
        Task<IEnumerable<PastResults>> GetAllResultsAsync( Guid OrgId, bool Subscription);
    }
}