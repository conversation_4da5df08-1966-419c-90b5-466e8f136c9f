﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IFamilyMemberRepository : IShardGenericRepository<FamilyMember>
    {
        Task<List<FamilyMember>> GetFamilyMembersById(Guid recordId, Guid OrgId, bool Subscription);
    }
}
