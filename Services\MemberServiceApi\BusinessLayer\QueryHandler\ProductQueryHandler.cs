﻿using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class ProductQueryHandler : IProductQueryHandler<ProductDTO>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public ProductQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<ProductDTO>> GetProduct()
        {
            return await _unitOfWork.ProductRepository.GetAllAsync();
        }

        public async Task<ProductDTO> GetProductById(Guid id, Guid orgId, bool Subscription)
        {
            return await _unitOfWork.ProductRepository.GetByIdAsync(id, orgId, Subscription);
        }
    }
}