﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface IPhysicalExaminationCommandHandler<TText>
    {
        Task DeleteExaminationByEntity(PhysicalExamination physical, Guid OrgId, bool Subscription);
        Task DeleteExaminationById(Guid id, Guid OrgId, bool Subscription);
        Task AddExamination(List<TText> texts, Guid OrgId, bool Subscription);
        Task UpdateExamination(PhysicalExamination Phyexamination, Guid OrgId, bool Subscription);
        Task UpdateExaminationList(List<PhysicalExamination> physicalExamination, Guid OrgId, bool Subscription);
    }
}
