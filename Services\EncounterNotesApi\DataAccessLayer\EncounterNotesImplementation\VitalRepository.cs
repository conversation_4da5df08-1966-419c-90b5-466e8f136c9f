﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Interfaces.ShardManagement;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class VitalRepository : ShardGenericRepository<Vitals>, IVitalRepository
    {
        private readonly RecordDatabaseContext _context;
        private readonly IStringLocalizer<EncounterDataAccessLayer> _localizer;
        private readonly ILogger<RecordDatabaseContext> _logger;
        private readonly ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> _shardMapManagerService;

        public VitalRepository(
            RecordDatabaseContext context,
            ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> shardMapManagerService,
            IStringLocalizer<EncounterDataAccessLayer> localizer,
            ILogger<RecordDatabaseContext> logger)
            : base(context, shardMapManagerService, localizer, logger)
        {
            _context = context;
            _shardMapManagerService = shardMapManagerService;
            _localizer = localizer;
            _logger = logger;
        }
    }
}
