using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using Interfaces.ShardManagement;
using ShardModels;

namespace EncounterNotesApiBusinessLayerTestCases.CommandHandlers
{
    [TestFixture]
    public class ChiefComplaintCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<ChiefComplaintCommandHandler>> _loggerMock;
        private Mock<IMigration<RecordDatabaseContext, ChiefComplaint, EncounterDataAccessLayer>> _migrationMock;
        private Mock<IChiefComplaintRepository<ChiefComplaint>> _chiefComplaintRepositoryMock;
        private ChiefComplaintCommandHandler _chiefComplaintCommandHandler;
        private Guid _orgId;
        private bool _subscription;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<ChiefComplaintCommandHandler>>();
            _migrationMock = new Mock<IMigration<RecordDatabaseContext, ChiefComplaint, EncounterDataAccessLayer>>();
            _chiefComplaintRepositoryMock = new Mock<IChiefComplaintRepository<ChiefComplaint>>();

            _unitOfWorkMock.Setup(u => u.ChiefComplaintRepository).Returns(_chiefComplaintRepositoryMock.Object);

            _chiefComplaintCommandHandler = new ChiefComplaintCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _migrationMock.Object
            );

            _orgId = Guid.NewGuid();
            _subscription = false;
        }

        [Test]
        public async Task AddChiefComplaint_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var complaints = new List<ChiefComplaint>
            {
                new ChiefComplaint
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "Headache",
                    DateOfComplaint = DateTime.Now.AddDays(-1),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false
                }
            };

            _chiefComplaintRepositoryMock.Setup(r => r.AddAsync(complaints, _orgId, _subscription))
                .Returns(Task.CompletedTask);
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _chiefComplaintCommandHandler.AddChiefComplaint(complaints, _orgId, _subscription);

            // Assert
            _chiefComplaintRepositoryMock.Verify(r => r.AddAsync(complaints, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateChiefComplaint_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var complaint = new ChiefComplaint
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Description = "Headache",
                DateOfComplaint = DateTime.Now.AddDays(-1),
                OrganizationId = _orgId,
                PcpId = Guid.NewGuid(),
                IsDeleted = false
            };

            _chiefComplaintRepositoryMock.Setup(r => r.UpdateAsync(complaint, _orgId, _subscription))
                .Returns(Task.CompletedTask);
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _chiefComplaintCommandHandler.UpdateChiefComplaint(complaint, _orgId, _subscription);

            // Assert
            _chiefComplaintRepositoryMock.Verify(r => r.UpdateAsync(complaint, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteChiefComplaintById_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var complaintId = Guid.NewGuid();

            _chiefComplaintRepositoryMock.Setup(r => r.DeleteByIdAsync(complaintId, _orgId, _subscription))
                .Returns(Task.CompletedTask);
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _chiefComplaintCommandHandler.DeleteChiefComplaintById(complaintId, _orgId, _subscription);

            // Assert
            _chiefComplaintRepositoryMock.Verify(r => r.DeleteByIdAsync(complaintId, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteChiefComplaintByEntity_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var complaint = new ChiefComplaint
            {
                Id = Guid.NewGuid(),
                PatientId = Guid.NewGuid(),
                Description = "Headache",
                DateOfComplaint = DateTime.Now.AddDays(-1),
                OrganizationId = _orgId,
                PcpId = Guid.NewGuid(),
                IsDeleted = false
            };

            _chiefComplaintRepositoryMock.Setup(r => r.DeleteByEntityAsync(complaint, _orgId, _subscription))
                .Returns(Task.CompletedTask);
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _chiefComplaintCommandHandler.DeleteChiefComplaintByEntity(complaint, _orgId, _subscription);

            // Assert
            _chiefComplaintRepositoryMock.Verify(r => r.DeleteByEntityAsync(complaint, _orgId, _subscription), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateChiefComplaintListAsync_ShouldCallRepositoryForEachComplaintAndSave()
        {
            // Arrange
            var complaints = new List<ChiefComplaint>
            {
                new ChiefComplaint
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "Headache",
                    DateOfComplaint = DateTime.Now.AddDays(-1),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false
                },
                new ChiefComplaint
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Description = "Fever",
                    DateOfComplaint = DateTime.Now.AddDays(-2),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    IsDeleted = false
                }
            };

            _chiefComplaintRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<ChiefComplaint>(), _orgId, _subscription))
                .Returns(Task.CompletedTask);
            _unitOfWorkMock.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _chiefComplaintCommandHandler.UpdateChiefComplaintListAsync(complaints, _orgId, _subscription);

            // Assert
            _chiefComplaintRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<ChiefComplaint>(), _orgId, _subscription), Times.Exactly(2));
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }




    }
}
