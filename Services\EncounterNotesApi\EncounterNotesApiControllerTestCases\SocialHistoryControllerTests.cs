﻿﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using NUnit.Framework;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class SocialHistoryControllerTests
    {
        private SocialHistoryController _controller;
        private Mock<ISocialHistoryCommandHandler<PatientSocialHistory>> _socialHistoryCommandHandlerMock;
        private Mock<ISocialHistoryQueryHandler<PatientSocialHistory>> _socialHistoryQueryHandlerMock;
        private Mock<ILogger<SocialHistoryController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private List<PatientSocialHistory> _testSocialHistories;
        private readonly Guid _orgId = Guid.NewGuid();
        private readonly bool _isSubscription = false;

        [SetUp]
        public void Setup()
        {
            _socialHistoryCommandHandlerMock = new Mock<ISocialHistoryCommandHandler<PatientSocialHistory>>();
            _socialHistoryQueryHandlerMock = new Mock<ISocialHistoryQueryHandler<PatientSocialHistory>>();
            _loggerMock = new Mock<ILogger<SocialHistoryController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();

            // Setup localizer mock
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving social history."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Social history registered successfully."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error adding social history."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Social history updated successfully."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating social history."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));

            _testSocialHistories = new List<PatientSocialHistory>
            {
                new PatientSocialHistory {
                    SocialHistoryId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Occupation = "Engineer",
                    LivingSituation = "Lives with spouse",
                    MaritalStatus = "Married",
                    LifestyleHabits = "Non-smoker, occasional alcohol",
                    Educationlevel = "Bachelor's degree",
                    isActive = true,
                    Subscription = _isSubscription
                },
                new PatientSocialHistory {
                    SocialHistoryId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    UpdatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-15),
                    Occupation = "Teacher",
                    LivingSituation = "Lives alone",
                    MaritalStatus = "Single",
                    LifestyleHabits = "Non-smoker, no alcohol",
                    Educationlevel = "Master's degree",
                    isActive = true,
                    Subscription = _isSubscription
                }
            };

            _controller = new SocialHistoryController(
                _socialHistoryCommandHandlerMock.Object,
                _socialHistoryQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetAllByPatientId_ReturnsSocialHistories_WhenHistoriesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientHistories = _testSocialHistories.Select(h => { h.PatientId = patientId; return h; }).ToList();
            _socialHistoryQueryHandlerMock.Setup(x => x.GetAllByPatientId(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientHistories);

            // Act
            var result = await _controller.GetAllByPatientId(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedHistories = okResult.Value as List<PatientSocialHistory>;
            returnedHistories.Should().NotBeNull();
            returnedHistories.Should().BeEquivalentTo(patientHistories);
        }

        [Test]
        public async Task GetAllByPatientId_ReturnsEmptyList_WhenNoHistoriesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _socialHistoryQueryHandlerMock.Setup(x => x.GetAllByPatientId(patientId, _orgId, _isSubscription))
                .ReturnsAsync(new List<PatientSocialHistory>());

            // Act
            var result = await _controller.GetAllByPatientId(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedHistories = okResult.Value as List<PatientSocialHistory>;
            returnedHistories.Should().NotBeNull();
            returnedHistories.Should().BeEmpty();
        }

        [Test]
        public async Task GetbypatientidandisActive_ReturnsSocialHistories_WhenActiveHistoriesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activeHistories = _testSocialHistories.Select(h => { h.PatientId = patientId; h.isActive = true; return h; }).ToList();
            _socialHistoryQueryHandlerMock.Setup(x => x.GetHistoryByIdAndIsActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activeHistories);

            // Act
            var result = await _controller.GetbypatientidandisActive(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedHistories = okResult.Value as List<PatientSocialHistory>;
            returnedHistories.Should().NotBeNull();
            returnedHistories.Should().BeEquivalentTo(activeHistories);
        }

        [Test]
        public async Task Addentity_ValidHistories_ReturnsOk()
        {
            // Arrange
            var historiesToAdd = _testSocialHistories;

            // Act
            var result = await _controller.Addentity(historiesToAdd, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _socialHistoryCommandHandlerMock.Verify(x => x.AddHistory(historiesToAdd, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task Addentity_EmptyHistories_ReturnsOk()
        {
            // Arrange
            var emptyHistories = new List<PatientSocialHistory>();

            // Act
            var result = await _controller.Addentity(emptyHistories, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["InvalidRecord"]);

            // Verify the command handler was not called
            _socialHistoryCommandHandlerMock.Verify(x => x.AddHistory(It.IsAny<List<PatientSocialHistory>>(), _orgId, _isSubscription), Times.Never);
        }

        [Test]
        public async Task Updateentity_ValidHistories_ReturnsOk()
        {
            // Arrange
            var historiesToUpdate = _testSocialHistories;

            // Act
            var result = await _controller.Updateentity(historiesToUpdate, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _socialHistoryCommandHandlerMock.Verify(x => x.UpdateHistoryList(historiesToUpdate, _orgId, _isSubscription), Times.Once);
        }


    }
}
