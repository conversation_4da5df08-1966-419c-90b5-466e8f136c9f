using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class AssessmentsControllerTests
    {
        private Mock<IAssessmentsCommandHandler<AssessmentsData>> _assessmentsCommandHandlerMock;
        private Mock<IAssessmentsQueryHandler<AssessmentsData>> _assessmentsQueryHandlerMock;
        private Mock<ILogger<AssessmentsController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private AssessmentsController _controller;
        private List<AssessmentsData> _testAssessments;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _assessmentsCommandHandlerMock = new Mock<IAssessmentsCommandHandler<AssessmentsData>>();
            _assessmentsQueryHandlerMock = new Mock<IAssessmentsQueryHandler<AssessmentsData>>();
            _loggerMock = new Mock<ILogger<AssessmentsController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["NoLicense"]).Returns(new LocalizedString("NoLicense", "No license available."));
            _localizerMock.Setup(x => x["DatabaseError"]).Returns(new LocalizedString("DatabaseError", "Database error occurred."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error posting data."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error deleting data."));
            _localizerMock.Setup(x => x["DeleteSuccessful"]).Returns(new LocalizedString("DeleteSuccessful", "Delete successful."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testAssessments = new List<AssessmentsData>
            {
                new AssessmentsData {
                    AssessmentsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Diagnosis = "Hypertension",
                    PCPId = Guid.NewGuid(),
                    ICDCode = "I10",
                    Specify = "Essential hypertension",
                    Notes = "Patient has high blood pressure",
                    IsActive = true,
                    CheifComplaint = "Headache",
                    CheifComplaintId = Guid.NewGuid(),
                    Subscription = _isSubscription
                },
                new AssessmentsData {
                    AssessmentsID = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    CreatedDate = DateTime.Now.AddDays(-20),
                    UpdatedDate = DateTime.Now.AddDays(-15),
                    Diagnosis = "Type 2 Diabetes",
                    PCPId = Guid.NewGuid(),
                    ICDCode = "E11",
                    Specify = "Type 2 diabetes mellitus",
                    Notes = "Patient has elevated blood glucose",
                    IsActive = true,
                    CheifComplaint = "Fatigue",
                    CheifComplaintId = Guid.NewGuid(),
                    Subscription = _isSubscription
                }
            };

            _controller = new AssessmentsController(
                _assessmentsCommandHandlerMock.Object,
                _assessmentsQueryHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetAllById_ReturnsAssessments_WhenAssessmentsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientAssessments = _testAssessments.Select(a => { a.PatientId = patientId; return a; }).ToList();
            _assessmentsQueryHandlerMock.Setup(x => x.GetAllAssessmentsById(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientAssessments);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedAssessments = okResult.Value as IEnumerable<AssessmentsData>;
            returnedAssessments.Should().NotBeNull();
            returnedAssessments.Should().BeEquivalentTo(patientAssessments);
        }

        [Test]
        public async Task GetAllById_ReturnsNotFound_WhenNoAssessmentsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _assessmentsQueryHandlerMock.Setup(x => x.GetAllAssessmentsById(patientId, _orgId, _isSubscription))
                .ReturnsAsync((IEnumerable<AssessmentsData>)null);

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task GetAllById_ExceptionThrown_ReturnsStatusCode500()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _assessmentsQueryHandlerMock.Setup(x => x.GetAllAssessmentsById(patientId, _orgId, _isSubscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllById(patientId, _orgId, _isSubscription);

            // Assert
            var statusCodeResult = result.Result as ObjectResult;
            statusCodeResult.Should().NotBeNull();
            statusCodeResult.StatusCode.Should().Be(500);
            statusCodeResult.Value.Should().Be(_localizerMock.Object["GetLogError"]);
        }

        [Test]
        public async Task AddAssessments_ValidAssessments_ReturnsOk()
        {
            // Arrange
            var assessments = _testAssessments;

            // Act
            var result = await _controller.AddAssessments(assessments, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _assessmentsCommandHandlerMock.Verify(x => x.AddAssessments(assessments, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task AddAssessments_EmptyList_ReturnsBadRequest()
        {
            // Arrange
            var emptyAssessments = new List<AssessmentsData>();

            // Act
            var result = await _controller.AddAssessments(emptyAssessments, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["NoLicense"]);
        }

        [Test]
        public async Task UpdateAssessmentsById_ValidAssessment_ReturnsOk()
        {
            // Arrange
            var assessment = _testAssessments[0];

            // Act
            var result = await _controller.UpdateAssessmentsById(assessment.PatientId, assessment, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _assessmentsCommandHandlerMock.Verify(x => x.UpdateAssessments(assessment, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task DeleteByEntity_ValidAssessment_ReturnsOk()
        {
            // Arrange
            var assessment = _testAssessments[0];

            // Act
            var result = await _controller.DeleteByEntity(assessment, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["DeleteSuccessful"]);

            // Verify the command handler was called
            _assessmentsCommandHandlerMock.Verify(x => x.DeleteAssessmentsByEntity(assessment, _orgId, _isSubscription), Times.Once);
        }
    }
}
