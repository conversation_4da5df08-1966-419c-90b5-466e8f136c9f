﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class PastResultsQueryHandler : IPastResultsQueryHandler<PastResults>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public PastResultsQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<PastResults>> GetAllResultsById(Guid id, Guid OrgId, bool Subscription)
        {
            var pastResults = await _unitOfWork.PastResultsRepository.GetAllResultsAsync(OrgId, Subscription);
            return pastResults.Where(PastRes => PastRes.PatientId == id);
        }

        public async Task<IEnumerable<PastResults>> GetResultByIdAndIsActive(Guid id, Guid OrgId, bool Subscription)
        {
            var pastResults = await _unitOfWork.PastResultsRepository.GetAsync(OrgId, Subscription);
            return pastResults.Where(PastRes => PastRes.PatientId == id && PastRes.IsActive == true);
        }
    }
}
