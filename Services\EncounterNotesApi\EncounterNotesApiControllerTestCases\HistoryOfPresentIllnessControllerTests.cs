using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer;
using EncounterNotesService.Controllers;
using Microsoft.AspNetCore.Mvc;
using EncounterNotesService.EncounterNotesServiceResources;
using ShardModels;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class HistoryOfPresentIllnessControllerTests
    {
        private Mock<IHistoryOfPresentIllnessCommandHandler<HistoryOfPresentIllness>> _hpiCommandHandlerMock;
        private Mock<IHistoryOfPresentIllnessQueryHandler<HistoryOfPresentIllness>> _hpiQueryHandlerMock;
        private Mock<ILogger<HistoryOfPresentIllnessController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private HistoryOfPresentIllnessController _controller;
        private List<HistoryOfPresentIllness> _testHPIs;
        private Guid _orgId;
        private bool _isSubscription;

        [SetUp]
        public void Setup()
        {
            _hpiCommandHandlerMock = new Mock<IHistoryOfPresentIllnessCommandHandler<HistoryOfPresentIllness>>();
            _hpiQueryHandlerMock = new Mock<IHistoryOfPresentIllnessQueryHandler<HistoryOfPresentIllness>>();
            _loggerMock = new Mock<ILogger<HistoryOfPresentIllnessController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();
            _orgId = Guid.NewGuid();
            _isSubscription = true;

            // Mock localizer strings
            _localizerMock.Setup(x => x["RecordNotFound"]).Returns(new LocalizedString("RecordNotFound", "Record not found."));
            _localizerMock.Setup(x => x["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error retrieving data."));
            _localizerMock.Setup(x => x["InvalidData"]).Returns(new LocalizedString("InvalidData", "Invalid data."));
            _localizerMock.Setup(x => x["InvalidRecord"]).Returns(new LocalizedString("InvalidRecord", "Invalid record."));
            _localizerMock.Setup(x => x["InvalidRecords"]).Returns(new LocalizedString("InvalidRecords", "Invalid records."));
            _localizerMock.Setup(x => x["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error posting data."));
            _localizerMock.Setup(x => x["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error updating data."));
            _localizerMock.Setup(x => x["BulkUpdateLogError"]).Returns(new LocalizedString("BulkUpdateLogError", "Error updating data in bulk."));
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));
            _localizerMock.Setup(x => x["BulkUpdateSuccessful"]).Returns(new LocalizedString("BulkUpdateSuccessful", "Bulk update successful."));
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testHPIs = new List<HistoryOfPresentIllness>
            {
                new HistoryOfPresentIllness {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Location = "Left knee",
                    Duration = "2 weeks",
                    Severity = "Moderate",
                    Symptoms = "Pain, swelling",
                    StartDate = DateTime.Now.AddDays(-14),
                    EndDate = null,
                    Notes = "Pain worsens with activity",
                    IsActive = true,
                    Subscription = _isSubscription
                },
                new HistoryOfPresentIllness {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Location = "Lower back",
                    Duration = "3 days",
                    Severity = "Severe",
                    Symptoms = "Sharp pain, limited mobility",
                    StartDate = DateTime.Now.AddDays(-3),
                    EndDate = null,
                    Notes = "Pain radiates down left leg",
                    IsActive = true,
                    Subscription = _isSubscription
                }
            };

            _controller = new HistoryOfPresentIllnessController(
                _hpiQueryHandlerMock.Object,
                _hpiCommandHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetByPatientId_ReturnsHPIs_WhenHPIsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientHPIs = _testHPIs.Select(h => { h.PatientId = patientId; return h; }).ToList();
            _hpiQueryHandlerMock.Setup(x => x.GetHistoryOfPresentIllnessByPatientId(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientHPIs);

            // Act
            var result = await _controller.GetByPatientId(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedHPIs = okResult.Value as IEnumerable<HistoryOfPresentIllness>;
            returnedHPIs.Should().NotBeNull();
            returnedHPIs.Should().BeEquivalentTo(patientHPIs);
        }

        [Test]
        public async Task GetByPatientId_ReturnsNotFound_WhenNoHPIsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _hpiQueryHandlerMock.Setup(x => x.GetHistoryOfPresentIllnessByPatientId(patientId, _orgId, _isSubscription))
                .ReturnsAsync((IEnumerable<HistoryOfPresentIllness>)null);

            // Act
            var result = await _controller.GetByPatientId(patientId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result.Result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["RecordNotFound"]);
        }

        [Test]
        public async Task GetByPatientIdAndActive_ReturnsHPIs_WhenActiveHPIsExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var activeHPIs = _testHPIs.Select(h => { h.PatientId = patientId; h.IsActive = true; return h; }).ToList();
            _hpiQueryHandlerMock.Setup(x => x.GetHistoryOfPresentIllnessByPatientIdAndActive(patientId, _orgId, _isSubscription))
                .ReturnsAsync(activeHPIs);

            // Act
            var result = await _controller.GetByPatientIdAndActive(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result.Result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedHPIs = okResult.Value as IEnumerable<HistoryOfPresentIllness>;
            returnedHPIs.Should().NotBeNull();
            returnedHPIs.Should().BeEquivalentTo(activeHPIs);
        }

        [Test]
        public async Task CreateHistoryOfPresentIllness_ValidHPIs_ReturnsOk()
        {
            // Arrange
            var hpis = _testHPIs;

            // Act
            var result = await _controller.CreateHistoryOfPresentIllness(hpis, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _hpiCommandHandlerMock.Verify(x => x.AddHistoryOfPresentIllness(hpis, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task CreateHistoryOfPresentIllness_EmptyList_ReturnsOk()
        {
            // Arrange
            var emptyHPIs = new List<HistoryOfPresentIllness>();

            // Mock the localizer string
            _localizerMock.Setup(x => x["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));


            // Act
            var result = await _controller.CreateHistoryOfPresentIllness(emptyHPIs, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["SuccessfulRegistration"]);

            // Verify the command handler was called
            _hpiCommandHandlerMock.Verify(x => x.AddHistoryOfPresentIllness(emptyHPIs, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateHistoryOfPresentIllness_ValidHPI_ReturnsOk()
        {
            // Arrange
            var hpi = _testHPIs[0];

            // Act
            var result = await _controller.UpdateHistoryOfPresentIllness(hpi.Id, hpi, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _hpiCommandHandlerMock.Verify(x => x.UpdateHistoryOfPresentIllness(hpi, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateHistoryOfPresentIllness_InvalidId_ReturnsOk()
        {
            // Arrange
            var hpi = _testHPIs[0];
            var differentId = Guid.NewGuid();

            // Mock the localizer string
            _localizerMock.Setup(x => x["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));


            // Act
            var result = await _controller.UpdateHistoryOfPresentIllness(differentId, hpi, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["UpdateSuccessful"]);

            // Verify the command handler was called
            _hpiCommandHandlerMock.Verify(x => x.UpdateHistoryOfPresentIllness(hpi, _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateHistoryOfPresentIllnessBulk_ValidHPIs_ReturnsOk()
        {
            // Arrange
            var hpis = _testHPIs;

            // Act
            var result = await _controller.UpdateHistoryOfPresentIllnessBulk(hpis, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);
            okResult.Value.Should().Be(_localizerMock.Object["BulkUpdateSuccessful"]);

            // Verify the command handler was called
            _hpiCommandHandlerMock.Verify(x => x.UpdateHistoryOfPresentIllnessBulk(hpis, _orgId, _isSubscription), Times.Once);
        }


    }
}
