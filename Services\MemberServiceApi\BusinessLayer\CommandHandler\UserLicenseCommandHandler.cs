﻿using System;
using System.Threading.Tasks;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class UserLicenseCommandHandler : IUserLicenseCommandHandler<UserLicense>
    {
        private readonly IUnitOfWork _unitOfWork;
        private const int singleUser = 1;

        public UserLicenseCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddUserLicenseAsync(UserLicense license)
        {
            if (license == null)
                throw new ArgumentNullException(nameof(license));

            await _unitOfWork.UserLicenseRepository.AddAsync(license);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateUserLicenseAsync(UserLicense license)
        {
            if (license == null)
                throw new ArgumentNullException(nameof(license));

            await _unitOfWork.UserLicenseRepository.UpdateAsync(license);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteUserLicenseAsync(Guid id)
        {
            await _unitOfWork.UserLicenseRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }

        public async Task IncrementActiveUserCountAsync(Guid organizationId)
        {
            var license = await _unitOfWork.UserLicenseRepository.GetByOrganizationIdAsync(organizationId);
            if (license != null)
            {
                license.ActiveUsers += singleUser;
                await _unitOfWork.UserLicenseRepository.UpdateAsync(license);
                await _unitOfWork.SaveAsync();
            }
        }

        public async Task ResetActiveUsersAsync(Guid organizationId)
        {
            var license = await _unitOfWork.UserLicenseRepository.GetByOrganizationIdAsync(organizationId);
            if (license != null)
            {
                license.ActiveUsers -= singleUser;
                await _unitOfWork.UserLicenseRepository.UpdateAsync(license);
                await _unitOfWork.SaveAsync();
            }
        }

        public async Task SetLicenseStatusAsync(Guid organizationId, bool status)
        {
            var license = await _unitOfWork.UserLicenseRepository.GetByOrganizationIdAsync(organizationId);
            if (license != null)
            {
                license.Status = status;
                await _unitOfWork.UserLicenseRepository.UpdateAsync(license);
                await _unitOfWork.SaveAsync();
            }
        }

        public async Task<bool> CheckLicenseExpiryAsync(Guid organizationId)
        {
            var license = await _unitOfWork.UserLicenseRepository.GetByOrganizationIdAsync(organizationId);
            if (license == null)
            {
                return true;
            }

            return license.ExpiryDate < DateTime.UtcNow;
        }
    }
}