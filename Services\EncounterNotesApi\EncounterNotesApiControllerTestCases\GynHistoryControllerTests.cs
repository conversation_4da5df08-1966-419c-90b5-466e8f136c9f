﻿﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Moq;
using NUnit.Framework;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesCommandHandler;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesService.EncounterNotesServiceResources;
using EncounterNotesApi.Controllers;
using ShardModels;
using System.Text.Json;

namespace EncounterNotesApiControllerTestCases
{
    [TestFixture]
    public class GynHistoryControllerTests
    {
        private GynHistoryController _controller;
        private Mock<IGynHistoryCommandHandler<GynHistory>> _gynHistoryCommandHandlerMock;
        private Mock<IGynHistoryQueryHandler<GynHistory>> _gynHistoryQueryHandlerMock;
        private Mock<ILogger<GynHistoryController>> _loggerMock;
        private Mock<IStringLocalizer<EncounterNotesServiceStrings>> _localizerMock;
        private List<GynHistory> _testGynHistories;
        private readonly Guid _orgId = Guid.NewGuid();
        private readonly bool _isSubscription = false;

        [SetUp]
        public void Setup()
        {
            _gynHistoryCommandHandlerMock = new Mock<IGynHistoryCommandHandler<GynHistory>>();
            _gynHistoryQueryHandlerMock = new Mock<IGynHistoryQueryHandler<GynHistory>>();
            _loggerMock = new Mock<ILogger<GynHistoryController>>();
            _localizerMock = new Mock<IStringLocalizer<EncounterNotesServiceStrings>>();

            // Setup localizer mock
            _localizerMock.Setup(x => x["GynHistoryNotFound"]).Returns(new LocalizedString("GynHistoryNotFound", "GYN history not found."));
            _localizerMock.Setup(x => x["ErrorFetchingGynHistory"]).Returns(new LocalizedString("ErrorFetchingGynHistory", "Error fetching GYN history."));
            _localizerMock.Setup(x => x["ErrorFetchingGynHistories"]).Returns(new LocalizedString("ErrorFetchingGynHistories", "Error fetching GYN histories."));
            _localizerMock.Setup(x => x["ErrorDeletingGynHistory"]).Returns(new LocalizedString("ErrorDeletingGynHistory", "Error deleting GYN history."));
            _localizerMock.Setup(x => x["ErrorUpdatingGynHistory"]).Returns(new LocalizedString("ErrorUpdatingGynHistory", "Error updating GYN history."));
            _localizerMock.Setup(x => x["ErrorCreatingGynHistory"]).Returns(new LocalizedString("ErrorCreatingGynHistory", "Error creating GYN history."));
            _localizerMock.Setup(x => x["ShardAdded"]).Returns(new LocalizedString("ShardAdded", "Shard added."));
            _localizerMock.Setup(x => x["500"]).Returns(new LocalizedString("500", "500"));

            _testGynHistories = new List<GynHistory>
            {
                new GynHistory {
                    gynId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Symptoms = "Irregular periods",
                    Notes = "Patient reports irregular menstrual cycles for the past 6 months",
                    DateOfHistory = DateTime.Now.AddDays(-30),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Subscription = _isSubscription,
                    IsDeleted = false
                },
                new GynHistory {
                    gynId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(),
                    Symptoms = "Pelvic pain",
                    Notes = "Patient reports pelvic pain during menstruation",
                    DateOfHistory = DateTime.Now.AddDays(-15),
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Subscription = _isSubscription,
                    IsDeleted = false
                }
            };

            _controller = new GynHistoryController(
                _gynHistoryQueryHandlerMock.Object,
                _gynHistoryCommandHandlerMock.Object,
                _loggerMock.Object,
                _localizerMock.Object
            );
        }

        [Test]
        public async Task GetByPatientIdAsync_ReturnsGynHistories_WhenHistoriesExist()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var patientHistories = _testGynHistories.Select(h => { h.PatientId = patientId; return h; }).ToList();
            _gynHistoryQueryHandlerMock.Setup(x => x.GetByPatientIdAsync(patientId, _orgId, _isSubscription))
                .ReturnsAsync(patientHistories);

            // Act
            var result = await _controller.GetByPatientIdAsync(patientId, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedHistories = okResult.Value as IEnumerable<GynHistory>;
            returnedHistories.Should().NotBeNull();
            returnedHistories.Should().BeEquivalentTo(patientHistories);
        }

        [Test]
        public async Task GetByPatientIdAsync_ReturnsBadRequest_WhenExceptionOccurs()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            _gynHistoryQueryHandlerMock.Setup(x => x.GetByPatientIdAsync(patientId, _orgId, _isSubscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetByPatientIdAsync(patientId, _orgId, _isSubscription);

            // Assert
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult.Should().NotBeNull();
            badRequestResult.StatusCode.Should().Be(400);
            badRequestResult.Value.Should().Be(_localizerMock.Object["ErrorFetchingGynHistories"]);
        }

        [Test]
        public async Task GetGynHistoryById_ReturnsHistory_WhenHistoryExists()
        {
            // Arrange
            var historyId = _testGynHistories[0].gynId;
            _gynHistoryQueryHandlerMock.Setup(x => x.GetByIdAsync(historyId, _orgId, _isSubscription))
                .ReturnsAsync(_testGynHistories[0]);

            // Act
            var result = await _controller.GetGynHistoryById(historyId, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            var returnedHistory = okResult.Value as GynHistory;
            returnedHistory.Should().NotBeNull();
            returnedHistory.Should().BeEquivalentTo(_testGynHistories[0]);
        }

        [Test]
        public async Task GetGynHistoryById_ReturnsNotFound_WhenHistoryDoesNotExist()
        {
            // Arrange
            var historyId = Guid.NewGuid();
            _gynHistoryQueryHandlerMock.Setup(x => x.GetByIdAsync(historyId, _orgId, _isSubscription))
                .ReturnsAsync((GynHistory)null);

            // Act
            var result = await _controller.GetGynHistoryById(historyId, _orgId, _isSubscription);

            // Assert
            var notFoundResult = result as NotFoundObjectResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
            notFoundResult.Value.Should().Be(_localizerMock.Object["GynHistoryNotFound"]);
        }

        [Test]
        public async Task CreateGynHistory_ReturnsCreatedAtAction_WhenCreationSucceeds()
        {
            // Arrange
            var newHistory = new GynHistory
            {
                PatientId = Guid.NewGuid(),
                Symptoms = "Abnormal bleeding",
                Notes = "Patient reports abnormal bleeding between periods",
                DateOfHistory = DateTime.Now,
                OrganizationId = _orgId,
                PcpId = Guid.NewGuid()
            };

            // Act
            var result = await _controller.CreateGynHistory(newHistory, _orgId, _isSubscription);

            // Assert
            var createdAtActionResult = result as CreatedAtActionResult;
            createdAtActionResult.Should().NotBeNull();
            createdAtActionResult.StatusCode.Should().Be(201);
            createdAtActionResult.ActionName.Should().Be(nameof(GynHistoryController.GetGynHistoryById));

            var returnedHistory = createdAtActionResult.Value as GynHistory;
            returnedHistory.Should().NotBeNull();
            returnedHistory.PatientId.Should().Be(newHistory.PatientId);
            returnedHistory.Symptoms.Should().Be(newHistory.Symptoms);
            returnedHistory.Notes.Should().Be(newHistory.Notes);

            // Verify the command handler was called
            _gynHistoryCommandHandlerMock.Verify(x => x.AddAsync(It.IsAny<List<GynHistory>>(), _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task UpdateGynHistory_ReturnsOk_WhenUpdateSucceeds()
        {
            // Arrange
            var existingHistory = _testGynHistories[0];
            var historyToUpdate = new GynHistory
            {
                gynId = existingHistory.gynId,
                PatientId = existingHistory.PatientId,
                Symptoms = "Updated symptoms",
                Notes = "Updated notes",
                DateOfHistory = existingHistory.DateOfHistory,
                OrganizationId = existingHistory.OrganizationId,
                PcpId = existingHistory.PcpId
            };

            _gynHistoryQueryHandlerMock.Setup(x => x.GetByIdAsync(existingHistory.gynId, _orgId, _isSubscription))
                .ReturnsAsync(existingHistory);

            // Act
            var result = await _controller.UpdateGynHistory(existingHistory.gynId, historyToUpdate, _orgId, _isSubscription);

            // Assert
            var okResult = result as OkObjectResult;
            okResult.Should().NotBeNull();
            okResult.StatusCode.Should().Be(200);

            // Verify the command handler was called
            _gynHistoryCommandHandlerMock.Verify(x => x.UpdateAsync(It.IsAny<GynHistory>(), _orgId, _isSubscription), Times.Once);
        }

        [Test]
        public async Task DeleteGynHistory_ReturnsNoContent_WhenDeletionSucceeds()
        {
            // Arrange
            var historyId = _testGynHistories[0].gynId;
            _gynHistoryQueryHandlerMock.Setup(x => x.GetByIdAsync(historyId, _orgId, _isSubscription))
                .ReturnsAsync(_testGynHistories[0]);

            // Act
            var result = await _controller.DeleteGynHistory(historyId, _orgId, _isSubscription);

            // Assert
            var noContentResult = result as NoContentResult;
            noContentResult.Should().NotBeNull();
            noContentResult.StatusCode.Should().Be(204);

            // Verify the command handler was called
            _gynHistoryCommandHandlerMock.Verify(x => x.DeleteByIdAsync(historyId, _orgId, _isSubscription), Times.Once);
        }


    }
}
