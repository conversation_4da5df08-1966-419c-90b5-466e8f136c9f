﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceBusinessLayer
{
    public interface IEmployerCommandHandler<T>
    {

        Task AddEmployerAsync(List<T> employer, Guid orgId, bool Subscription);
        Task UpdateEmployerAsync(T employer, Guid orgId, bool Subscription);
        Task DeleteEmployerAsync(Guid id, Guid orgId, bool Subscription);
    }
}