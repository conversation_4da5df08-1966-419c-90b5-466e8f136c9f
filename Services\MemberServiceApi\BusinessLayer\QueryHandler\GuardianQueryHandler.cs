﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class GuardianQueryHandler : IGuardianQueryHandler<Guardian>
    {
        private readonly IUnitOfWork _unitOfWork;

        public GuardianQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<Guardian> GetGuardianByIdAsync(Guid id, Guid OrgID, bool Subscription)
        {
            var Guardian = await _unitOfWork.GuardianRepository.GetByIdAsync(id, OrgID, Subscription);
            return Guardian;
        }



        public async Task<List<Guardian>> GetAllGuardianAsync(Guid orgId, bool Subscription)
        {
            var Guardians = await _unitOfWork.GuardianRepository.GetAllAsync(orgId, Subscription);
            return Guardians.ToList();
        }
    }
}
