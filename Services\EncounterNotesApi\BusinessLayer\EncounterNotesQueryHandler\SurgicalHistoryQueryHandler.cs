﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class SurgicalHistoryQueryHandler : ISurgicalHistoryQueryHandler<SurgicalHistory>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public SurgicalHistoryQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<SurgicalHistory>> GetAllSurgeriesById(Guid id, Guid OrgId, bool Subscription)
        {
            var surgeries = await _unitOfWork.SurgicalRepository.GetAllSurgeriesAsync(OrgId, Subscription);
            return surgeries.Where(SurHistory => SurHistory.PatientId == id);
        }

        public async Task<IEnumerable<SurgicalHistory>> GetSurgeryByIdAndIsActive(Guid id, Guid OrgId, bool Subscription)
        {
            var surgeries = await _unitOfWork.SurgicalRepository.GetAsync(OrgId, Subscription);
            return surgeries.Where(SurHistory => SurHistory.PatientId == id && SurHistory.IsActive == true);
        }
    }
}
