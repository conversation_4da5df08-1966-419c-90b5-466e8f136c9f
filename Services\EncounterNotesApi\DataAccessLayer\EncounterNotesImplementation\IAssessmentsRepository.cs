﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IAssessmentsRepository:IShardGenericRepository<AssessmentsData>
    {
        Task<IEnumerable<AssessmentsData>> GetAllAssessmentsAsync(Guid OrgId, bool Subscription);
        Task<List<string>> GetAssessmentRelatedMedications(Guid PatientId, Guid OrgId, bool Subscription);
        Task<List<AssessmentsData>> GetListOfAssesmentsThroughCheifComplaintId(Guid CheifComplaintId, Guid OrgId, bool Subscription);
    }
}
