﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public interface IAllergyRepository : IShardGenericRepository<Allergy>
    {
        Task<IEnumerable<Allergy>> GetAllAllergyAsync(Guid OrgId, bool Subscription);
        Task UpdateRangeAsync(List<Allergy> allergies, Guid OrgId, bool Subscription);
        Task UpdateAsync(Allergy allergy, Guid OrgId, bool Subscription);
        Task AddAsync(Allergy allergy, Guid OrgId, bool Subscription);
        Task<Allergy> GetByIdAsync(Guid patientId, Guid medicineId, Guid OrgId, bool Subscription);
    }
}
