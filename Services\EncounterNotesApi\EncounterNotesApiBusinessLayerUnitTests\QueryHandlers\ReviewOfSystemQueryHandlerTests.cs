using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class ReviewOfSystemQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IReviewOfSystemRepository> _reviewOfSystemRepositoryMock;
        private ReviewOfSystemQueryHandler _reviewOfSystemQueryHandler;
        private Guid _orgId;
        private bool _subscription;
        private Guid _patientId;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _reviewOfSystemRepositoryMock = new Mock<IReviewOfSystemRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;
            _patientId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.ReviewOfSystemRepository).Returns(_reviewOfSystemRepositoryMock.Object);
            _reviewOfSystemQueryHandler = new ReviewOfSystemQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetAllReviewOfSystemsById_ShouldReturnAllReviewOfSystemsForPatient()
        {
            // Arrange
            var reviewOfSystems = new List<ReviewOfSystem>
            {
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    UpdatedDate = DateTime.Now.AddDays(-25),
                    Decription = "Comprehensive review of systems",
                    IsActive = true,
                    RunnyNose = true,
                    Congestion = true,
                    ShortnessOfBreath = false,
                    ChestPain = false,
                    ItchyEyes = true,
                    Subscription = _subscription
                },
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-60),
                    UpdatedDate = DateTime.Now.AddDays(-55),
                    Decription = "Follow-up review of systems",
                    IsActive = false,
                    RunnyNose = false,
                    Congestion = false,
                    ShortnessOfBreath = true,
                    ChestPain = true,
                    ItchyEyes = false,
                    Subscription = _subscription
                },
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    CreatedDate = DateTime.Now.AddDays(-45),
                    UpdatedDate = DateTime.Now.AddDays(-40),
                    Decription = "Initial review of systems",
                    IsActive = true,
                    RunnyNose = true,
                    Congestion = true,
                    ShortnessOfBreath = true,
                    ChestPain = false,
                    ItchyEyes = true,
                    Subscription = _subscription
                }
            };

            _reviewOfSystemRepositoryMock.Setup(r => r.GetAllReviewOfSystemsAsync(_orgId, _subscription))
                .ReturnsAsync(reviewOfSystems);

            // Act
            var result = await _reviewOfSystemQueryHandler.GetAllReviewOfSystemsById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(r => r.PatientId == _patientId).Should().BeTrue();
        }

        [Test]
        public async Task GetAllReviewOfSystemsById_WithNoReviewOfSystems_ShouldReturnEmptyCollection()
        {
            // Arrange
            var reviewOfSystems = new List<ReviewOfSystem>();

            _reviewOfSystemRepositoryMock.Setup(r => r.GetAllReviewOfSystemsAsync(_orgId, _subscription))
                .ReturnsAsync(reviewOfSystems);

            // Act
            var result = await _reviewOfSystemQueryHandler.GetAllReviewOfSystemsById(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetReviewOfSystemByIdAndIsActive_ShouldReturnActiveReviewOfSystemsForPatient()
        {
            // Arrange
            var reviewOfSystems = new List<ReviewOfSystem>
            {
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Decription = "Comprehensive review of systems",
                    IsActive = true,
                    RunnyNose = true,
                    Congestion = true,
                    ShortnessOfBreath = false,
                    ChestPain = false,
                    ItchyEyes = true,
                    Subscription = _subscription
                },
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Decription = "Follow-up review of systems",
                    IsActive = false, // Inactive
                    RunnyNose = false,
                    Congestion = false,
                    ShortnessOfBreath = true,
                    ChestPain = true,
                    ItchyEyes = false,
                    Subscription = _subscription
                },
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Decription = "Initial review of systems",
                    IsActive = true,
                    RunnyNose = true,
                    Congestion = true,
                    ShortnessOfBreath = true,
                    ChestPain = false,
                    ItchyEyes = true,
                    Subscription = _subscription
                }
            };

            _reviewOfSystemRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(reviewOfSystems);

            // Act
            var result = await _reviewOfSystemQueryHandler.GetReviewOfSystemByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.All(r => r.PatientId == _patientId && r.IsActive == true).Should().BeTrue();
        }

        [Test]
        public async Task GetReviewOfSystemByIdAndIsActive_WithNoActiveReviewOfSystems_ShouldReturnEmptyCollection()
        {
            // Arrange
            var reviewOfSystems = new List<ReviewOfSystem>
            {
                new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PcpId = Guid.NewGuid(),
                    Decription = "Follow-up review of systems",
                    IsActive = false, // Inactive
                    RunnyNose = false,
                    Congestion = false,
                    ShortnessOfBreath = true,
                    ChestPain = true,
                    ItchyEyes = false,
                    Subscription = _subscription
                }
            };

            _reviewOfSystemRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(reviewOfSystems);

            // Act
            var result = await _reviewOfSystemQueryHandler.GetReviewOfSystemByIdAndIsActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
