﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterDataAccessLayerResource;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using Interfaces.ShardManagement;
using Microsoft.Extensions.Logging;

namespace EncounterNotesDataAccessLayer.EncounterNotesImplementation
{
    public class VisionExaminationRepository : ShardGenericRepository<VisionRx>, IVisionExaminationRepository
    {
        private readonly RecordDatabaseContext _context;
        private readonly ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> _shardMapManagerService;
        private readonly IStringLocalizer<EncounterDataAccessLayer> _localizer;
        private readonly ILogger<RecordDatabaseContext> _logger;
        private readonly string _shardMapName;
        public VisionExaminationRepository(RecordDatabaseContext context, ShardMapManagerService<RecordDatabaseContext, EncounterDataAccessLayer> shardMapManagerService, IStringLocalizer<EncounterDataAccessLayer> localizer, ILogger<RecordDatabaseContext> logger) : base(context, shardMapManagerService, localizer, logger)
        {
            _context = context;
            _shardMapManagerService = shardMapManagerService;
            _localizer = localizer;
            _logger = logger;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }
        public async Task<List<VisionRx>> GetVisionExaminationById(Guid recordId, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return new List<VisionRx>();
            return await context.VisionExamination
                .Where(wt => wt.PatientId == recordId)
                .ToListAsync();
        }

        public async Task DeleteVisionExaminationByIdAsync(Guid examinationId, Guid orgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, orgId, _shardMapName);
            if (context == null)
                return;

            var record = await context.Set<VisionRx>().FindAsync(examinationId);
            if (record == null)
                return;

            record.IsActive = false;
            context.Set<VisionRx>().Update(record);
            await context.SaveChangesAsync();
        }

    }
}

