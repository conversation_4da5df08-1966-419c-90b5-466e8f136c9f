﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface IRecordQueryHandler<TText>
    {
        Task<IEnumerable<TText>> GetRecord(Guid OrgId, bool Subscription);
        Task<TText> GetRecordById(Guid id, Guid OrgId, bool Subscription);
        Task<IEnumerable<Record>> GetRecordByPCPId(Guid pcpId, Guid OrgId, bool Subscription);
        Task<IEnumerable<Record>> GetRecordByPatientId(Guid patientId, Guid OrgId, bool Subscription);
    }
}
