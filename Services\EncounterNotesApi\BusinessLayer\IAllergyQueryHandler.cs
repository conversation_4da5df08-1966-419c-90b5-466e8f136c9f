﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IAllergyQueryHandler<TText>
    {
        Task<IEnumerable<Allergy>> GetAllergyByIdAndIsActive(Guid id, Guid OrgId, bool Subscription);
        Task<IEnumerable<Allergy>> GetAllAllergyById(Guid id, Guid OrgId, bool Subscription);
    }
}
