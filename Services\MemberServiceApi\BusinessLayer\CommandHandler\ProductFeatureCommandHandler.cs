﻿using System;
using System.Threading.Tasks;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;

namespace MemberServiceBusinessLayer.CommandHandler
{
    public class ProductFeatureCommandHandler : IProductFeatureCommandHandler<ProductFeature>
    {
        private readonly IUnitOfWork _unitOfWork;

        public ProductFeatureCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task AddProductFeatureAsync(ProductFeature feature)
        {
            if (feature == null)
                throw new ArgumentNullException(nameof(feature));

            await _unitOfWork.ProductFeatureRepository.AddAsync(feature);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateProductFeatureAsync(ProductFeature feature)
        {
            if (feature == null)
                throw new ArgumentNullException(nameof(feature));

            await _unitOfWork.ProductFeatureRepository.UpdateAsync(feature);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteProductFeatureAsync(Guid id)
        {
            await _unitOfWork.ProductFeatureRepository.DeleteByIdAsync(id);
            await _unitOfWork.SaveAsync();
        }
    }
}
