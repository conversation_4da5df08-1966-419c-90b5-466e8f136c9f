﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;
using Microsoft.Extensions.Configuration;

namespace EncounterNotesBusinessLayer.EncounterNotesQueryHandler
{
    public class ReviewOfSystemQueryHandler : IReviewOfSystemQueryHandler<ReviewOfSystem>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;

        public ReviewOfSystemQueryHandler(IConfiguration configuration, IUnitOfWork unitOfWork)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<ReviewOfSystem>> GetAllReviewOfSystemsById(Guid id, Guid OrgId, bool Subscription)
        {
            var reviewOfSystems = await _unitOfWork.ReviewOfSystemRepository.GetAllReviewOfSystemsAsync(OrgId, Subscription);
            return reviewOfSystems.Where(review => review.PatientId == id);
        }

        public async Task<IEnumerable<ReviewOfSystem>> GetReviewOfSystemByIdAndIsActive(Guid id, Guid OrgId, bool Subscription)
        {
            var reviewOfSystems = await _unitOfWork.ReviewOfSystemRepository.GetAsync(OrgId, Subscription);
            return reviewOfSystems.Where(review => review.PatientId == id && review.IsActive == true);
        }
    }
}
