﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;

namespace MemberServiceBusinessLayer
{
    public interface IRolesCommandHandler<T>
    {
        Task AddRoleAsync(List<T> roles, Guid OrgID, bool Subscription);
        Task UpdateRoleAsync(T role, Guid OrgID, bool Subscription);
        Task DeleteRoleAsync(Guid id, Guid OrgID, bool Subscription);
    }
}
