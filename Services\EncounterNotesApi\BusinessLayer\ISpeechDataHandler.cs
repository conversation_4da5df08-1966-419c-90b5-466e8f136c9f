﻿using EncounterNotesContracts;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface ISpeechDataHandler<TText>
    {
        Task<Guid?> Handle(SpeechRequest speechRequest, Guid orgId, bool isSubscription);
        Task<string> UploadToBlobAsync(MemoryStream fileStream, string fileName);
        Task ProcessSpeech(SpeechRequest speechRequest, Kernel kernel, Guid orgId, bool isSubscription);
        Task<string> ExtractFromPrompt(string text, string prompt);
    }
}