﻿using EncounterNotesContracts;
using ShardModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer
{
    public interface ITemplatesCommandHandler<TText>
    {
        Task DeleteTemplatesByEntity(Templates templates, Guid OrgId, bool Subscription);
        Task DeleteTemplatesById(Guid id, Guid OrgId, bool Subscription);
        Task AddTemplates(List<TText> templates, Guid OrgId, bool Subscription);
        Task UpdateTemplates(List<Templates> templates, Guid OrgId, bool Subscription);
    }
}