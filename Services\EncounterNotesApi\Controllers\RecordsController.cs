﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesBusinessLayer;
using EncounterNotesContracts;
using EncounterNotesDataAccessLayer.EncounterNotesContext;
using EncounterNotesService.EncounterNotesServiceResources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using ShardModels;

namespace EncounterNotesService.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class RecordsController : ControllerBase
    {
        private readonly IRecordCommandHandler<Record> _recordDataHandler;
        private readonly IRecordQueryHandler<Record> _recordQueryHandler;
        private readonly ILogger<RecordsController> _logger;
        private readonly IStringLocalizer<EncounterNotesServiceStrings> _localizer;


        public RecordsController(
            IRecordCommandHandler<Record> dataHandler,
            IRecordQueryHandler<Record> queryHandler,
            ILogger<RecordsController> logger,
            IStringLocalizer<EncounterNotesServiceStrings> localizer
            )
        {
            _recordDataHandler = dataHandler;
            _recordQueryHandler = queryHandler;
            _logger = logger;
            _localizer = localizer;
        }
        [HttpGet]
        [Route("{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<Record>>> Get(Guid orgId, bool isSubscription)
        {
            ActionResult result;
            try
            {
                var records = await _recordQueryHandler.GetRecord(orgId, isSubscription);
                result = Ok(records);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        [HttpGet("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<Record>> GetById(Guid id, Guid orgId, bool isSubscription)
        {
            ActionResult result;
            try
            {
                var record = await _recordQueryHandler.GetRecordById(id, orgId, isSubscription);
                result = record != null ? Ok(record) : NotFound(_localizer["RecordNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        [HttpGet("ByPCP/{pcpId:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<Record>>> GetByPCPId(Guid pcpId, Guid orgId, bool isSubscription)
        {
            try
            {
                var records = await _recordQueryHandler.GetRecordByPCPId(pcpId, orgId, isSubscription);
                return Ok(records ?? new List<Record>()); // Return an empty list instead of null
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
        }

        [HttpGet("PatientId/{patientId:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<ActionResult<IEnumerable<Record>>> GetByPatientId(Guid patientId, Guid orgId, bool isSubscription)
            {
            ActionResult result;
            try
            {
                var records = await _recordQueryHandler.GetRecordByPatientId(patientId, orgId, isSubscription);
                result = Ok(records);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["GetLogError"]);
            }
            return result;
        }

        [HttpPut("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> UpdateById([FromRoute] Guid id, [FromBody] Record record, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            if (record == null || record.Id != id)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _recordDataHandler.UpdateRecord(record, orgId, isSubscription);
                    result = Ok(_localizer["UpdateSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["UpdateLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["UpdateLogError"]);
                }
            }
            return result;
        }

        [HttpDelete("{id:guid}/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> DeleteById(Guid id, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            try
            {
                await _recordDataHandler.DeleteRecordById(id, orgId, isSubscription);
                result = Ok(_localizer["SuccessfulDeletion"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
            }
            return result;
        }

        [HttpDelete]
        [Route("{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> DeleteByEntity([FromBody] Record record, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            if (record == null)
            {
                result = BadRequest(_localizer["InvalidRecord"]);
            }
            else
            {
                try
                {
                    await _recordDataHandler.DeleteRecordByEntity(record, orgId, isSubscription);
                    result = Ok(_localizer["DeleteSuccessful"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["DeleteLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["DeleteLogError"]);
                }
            }
            return result;
        }

        [HttpPost]
        [Route("EncounterNote/{orgId:guid}/{isSubscription}")]
        public async Task<IActionResult> EncounterNote([FromBody] List<RecordDTO> encounterNotesDto, Guid orgId, bool isSubscription)
        {
            IActionResult result;
            if (encounterNotesDto == null || !encounterNotesDto.Any())
            {
                result = BadRequest(_localizer["NoLicense"]);
            }
            else
            {
                try
                {
                    var encounterNotes = encounterNotesDto.Select(dto => new Record
                    {
                        Id = dto.Id,
                        PatientId=dto.PatientId,
                        PatientName = dto.PatientName,
                       
                        DateTime = dto.DateTime,
                        Notes = dto.Notes,
                        OrganizationId = dto.OrganizationId,
                        PCPId = dto.PCPId,
                        Transcription = dto.Transcription,
                        WordTimings = null,
                        isEditable=true
                    }).ToList();

                    await _recordDataHandler.AddRecord(encounterNotes, orgId, isSubscription);
                    var firstRecordId = encounterNotes.FirstOrDefault()?.Id;
                    result = Ok(new { Message = _localizer["SuccessfulEncounterNote"], FirstRecordId = firstRecordId });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["PostLogError"]);
                    result = StatusCode(int.Parse(_localizer["500"]), _localizer["PostLogError"]);
                }
            }
            return result;
        }

    }
}

