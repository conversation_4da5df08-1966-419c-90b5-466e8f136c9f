﻿using EncounterNotesContracts;
using ShardModels;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EncounterNotesBusinessLayer.EncounterNotesCommandHandler
{
    public interface IChiefComplaintCommandHandler<T> where T : class
    {
        Task AddChiefComplaint(IEnumerable<T> complaints, Guid OrgId, bool Subscription);

        Task UpdateChiefComplaint(T complaint, Guid OrgId, bool Subscription);

        Task UpdateChiefComplaintListAsync(IEnumerable<ChiefComplaint> complaints, Guid OrgId, bool Subscription);

        Task DeleteChiefComplaintById(Guid id, Guid OrgId, bool Subscription);

        Task DeleteChiefComplaintByEntity(T complaint, Guid OrgId, bool Subscription);

    }
}
