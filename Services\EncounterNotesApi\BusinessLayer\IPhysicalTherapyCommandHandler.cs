﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface IPhysicalTherapyCommandHandler<TText>
    {
        Task DeletePhysicalTherapyByEntity(PhysicalTherapyData _PhysicalTherapy, Guid OrgId, bool Subscription);
        Task DeletePhysicalTherapyById(Guid id, Guid OrgId, bool Subscription);
        Task AddPhysicalTherapy(List<TText> texts, Guid OrgId, bool Subscription);
        Task UpdatePhysicalTherapy(PhysicalTherapyData _PhysicalTherapy, Guid OrgId, bool Subscription);
        Task UpdatePhysicalTherapyList(List<PhysicalTherapyData> _PhysicalTherapy, Guid OrgId, bool Subscription);
    }
}
