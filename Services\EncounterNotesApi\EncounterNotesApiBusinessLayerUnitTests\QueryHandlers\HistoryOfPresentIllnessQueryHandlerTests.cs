using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using System.Linq;
using EncounterNotesContracts;
using EncounterNotesBusinessLayer.EncounterNotesQueryHandler;
using EncounterNotesDataAccessLayer.EncounterNotesImplementation;

namespace EncounterNotesApiBusinessLayerTestCases.QueryHandlers
{
    [TestFixture]
    public class HistoryOfPresentIllnessQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<IHistoryOfPresentIllnessRepository> _historyOfPresentIllnessRepositoryMock;
        private HistoryOfPresentIllnessQueryHandler _historyOfPresentIllnessQueryHandler;
        private Guid _orgId;
        private bool _subscription;
        private Guid _patientId;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _historyOfPresentIllnessRepositoryMock = new Mock<IHistoryOfPresentIllnessRepository>();
            _orgId = Guid.NewGuid();
            _subscription = false;
            _patientId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.HistoryOfPresentIllnessRepository).Returns(_historyOfPresentIllnessRepositoryMock.Object);
            _historyOfPresentIllnessQueryHandler = new HistoryOfPresentIllnessQueryHandler(_configurationMock.Object, _unitOfWorkMock.Object);
        }

        [Test]
        public async Task GetHistoryOfPresentIllnessByPatientId_ShouldReturnAllHPIRecordsForPatient()
        {
            // Arrange
            var hpiRecords = new List<HistoryOfPresentIllness>
            {
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Location = "Chest",
                    Duration = "3 days",
                    Severity = "Moderate",
                    Symptoms = "Chest pain, shortness of breath",
                    StartDate = DateTime.Now.AddDays(-3),
                    EndDate = null,
                    Notes = "Pain worsens with exertion",
                    IsActive = true,
                    Subscription = _subscription
                },
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Location = "Head",
                    Duration = "1 week",
                    Severity = "Mild",
                    Symptoms = "Headache, dizziness",
                    StartDate = DateTime.Now.AddDays(-7),
                    EndDate = DateTime.Now.AddDays(-2),
                    Notes = "Improves with rest",
                    IsActive = false,
                    Subscription = _subscription
                },
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Location = "Abdomen",
                    Duration = "2 days",
                    Severity = "Severe",
                    Symptoms = "Abdominal pain, nausea",
                    StartDate = DateTime.Now.AddDays(-2),
                    EndDate = null,
                    Notes = "Pain is constant",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _historyOfPresentIllnessRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(hpiRecords);

            // Act
            var result = await _historyOfPresentIllnessQueryHandler.GetHistoryOfPresentIllnessByPatientId(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.All(h => h.PatientId == _patientId).Should().BeTrue();
        }

        [Test]
        public async Task GetHistoryOfPresentIllnessByPatientId_WithNoHPIRecords_ShouldReturnEmptyCollection()
        {
            // Arrange
            var hpiRecords = new List<HistoryOfPresentIllness>();

            _historyOfPresentIllnessRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(hpiRecords);

            // Act
            var result = await _historyOfPresentIllnessQueryHandler.GetHistoryOfPresentIllnessByPatientId(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Test]
        public async Task GetHistoryOfPresentIllnessByPatientIdAndActive_ShouldReturnActiveHPIRecordsForPatient()
        {
            // Arrange
            var hpiRecords = new List<HistoryOfPresentIllness>
            {
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Location = "Chest",
                    Duration = "3 days",
                    Severity = "Moderate",
                    Symptoms = "Chest pain, shortness of breath",
                    IsActive = true,
                    Subscription = _subscription
                },
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Location = "Head",
                    Duration = "1 week",
                    Severity = "Mild",
                    Symptoms = "Headache, dizziness",
                    IsActive = false, // Inactive
                    Subscription = _subscription
                },
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = Guid.NewGuid(), // Different patient
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Location = "Abdomen",
                    Duration = "2 days",
                    Severity = "Severe",
                    Symptoms = "Abdominal pain, nausea",
                    IsActive = true,
                    Subscription = _subscription
                }
            };

            _historyOfPresentIllnessRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(hpiRecords);

            // Act
            var result = await _historyOfPresentIllnessQueryHandler.GetHistoryOfPresentIllnessByPatientIdAndActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.All(h => h.PatientId == _patientId && h.IsActive).Should().BeTrue();
        }

        [Test]
        public async Task GetHistoryOfPresentIllnessByPatientIdAndActive_WithNoActiveHPIRecords_ShouldReturnEmptyCollection()
        {
            // Arrange
            var hpiRecords = new List<HistoryOfPresentIllness>
            {
                new HistoryOfPresentIllness
                {
                    Id = Guid.NewGuid(),
                    PatientId = _patientId,
                    OrganizationId = _orgId,
                    PCPId = Guid.NewGuid(),
                    Location = "Head",
                    Duration = "1 week",
                    Severity = "Mild",
                    Symptoms = "Headache, dizziness",
                    IsActive = false, // Inactive
                    Subscription = _subscription
                }
            };

            _historyOfPresentIllnessRepositoryMock.Setup(r => r.GetAsync(_orgId, _subscription))
                .ReturnsAsync(hpiRecords);

            // Act
            var result = await _historyOfPresentIllnessQueryHandler.GetHistoryOfPresentIllnessByPatientIdAndActive(_patientId, _orgId, _subscription);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }
    }
}
