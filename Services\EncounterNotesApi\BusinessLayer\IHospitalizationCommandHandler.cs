﻿using EncounterNotesContracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ShardModels;

namespace EncounterNotesBusinessLayer
{
    public interface IHospitalizationRecordCommandHandler<TText>
    {
        Task DeleteHospitalizationRecordById(Guid id, Guid OrgId, bool Subscription);
        Task<bool> UpdateHospitalizationRecordList(List<HospitalizationRecord> HospitalizationRecordList, Guid OrgId, bool Subscription);
        Task AddHospitalizationRecord(List<TText> hospitalizationRecordList, Guid OrgId, bool Subscription);
        Task UpdateHospitalizationRecord(HospitalizationRecord hospitalizationRecord, Guid OrgId, bool Subscription);
    }
}
