﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EncounterNotesContracts;

namespace EncounterNotesBusinessLayer
{
    public interface IDiagnosticImagingPageQueryHandler<T>
    {
        Task<List<DiagnosticImagingDTO>> GetDiagnosticImagingByIdAndIsActive(Guid id, Guid OrgId, bool Subscription);
        Task<List<T>> GetDiagnosticImagingById(Guid id, Guid OrgId, bool Subscription);
    }
}
